def project_name = 'fitquid-cms'
def production_branch = 'master'
def development_branch = 'development'
def staging_branch = 'Preprod'
def ip_address = '**************'
def user = "ubuntu"
if (env.BRANCH_NAME == "${development_branch}")
{
  agentName = 'mover-inventory-live'
  ip_address = "************"
  user = "ubuntu"
  deploy_path = "/usr/share/nginx/development/"
}
else if (env.BRANCH_NAME == "${staging_branch}")
{
  agentName = 'mover-inventory-live'
  ip_address = "0.0.0.0"
  user = "ubuntu"
  deploy_path = "/usr/share/nginx/staging/"
}
else if (env.BRANCH_NAME == "${production_branch}")
{
  agentName = 'mover-inventory-live'
  ip_address = "0.0.0.0"
  user = "ubuntu"
  deploy_path = "/usr/share/nginx/html"
}


pipeline {
    agent any

    options {
        buildDiscarder(logRotator(numToKeepStr: "7"))
    }
    
    stages {
        stage("Build")
        {
        	agent {
		        docker {
		            image "node:12-alpine"
                    args '-u root:root'
		        }
		    }
            steps
            {   
                sh "npm install"
                sh "mv .env-${env.BRANCH_NAME} .env || ls -la"
                sh "mv .env.${env.BRANCH_NAME} .env || ls -la"
                sh "npm run --silent build"
                stash includes: 'build/**/*', name: 'BUILD'
                cleanWs()
                    
            }
        }

        stage("Deploy")
        {
            steps
            {
                script {
                    unstash 'BUILD'
                    sshagent ( ["${agentName}"]) {
                    sh 'ls -la build'
                    sh """
                    ssh -o StrictHostKeyChecking=no ${user}@${ip_address} "sudo mkdir -p ${deploy_path} || exit 0; sudo chown -R ${user}:${user} ${deploy_path}"
                    """
                    sh "scp -r -o StrictHostKeyChecking=no  build/* ${user}@${ip_address}:${deploy_path}"
                   
                    }
                    
                }
            }
        }
    }
    post { 
        always { 
                cleanWs()
            }
        }
}

