image: node:14-alpine

stages:
 - build
 - deploy

cache:
  paths:
    - node_modules/

variables:
 PROJECT: "mover-inventory-cms"

build:
 stage: build
 environment:
    name: $CI_COMMIT_REF_SLUG
 variables:
    CI: "false"
 cache:
    key: $CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
 before_script:
      - apk add git curl
 script:
    #- if [ -z "$TARGET_PATH" ];then cp ${ENV_FILE:-.env.$CI_COMMIT_REF_SLUG} .env;else echo "read env from s3";fi
    - cp ${ENV_FILE:-.env.$CI_COMMIT_REF_SLUG} .env || ls -la
    - rm -rf .env.*
    - npm install
    - npm run build
 artifacts:
    paths: 
     - build/*
 only:
    - development
 
deploy_dev:
 stage: deploy
 environment:
  name: $CI_COMMIT_REF_NAME
  url: "https://staging.moverinventory-cms.movegistics.com"
 variables:
  BUILD_PATH: build
  USER: "ubuntu"
  DEPLOY_PATH: "/usr/share/nginx/development"
  IP_ADDRESS: "************"
 image: 
    name: gotechnies/alpine-ssh
 before_script:
    - apk update && apk add --no-cache openssh-client rsync
 script:
  - mkdir ~/.ssh
  - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
  - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "sudo mkdir -p $DEPLOY_PATH || exit 0; sudo chown -R $USER:$USER $DEPLOY_PATH"
  - rsync -avrx -e 'ssh -o StrictHostKeychecking=no' --delete ./$BUILD_PATH/ ${USER}@${IP_ADDRESS}:$DEPLOY_PATH
 only:
  - development

deploy_master:
 stage: deploy
 environment:
  name: $CI_COMMIT_REF_NAME  
  url: "https://moverinventory-cms.movegistics.com"
 variables:
  BUILD_PATH: build
  USER: "ubuntu"
  DEPLOY_PATH: "/var/www/html"
  IP_ADDRESS: "***********"
 image: 
    name: gotechnies/alpine-ssh
 before_script:
    - apk update && apk add --no-cache openssh-client rsync
 script:
  - mkdir ~/.ssh
    - cp $SSH_PRIVATE_KEY ~/.ssh/id_rsa && chmod 400 ~/.ssh/id_rsa
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "sudo mkdir -p $DEPLOY_PATH || exit 0; sudo chown -R $USER:$USER $DEPLOY_PATH"
    - rsync -avrx -e 'ssh -o StrictHostKeychecking=no' --delete ./$BUILD_PATH/ ${USER}@${IP_ADDRESS}:$DEPLOY_PATH 
    - if [[ $CI_COMMIT_REF_SLUG == "development" ]]; then \
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "curl -sSLk https://${CI_SERVER_HOST}/-/snippets/2/raw | sed 's%__PROJECT_NAME__%${PROJECT}%g' > ${NGINX_PATH}/${PROJECT}.conf";\
    - ssh -o StrictHostKeyChecking=no ${USER}@${IP_ADDRESS} "if docker exec web nginx -t;then docker exec web nginx -s reload;fi";fi
 only:
  - master
