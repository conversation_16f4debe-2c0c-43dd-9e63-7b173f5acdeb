import { Button, Form, Icon, Input, message, Select, Spin, Tag, Upload, List } from "antd";
import React from "react";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';
import PlacesAutocomplete, {
	geocodeByAddress,
} from 'react-places-autocomplete';
import { GOOGLE_API_KEY } from "../../static/data/constants";


const { TextArea } = Input;
const { Option } = Select;
// alert('hi');
const { changeCurrent } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddCustomer extends React.Component {
	state = {
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		staffData: null,
		fileList: [],
		companyList: [],
		tags: [],
		companyName: this.props.company,
		companyID: this.props.companyID,
		integrationKeyStatus: false,
		integrationKey: "",
		consumerLoginAccessToken: "",
		storageCompanyId: "",
		isAccountIdRequired: false,
		PickupAddress: "",
		PickupCity: "",
		PickupCountry: "",
		PickupState: "",
		PickupZipCode: "",
	};

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	componentDidMount() {
		this.setState({ contentloader: true });


		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							integrationKeyStatus: true,
							integrationKey: response.data.integration_key,
							storageCompanyId: response.data.key_company.storage_company_id
						});
						this.consumerLoginJson(response.data.integration_key);
					} else {
						this.setState({
							contentloader: false,
							integrationKeyStatus: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});




		const data = [];


		API.get("api/admin/staff/basic/0", data)
			.then((response) => {
				if (response) {
					this.setState({
						staffData: response.data && response.data,
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.post("api/admin/company/list", data).then((response) => {
			if (response) {
				if (response.status === 1) {
					this.setState({
						companyList: response.data.companyList.rows,
						isAccountIdRequired: response.data && response.data.companyList && response.data.companyList.rows[0] && response.data.companyList.rows[0].make_account_id_mandatory == 1 ? true : false,
						contentloader: false,
					});
				} else {
					this.setState({
						usersList: response.data,
						contentloader: false,
					});
				}
			}
		});

		API.get(`api/admin/tag/list/customer`)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							tags: response.data,
							contentloader: false,
						});

					} else {
						message.error(response.message);
						this.setState({ contentloader: false });
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}


	consumerLoginJson = (integrationKey) => {
		this.setState({ contentloader: true, loading: true });

		const consumerLoginJson = JSON.stringify({
			companyIdTokenMoverInventory: this.state.integrationKey ? this.state.integrationKey : integrationKey,
			email: "<EMAIL>",
			password: "5PLaRAqq",
			deviceToken: "abcd",
			deviceType: 0,
		});

		axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson,
			{
				headers: {
					'Content-Type': 'application/json'
				}
			})
			.then((consumerLoginResponse) => {
				this.setState({ contentloader: false, loading: false, consumerLoginAccessToken: consumerLoginResponse.data.data.accessToken });
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false });
			});
	}

	handleUpload = ({ fileList }) => {
		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};


	addCustomerJson = async (response) => {
		const addCustomerJson = JSON.stringify({
			importCustomerId: response.data.customer_id,
			firstName: (response.data.first_name !== undefined) ? response.data.first_name : "",

			lastName: (response.data.last_name !== undefined) ? response.data.last_name : "",
			address: {
				addressLine1: (response.data.address1 !== undefined) ? response.data.address1 : "",
				addressLine2: (response.data.address2 !== undefined) ? response.data.address2 : "",
				city: (response.data.city !== undefined) ? response.data.city : "",
				state: (response.data.state !== undefined) ? response.data.state : "",
				zipcode: (response.data.zipCode !== undefined) ? response.data.zipCode : "",
				country: (response.data.country !== undefined) ? response.data.country : "",
			},
			email: [
				(response.data.email !== undefined) ? response.data.email : "",
				(response.data.email2 !== undefined) ? response.data.email2 : "",
			],
			phoneNumber: [
				(response.data.phone !== undefined) ? response.data.phone : "",
				(response.data.phone2 !== undefined) ? response.data.phone2 : "",
				(response.data.phone3 !== undefined) ? response.data.phone3 : "",
			],
			accountId: (response.data.account_id !== undefined) ? response.data.account_id : "",
			accountName: (response.data.account_name !== undefined) ? response.data.account_name : "",
			salesRep: (response.data.sales_rep !== undefined) ? response.data.sales_rep : "",
			companyId: this.state.storageCompanyId,
			importedTags: [

			],
			moverInventoryCustomerId: response.data.customer_id,
			createdFromMoverInventory: true
		});

		axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/customers`, addCustomerJson,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': this.state.consumerLoginAccessToken
				}
			})
			.then((addCustomerResponse) => {
				let params = {
					customer_id: response.data.customer_id,
					customerIdStorage: addCustomerResponse.data.data.id
				};

				const data = [];
				data["data"] = params;


				API.post("api/admin/customer/storageId/update", data)
					.then((storageIdUpdate) => {
						if (storageIdUpdate.status === 1) {
							this.setState({ loading: false, pageloading: false, contentloader: false });
							message.success(response.message);
							this.props.history.goBack();

						} else {
							this.setState({ loading: false, pageloading: false, contentloader: false });
							message.error(storageIdUpdate.message);
						}
					})
					.catch((error) => {
						message.error(error.message);

						this.setState({ loading: false, pageloading: false, contentloader: false });
					});

			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false });
			});
	}

	checkCustomerEmailValidation = async (response) => {
		const data = JSON.stringify({
			email: [
				(response.email !== undefined) ? response.email : "",
				(response.email2 !== undefined) ? response.email2 : "",
			],
		})
		const resultData = await axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/check-customer-email-already-exist`,
			data,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': this.state.consumerLoginAccessToken
				}
			})
		return resultData.data.data;
	}


	handleSubmit = (e) => {
		e.preventDefault();
		let formData = new FormData();
		this.props.form.validateFieldsAndScroll(async (err, values) => {
			if (this.state.integrationKeyStatus) {
				const checkResponse = await this.checkCustomerEmailValidation(values);
				if (checkResponse) {
					this.setState({ loading: false, contentloader: false });
					message.error("The user email id is associated with another customer in Moverstorage, please add a unique email id");
				}
				else {
					if (!err) {
						const data = [];
						const tag = [];
						if (values.tags) {
							values.tags.forEach((item) => {
								tag.push(item.key);
							});
						}
						formData.append("first_name", (values.first_name !== undefined) ? values.first_name : "");
						formData.append("last_name", (values.last_name !== undefined) ? values.last_name : "");
						formData.append("company_id", (values.company.key !== undefined) ? values.company.key : "");
						formData.append("address1", this.state.PickupAddress);
						formData.append("address2", (values.address2 !== undefined) ? values.address2 : "");
						formData.append("city", (values.city !== undefined) ? values.city : "");
						formData.append("state", (values.state !== undefined) ? values.state : "");
						formData.append("zipCode", (values.zipCode !== undefined) ? values.zipCode : "");
						formData.append("country", (values.country !== undefined) ? values.country : "");
						formData.append("phone", (values.phone !== undefined) ? values.phone : "");
						formData.append("phone2", (values.phone2 !== undefined) ? values.phone2 : "");
						formData.append("phone3", (values.phone3 !== undefined) ? values.phone3 : "");
						formData.append("country_code", (values.country_code !== undefined) ? values.country_code : "");
						formData.append("email", (values.email !== undefined) ? values.email : "");
						formData.append("email2", (values.email2 !== undefined) ? values.email2 : "");
						formData.append("account_id", (values.account_id !== undefined) ? values.account_id : "");
						formData.append("account_name", (values.account_name !== undefined) ? values.account_name : "");
						formData.append("sales_rep", (values.sales_rep !== undefined) ? values.sales_rep : "");
						formData.append("notes", (values.notes !== undefined) ? values.notes : "");
						formData.append("tag", JSON.stringify(tag));

						formData.append(
							"photo",
							values.photo && values.photo !== "" && this.state.fileList.length
								? this.state.fileList[0].originFileObj
								: ""
						);
						formData.append("password", values.first_name.replace(/\s/g, "") + "@123");

						data["data"] = formData;
						this.setState({ loading: true, contentloader: true });
						API.post("api/admin/customer/add", data)
							.then((response) => {
								if (response.status === 1) {
									if (this.state.integrationKeyStatus) {
										this.addCustomerJson(response)
									}
									else {
										this.setState({ loading: false, contentloader: false });
										message.success(response.message);
										this.props.history.goBack();
									}
								} else {
									this.setState({ loading: false, contentloader: false });
									message.error(response.message);
								}
							})
							.catch((error) => {
								this.setState({ loading: false, contentloader: false });
							});
					}
				}
			}
			else {

				if (!err) {
					const data = [];
					const tag = [];
					if (values.tags) {
						values.tags.forEach((item) => {
							tag.push(item.key);
						});
					}
					formData.append("first_name", (values.first_name !== undefined) ? values.first_name : "");
					formData.append("last_name", (values.last_name !== undefined) ? values.last_name : "");
					formData.append("company_id", (values.company.key !== undefined) ? values.company.key : "");
					formData.append("address1", this.state.PickupAddress);
					formData.append("address2", (values.address2 !== undefined) ? values.address2 : "");
					formData.append("city", (values.city !== undefined) ? values.city : "");
					formData.append("state", (values.state !== undefined) ? values.state : "");
					formData.append("zipCode", (values.zipCode !== undefined) ? values.zipCode : "");
					formData.append("country", (values.country !== undefined) ? values.country : "");
					formData.append("phone", (values.phone !== undefined) ? values.phone : "");
					formData.append("phone2", (values.phone2 !== undefined) ? values.phone2 : "");
					formData.append("phone3", (values.phone3 !== undefined) ? values.phone3 : "");
					formData.append("country_code", (values.country_code !== undefined) ? values.country_code : "");
					formData.append("email", (values.email !== undefined) ? values.email : "");
					formData.append("email2", (values.email2 !== undefined) ? values.email2 : "");
					formData.append("account_id", (values.account_id !== undefined) ? values.account_id : "");
					formData.append("account_name", (values.account_name !== undefined) ? values.account_name : "");
					formData.append("sales_rep", (values.sales_rep !== undefined) ? values.sales_rep : "");
					formData.append("notes", (values.notes !== undefined) ? values.notes : "");
					formData.append("tag", JSON.stringify(tag));

					formData.append(
						"photo",
						values.photo && values.photo !== "" && this.state.fileList.length
							? this.state.fileList[0].originFileObj
							: ""
					);
					formData.append("password", values.first_name.replace(/\s/g, "") + "@123");

					data["data"] = formData;
					this.setState({ loading: true, contentloader: true });
					API.post("api/admin/customer/add", data)
						.then((response) => {
							if (response.status === 1) {
								if (this.state.integrationKeyStatus) {
									this.addCustomerJson(response)
								}
								else {
									this.setState({ loading: false, contentloader: false });
									message.success(response.message);
									this.props.history.goBack();
								}
							} else {
								this.setState({ loading: false, contentloader: false });
								message.error(response.message);
							}
						})
						.catch((error) => {
							this.setState({ loading: false, contentloader: false });
						});
				}
			}
		});
	};

	normFile = (e) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e && e.fileList;
	};

	handleChangePickupAddress = PickupAddress => {
		this.setState({ PickupAddress });
	};

	handleSelectPickupAddress = (PickupAddress, placeId) => {
		if (placeId !== undefined && placeId !== "" && placeId !== null) {
			this.fetchPlaceDetails(placeId);
		}
		else {
			this.setState({ PickupAddress: PickupAddress });
		}
	};

	fetchPlaceDetails = async (placeId) => {

		let params = {
			placeId: placeId,
		};
		const data = [];
		data["data"] = params;
		this.setState({ loading: true, contentloader: true });

		API.post("api/admin/google/place/details", data)
			.then((response) => {
				const addressComponents = response.data && response.data.result && response.data.result.address_components;

				// Initialize variables to store city, state, and country
				let address, city, state, country, zipCode;

				// Iterate through the address components to find city, state, and country
				for (const component of addressComponents) {
					for (const type of component.types) {
						if (type === 'street_number' || type === 'route') {
							address = address ? `${address} ${component.long_name}` : component.long_name;
						} else if (type === 'locality') {
							city = component.long_name;
						} else if (type === 'administrative_area_level_1') {
							state = component.long_name;
						} else if (type === 'country') {
							country = component.long_name;
						} else if (type === 'postal_code') {
							zipCode = component.long_name;
						}
					}
				}

				this.setState({
					PickupAddress: address,
					PickupCity: city,
					PickupState: state,
					PickupCountry: country,
					PickupZipCode: zipCode
				});

				this.props.form.setFieldsValue({
					city: city,
					state: state,
					zipCode: zipCode,
					country: country,
				});
				this.setState({ loading: false, contentloader: false });
			})
			.catch((error) => {
				this.setState({ loading: false, contentloader: false });
				message.error(error.message);
			});
	};

	autocompleteDropdownStyle = {
		position: 'absolute',
		top: '100%',
		left: 0,
		width: '500px',
		zIndex: 1,
		backgroundColor: '#ffffff',
		border: '1px solid #ccc',
		boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
		maxHeight: '200px',
		overflowY: 'auto',
		padding: '5px',
	};

	render() {

		const { getFieldDecorator } = this.props.form;

		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		const countryCodeSelector = getFieldDecorator("country_code", {
			initialValue: "1",
		})(
			<Select disabled style={{ width: 70 }}>
				<Option value='1'>+1</Option>
			</Select>
		);
		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add Customer
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent
					style={{
						margin: "0 20px",
						height: "93%",
						overflowY: "auto",
					}}>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								{/* <Form.Item label="Customer Name">
									{getFieldDecorator("customer_name", {
										placeholder: "Customer Name",
										rules: [
											{
												required: true,
												message:
													"Please input customer name!",
												whitespace: true,
											},
										],
									})(<Input placeholder="Customer Name" />)}
								</Form.Item> */}
								<Form.Item label='First Name'>
									{getFieldDecorator("first_name", {
										placeholder: "First Name",
										rules: [
											{
												required: true,
												message: "Please input first name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='First Name' />)}
								</Form.Item>
								<Form.Item label='Last Name'>
									{getFieldDecorator("last_name", {
										placeholder: "Last Name",
									})(<Input placeholder='Last Name' />)}
								</Form.Item>
								<Form.Item label='Company Name'>
									{getFieldDecorator("company", {
										initialValue: this.state.companyName
											? {
												key: this.state.companyID,
												label: this.state.companyName,
											}
											: [],
										rules: [
											{
												required: true,
												message: "Please select company name",
											},
										],
									})(
										<Select
											size={"default"}
											labelInValue
											disabled={this.state.companyName}
											placeholder='Select Company Name'
											style={{ width: "100%" }}>
											{!this.state.companyName && this.state.companyList
												? this.state.companyList.map((e) => (
													<Option key={e.company_id} value={e.company_id}>
														{e.company_name}
													</Option>
												))
												: // this.state.companyList.map(
												// 		(e) =>
												// 			this.state.companyName === e.company_name && (
												// 				<Option key={e.company_id} value={e.company_id}>
												// 					{e.company_name}
												// 				</Option>
												// 			)
												// )
												""}
										</Select>
									)}
								</Form.Item>
								<Form.Item label='Primary Email'>
									{getFieldDecorator("email", {
										rules: [
											{
												type: "email",
												message: "The input is not valid Email!",
											},
											{
												required: true,
												message: "Please input your Email!",
											},
										],
									})(<Input placeholder='Primary Email' />)}
								</Form.Item>
								<Form.Item label='Account ID'>
									{getFieldDecorator("account_id",
										{
											rules: [
												{
													required: this.state.isAccountIdRequired ? true : false,
													message: "Please enter Account ID",
												},
											],
										}
									)(<Input placeholder='Account ID' />)}
								</Form.Item>
								<Form.Item label='Account Name'>
									{getFieldDecorator("account_name", {
										placeholder: "Account Name",
									})(<Input placeholder='Account name' />)}
								</Form.Item>
								<Form.Item label='Primary Phone Number'>
									{getFieldDecorator("phone", {
										// rules: [
										// 	{
										// 		required:true,
										// 		pattern: /^(\+)(\d{3}\s?){4}(\d{2})?$/gm,
										// 		message: "Please input valid Phone number!",
										// 	},
										// ],
									})(
										<Input
											// addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder=' Primary Phone Number'
											customInput={Input}
											maxLength={20}
										// format='+### ### ### ###'
										/>
									)}
								</Form.Item>
								<Form.Item label='Tags'>
									{getFieldDecorator(
										"tags",
										{}
									)(
										<Select
											getPopupContainer={trigger => trigger.parentNode}
											mode='multiple'
											labelInValue
											allowClear
											style={{ width: "100%" }}
											placeholder='Please select tags'>
											{this.state.tags.map((item) => (
												<Option value={item.tag_id} key={item.tag_id}>
													<Tag color={item.color ? item.color : ""}>{item.name}</Tag>
												</Option>
											))}
										</Select>
									)}
								</Form.Item>
								<Form.Item label='Customer Notes'>
									{getFieldDecorator("notes", {})(<TextArea rows={4} placeholder='Customer Notes' />)}
								</Form.Item>
								<Form.Item label='Email 2'>
									{getFieldDecorator("email2", {})(<Input placeholder='Secondary Email' />)}
								</Form.Item>
								<Form.Item label='Phone Number 2'>
									{getFieldDecorator("phone2", {
										// rules: [
										// 	{
										// 		pattern: /^(\+)(\d{3}\s?){4}(\d{2})?$/gm,
										// 		message: "Please input valid Phone number!",
										// 	},
										// ],
									})(
										<Input
											// addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Phone Number 2'
											customInput={Input}
											maxLength={20}
										// format='+### ### ### ###'
										/>
									)}
								</Form.Item>
								<Form.Item label='Phone Number 3'>
									{getFieldDecorator("phone3", {
										// rules: [
										// 	{
										// 		pattern: /^(\+)(\d{3}\s?){4}(\d{2})?$/gm,
										// 		message: "Please input valid Phone number!",
										// 	},
										// ],
									})(
										<Input
											// addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Phone Number 3'
											customInput={Input}
											maxLength={20}
										// format='+### ### ### ####'
										/>
									)}
								</Form.Item>
								{/* <Form.Item label='Address Line 1'>
									{getFieldDecorator("address1", {})(<Input placeholder='Address Line 1' />)}
								</Form.Item> */}
								<PlacesAutocomplete
									value={this.state.PickupAddress}
									onChange={this.handleChangePickupAddress}
									onSelect={this.handleSelectPickupAddress}
								>
									{({ getInputProps, suggestions, getSuggestionItemProps, loading }) => (
										<div>
											<Form.Item label='Address Line 1'>
												<div style={{ position: 'relative' }}>
													<Input
														{...getInputProps({
															placeholder: 'Address Line 1 ...',
														})}
													/>
													{suggestions.length > 0 && (
														<div
															style={this.autocompleteDropdownStyle}
															className="autocomplete-dropdown-container"
														>
															{loading && <div>Loading...</div>}
															{suggestions.map(suggestion => {
																const style = {
																	backgroundColor: suggestion.active ? '#f1f2f6' : '#ffffff',
																	cursor: 'pointer',
																};
																return (
																	<div
																		key={suggestion.id} // Add a unique key for each suggestion
																		{...getSuggestionItemProps(suggestion, {
																			style,
																		})}
																	>
																		<List.Item>
																			<List.Item.Meta
																				description={suggestion.description}
																			/>
																		</List.Item>
																	</div>
																);
															})}
														</div>
													)}
												</div>
											</Form.Item>

										</div>
									)}
								</PlacesAutocomplete>

								<Form.Item label='Address Line 2'>
									{getFieldDecorator("address2", {})(<Input placeholder='Address Line 2' />)}
								</Form.Item>
								<Form.Item label='City'>
									{getFieldDecorator("city", {
										initialValue: this.state.PickupCity
									})(<Input placeholder='City' />)}
								</Form.Item>
								<Form.Item label='State'>
									{getFieldDecorator("state", {
										initialValue: this.state.PickupState
									})(<Input placeholder='State' />)}
								</Form.Item>
								<Form.Item label='Zipcode'>
									{getFieldDecorator("zipCode", {
										initialValue: this.state.PickupZipCode
									})(<Input placeholder='Zipcode' />)}
								</Form.Item>
								<Form.Item label='Country'>
									{getFieldDecorator("country", {
										initialValue: this.state.PickupCountry
									})(<Input placeholder='Country' />)}
								</Form.Item>
								<Form.Item label='Sales Rep'>
									{getFieldDecorator("sales_rep", {})(<Input placeholder='Sales Rep' />)}
								</Form.Item>
								<Form.Item label='Profile Image'>
									{getFieldDecorator("photo", {
										valuePropName: "fileList",
										getValueFromEvent: this.normFile,
									})(
										<Upload
											listType='picture-card'
											// fileList={this.state.fileList}
											multiple={false}
											onPreview={this.handlePreview}
											onChange={this.handleUpload}
											showUploadList={{
												showRemoveIcon: true,
												showPreviewIcon: false,
											}}
											beforeUpload={() => false} // return false so that antd doesn't upload the picture right away
										>
											{this.state.fileList && this.state.fileList.length < 1 && (
												<Button>
													<Icon type='upload' /> Click to Upload
												</Button>
											)}
										</Upload>
									)}
								</Form.Item>
								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Add Customer
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

const mapStateToProps = (state) => {
	return {
		company: state.Auth.company,
		companyID: Number(state.Auth.companyID),
	};
};

export default Form.create()(connect(mapStateToProps, { changeCurrent })(AddCustomer));
