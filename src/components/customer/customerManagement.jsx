import {
	But<PERSON>,
	Col,
	Dropdown,
	Icon,
	Input,
	Menu,
	message,
	Modal,
	Row,
	Spin,
	Table,
	Tag,
	Tooltip,
	Upload
} from "antd";
import React from "react";
import ModalImage from "react-modal-image";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import user from "../../static/images/user1.png";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import queryString from 'query-string';
import { saveAs } from 'file-saver';
import Papa from 'papaparse';


const { changeCurrent } = appActions;

const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let customerId;

class customerManagement extends React.Component {
	state = {
		apiParam: {},
		customerList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		confirmModal: false,
		loading: false,
		confirmLoading: false,
		adminId: "",
		companyId: "",
		staffId: "",
		apiKeyStatus: false,
		isMainAdmin: false,
		integrationKeyStatus: false,
		integrationKey: "",
		storageCompanyId: "",
		isUploading: false

	};
	componentDidMount() {
		const queryParams = queryString.parse(window.location.search);
		let params = {
			search: "",
			orderBy: "created_at",
			pageNo: parseInt(queryParams.page) || "1",
			orderSequence: "DESC",
			pageSize: 25,
			filter: "active",
		};
		const userType = localStorage.getItem("userType") === "1" ? true : false;

		this.setState({ apiParam: params, isMainAdmin: userType }, () => this.fetchCustomerList());

		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							integrationKeyStatus: true,
							integrationKey: response.data.integration_key,
							storageCompanyId: response.data.key_company.storage_company_id
						});
						this.consumerLoginJson(response.data.integration_key);
					} else {
						this.setState({
							contentloader: false,
							integrationKeyStatus: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.get(`api/open-api/apiKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						if (response.data.isEnable === 1) {
							this.setState({
								apiKeyStatus: true,
							});
						}
						else {
							this.setState({
								apiKeyStatus: false,
							});
						}

					} else {
						console.log(response.message);
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});


		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});
	}
	fetchCustomerList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		this.setState({ pageloading: true });
		API.get(
			`api/admin/customer/list?search=${this.state.apiParam.search}&pageNo=${this.state.apiParam.pageNo}&pageSize=${this.state.apiParam.pageSize}&orderSequence=${this.state.apiParam.orderSequence}&orderBy=${this.state.apiParam.orderBy}&filter=${this.state.apiParam.filter}`
		)
			.then((response) => {
				const pagination = { ...this.state.pagination };
				if (response) {
					if (response.status === 1) {
						pagination.total = response.data.count;
						this.setState({
							customerList: response.data.rows,
							pagination,
							pageloading: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							pagination,
							pageloading: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	};
	menus = (text) => {
		return (
			<Menu>
				{this.state.apiKeyStatus ? "" :
					<Menu.Item key='1'>
						<Tooltip title='Edit'>
							<Button
								type='primary'
								className='c-btn c-round c-warning'
								icon='edit'
								onClick={() => this.edit(text.customer_id)}></Button>
						</Tooltip>
					</Menu.Item>
				}
				{this.state.apiKeyStatus ? "" :
					<Menu.Item key='2'>
						<Tooltip title='Create Shipment'>
							<Button
								type='primary'
								className='c-btn c-round c-success'
								icon='plus'
								onClick={() => this.createShipment(text)}></Button>
						</Tooltip>
					</Menu.Item>
				}
				{this.state.apiKeyStatus ? "" :
					<Menu.Item key='3'>
						<Tooltip title='Delete'>
							<Button
								type='primary'
								className='c-btn c-round c-danger'
								icon='delete'
								onClick={() => this.delete(text.customer_id)}></Button>
						</Tooltip>
					</Menu.Item>
				}
			</Menu>
		);
	};
	handleChange = (pagination, filters, sorter) => {
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		const queryParams = queryString.parse(window.location.search);
		queryParams.page = pagination.current;
		const newUrl = `${window.location.pathname}?${queryString.stringify(queryParams)}`;
		window.history.pushState(null, null, newUrl);
		let params = {
			...this.state.apiParam,
			pageNo: pagination.current,
			orderBy: sorter.field && sorter.column ? sorter.field : "created_at",
			orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
			pageSize: pagination.pageSize,
			filter: filters.status && filters.status[0] ? filters.status[0] : "active",
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchCustomerList();
			}
		);
		scrollTo();
	};
	addCustomer() {
		this.props.history.push(`${this.props.match.url}/add`);
	}
	delete(id) {
		this.setState({ deleteModal: true });
		customerId = id;
	}
	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}
	confirmModal(id) {
		this.setState({ confirmModal: true });
		customerId = id;
	}
	createShipment(data) {
		this.props.changeCurrent("job");
		this.props.history.push({
			pathname: "/job/add",
			state: { customerDirect: data },
		});
	}

	handleCancel = () => {
		this.setState({
			deleteModal: false,
			confirmLoading: false,
			confirmModal: false,
		});
	};
	handleOk = () => {
		const id = customerId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { customer_id: id };
		API.post("api/admin/customer/delete", deleteData)
			.then((response) => {
				if (response) {
					this.fetchCustomerList();
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({ deleteModal: false, confirmLoading: false });
			});
	};
	handleStatusChange = () => {
		this.setState({ confirmLoading: true, pageloading: true });
		const id = customerId;
		const statusData = [];
		statusData["data"] = { customer_id: id };
		API.post("api/admin/customer/change-customer-status", statusData)
			.then((response) => {
				if (response) {
					this.setState({
						confirmModal: false,
						pageloading: false,
						confirmLoading: false,
					});
					this.fetchCustomerList({
						page: this.state.pagination.current ? this.state.pagination.current : 1,
					});
					message.success(response.message);
				} else {
					this.setState({
						confirmModal: false,
						pageloading: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch(() => {
				this.setState({
					confirmModal: false,
					confirmLoading: false,
					pageloading: false,
				});
			});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchCustomerList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					pageNo: 1,
				},
				search: e,
			},
			this.callback
		);
	};

	handleExportCSV = () => {
		const headers = [
			{
				first_name: 'John',
				last_name: 'Doe',
				email: '<EMAIL>',
				address1: '1234 Elm Street',
				address2: 'Apartment 12B',
				city: 'Springfield',
				state: 'Illinois',
				zipCode: '62704',
				country: 'USA',
				phone: '******-123-4567',
				account_id: '**********'
			}
		];
		const csv = Papa.unparse(headers);
		const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
		saveAs(blob, 'Sample.csv');
	};

	addCSVCustomer = (results) => {
		if (this.state.isUploading) return; 
		this.setState({ confirmLoading: true, pageloading: true, isUploading: true });
		const formData = [];
		formData["data"] = {
			customerData: results.data,
			integrationKeyStatus: this.state.integrationKeyStatus,
			integrationKey: this.state.integrationKey,
			storageCompanyId: this.state.storageCompanyId,
			companyId: localStorage.getItem("companyID")
		}
		API.post("api/admin/add-csv-customer", formData)
			.then((response) => {
				if (response) {
					this.fetchCustomerList();
					this.setState({
						confirmLoading: false,
						pageloading: false,
						isUploading: false
					});
					message.success(response.message);
				} else {
					this.setState({
						confirmLoading: false,
						pageloading: false,
						isUploading: false
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({
					confirmLoading: false,
					pageloading: false,
					isUploading: false
				});
			});
	}

	csvFileUpload = (data) => {
		if (data.file && data.file.originFileObj) {
			if (this.state.isUploading) {
				return;
			}
			Papa.parse(data.file.originFileObj, {
				header: true,
				skipEmptyLines: true,
				complete: (results) => {
					this.addCSVCustomer(results);
				},
				error: (error) => {
					message.error("Error while uploading CSV");
				}
			});
		} else {
			message.error('No file found or file is not valid.');
		}
	};


	render() {
		const { apiKeyStatus } = this.state;
		const columns = [
			{
				title: "Customer Name",
				dataIndex: "first_name",
				key: "first_name",
				sorter: true,
				align: "left",
				width: "15%",
				render: (record, text) => {
					return (
						<div style={{ display: "flex", alignItems: "center" }}>
							<div
								style={{
									width: "50px",
									height: "50px",
									marginTop: "10px"
								}}>
								<ModalImage
									className='imageModal'
									small={
										text.customer_profile && text.customer_profile.trim() !== ""
											? `${text.customer_profile}`
											: user
									}
									large={
										text.customer_profile && text.customer_profile.trim() !== ""
											? `${text.customer_profile}`
											: user
									}
									hideDownload={true}
									hideZoom={true}
								/>
							</div>
							<div
								style={{
									flex: "1",
								}}>
								{/* {text.customer_name} */}
								{text.first_name} {text.last_name !== "undefined" ? text.last_name : ""}
							</div>
						</div>
					);
				},
			},

			{
				title: "Account ID",
				dataIndex: "account_id",
				key: "account_id",
				align: "center",
				width: "10%",
			},

			{
				title: "Address",
				// dataIndex: "address1",
				key: "address1",
				align: "left",
				render: (text, record) => {
					return (
						<div>
							<div>
								<p style={{ margin: "0" }}>
									{record && record.address1 && record.address1 !== "undefined" && record.address1 !== "null"
										? record.address1
										: ""}
									&nbsp;{" "}
									{record && record.address2 && record.address2 !== "undefined" && record.address2 !== "null"
										? record.address2
										: ""}
								</p>
							</div>
							<div>
								<p style={{ margin: "0" }}>
									{record && record.city && record.city !== "undefined" && record.city !== "null"
										? record.city
										: ""}
									&nbsp;{" "}
									{record && record.state && record.state !== "undefined" && record.state !== "null"
										? record.state
										: ""}
									{record && record.zipCode && record.zipCode !== "undefined" && record.zipCode !== "null"
										? ", " + record.zipCode
										: ""}
								</p>
							</div>
							<div>
								<p style={{ margin: "0" }}>
									{record && record.country && record.country !== "undefined" && record.country !== "null"
										? record.country
										: ""}
								</p>
							</div>
						</div>
					);
				},
			},
			{
				title: "Phone",
				dataIndex: "phone",
				key: "phone",
				align: "left",
				render: (record, text) => {
					return record === "undefined" || record === "" ? "" : record
				},
			},
			{
				title: "Customer Tags",
				dataIndex: "customer_tag_name",
				key: "customer_tag_name",
				align: "center",
				render: (text, record) => {
					return (
						<div style={{ display: "flex", flexWrap: "wrap" }}>
							{record
								? record.customer_tag.map((item, index) => {
									return (
										<Tag
											color={item.m2m_customer_tag.color}
											style={{ marginTop: "5px" }}
											key={index}>
											{item.m2m_customer_tag.name}
										</Tag>

									)
								})

								: ""}
						</div>
					)
				}

			},
			{
				title: "Email",
				dataIndex: "email",
				key: "email",
				sorter: true,
				align: "left",
			},
			{
				title: "Account Name",
				dataIndex: "account_name",
				key: "account_name",
				sorter: true,
				align: "center",
				render: (record) => (record === "undefined" ? "" : record),
			},
			{
				title: "Total Shipments",
				dataIndex: "total_shipment",
				key: "total_shipment",
				sorter: true,
				align: "center",
			},
			{
				title: (data) => (
					<div>
						<p style={{ margin: "0" }}>Status</p>
						<p style={{ margin: "0", textTransform: "capitalize" }}>
							({data.filters && data.filters.status ? data.filters.status[0] : ""})
						</p>
					</div>
				),
				dataIndex: "status",
				key: "status",
				align: "center",
				filterMultiple: false,
				filtered: true,
				defaultFilteredValue: ["active"],
				filteredValue: [`${this.state.apiParam.filter}`],
				filters: [
					{ text: "Active", value: "active" },
					{ text: "Inactive", value: "inactive" },
				],
				render: (text, record) => {
					return (
						<div>
							<button
								className={record.status === "active" ? "statusButtonActive" : "statusButtoninactive"}
								onClick={() => this.confirmModal(record.customer_id)}>
								<Icon type='swap' />
								{record.status === "active" ? " Active" : " Inactive"}
							</button>
						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				render: (record, text) => {
					return (
						<div className='icons' style={{ textAlign: "center" }}>
							<Dropdown placement='bottomCenter' overlay={this.menus.bind(null, text)}>
								<Icon
									type='setting'
									theme='twoTone'
									twoToneColor='#3fa146'
									onClick={() => this.menus.bind(null, text)}
								/>
							</Dropdown>
						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<Col sm={8}>
							<Row align="middle" justify="space-between">
								<Col sm={16}>
									<h2 style={{ marginBottom: "0", display: "inline-flex", alignItems: "center" }}>
										<Icon type="user" /> &emsp;Customer Management
									</h2>
								</Col>
								{!this.state.isMainAdmin && (
									<Col sm={8}>
										<Button
											className="addButton"
											style={{ marginTop: "0" }}
											type="primary"
											onClick={() => this.handleExportCSV()}>
											Download Sample CSV
										</Button>
									</Col>
								)}
							</Row>
						</Col>
						<Col sm={6}>
							{this.state.search ? (
								<p
									style={{
										margin: "5px",
										textAlign: "center",
										color: "#303030",
									}}>
									Matching Records: {this.state.pagination.total}
								</p>
							) : (
								""
							)}
						</Col>
						<Col sm={10}>
							<Row
								align="middle"
								style={{
									display: "flex",
									justifyContent: "center",
									alignItems: "center",
									marginTop: "0",
								}}
							>
								<Col
									sm={10}
								>
									<Search
										placeholder='Search Customer'
										onChange={(e) => this.handleSearch(e.target.value)}
										value={this.state.search}
										style={{ width: 200, marginBottom: "10px" }}
									/>
								</Col>

								{!this.state.isMainAdmin && (
									<Col sm={6}>
										<Upload
											accept=".csv"
											onChange={this.csvFileUpload}
											maxCount={1}
											showUploadList={false}
										>
											<Button
												className='addButton'
												type='primary'
												style={{
													marginTop: "7px",
												}}
												disabled={this.state.isUploading}
											>
												<Icon type='upload' /> Upload CSV
											</Button>
										</Upload>
									</Col>
								)}

								{!this.state.isMainAdmin && (
									<Col sm={8}>
										<Button
											className='addButton'
											style={{
												marginTop: "0",
											}}
											type='primary'
											onClick={() => this.addCustomer()}
										>
											+ Add Customer
										</Button>
									</Col>
								)}
							</Row>
						</Col>

					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div style={{ marginTop: "-1.5rem", overflowX: "auto" }}>
							<Table
								bordered={true}
								columns={columns}
								pagination={{
									// pageSize: 10,
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 25,
									pageSizeOptions: ["25", "50"],
									current: this.state.apiParam.pageNo
								}}
								key={this.state.customerList}
								dataSource={this.state.customerList}
								onChange={this.handleChange}
							/>
						</div>
					</LayoutContent>
				</Spin>
				{
					<>
						<Modal
							title='Are You Sure?'
							visible={this.state.deleteModal}
							onOk={this.handleOk}
							okText='Yes'
							cancelText='No'
							centered
							maskClosable={false}
							confirmLoading={this.state.confirmLoading}
							onCancel={this.handleCancel}>
							<p>Are you sure you want to delete Customer?</p>
						</Modal>

						<Modal
							title='Are You Sure?'
							visible={this.state.confirmModal}
							onOk={this.handleStatusChange}
							okText='Yes'
							cancelText='No'
							centered
							maskClosable={false}
							confirmLoading={this.state.confirmLoading}
							onCancel={this.handleCancel}>
							<p>Are you sure you want to change status of this Customer?</p>
						</Modal>
					</>
				}
			</LayoutContentWrapper>
		);
	}
}
export default connect(null, { changeCurrent })(customerManagement);
