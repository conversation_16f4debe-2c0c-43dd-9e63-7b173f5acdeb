import { Button, Form, Icon, Input, message, Select, Spin, Tag, Upload, List } from "antd";
import React from "react";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';
import PlacesAutocomplete, {
	geocodeByAddress,
} from 'react-places-autocomplete';
import { GOOGLE_API_KEY } from "../../static/data/constants";

const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { TextArea } = Input;
const { Option } = Select;

class EditCustomer extends React.Component {
	state = {
		customerData: null,
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		companyList: [],
		tags: [],
		companyName: localStorage.getItem("company"),
		integrationKeyStatus: false,
		integrationKey: "",
		consumerLoginAccessToken: "",
		storage_customer_id: "",
		storageCompanyId: "",
		isAccountIdRequired: false,
		PickupAddress: "",
		PickupCity: "",
		PickupCountry: "",
		PickupState: "",
		PickupZipCode: "",
	};
	componentDidMount() {
		const id = this.props.match.params.id;
		this.props.changeCurrent("customer");
		this.setState({ contentloader: true });
		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							integrationKeyStatus: true,
							integrationKey: response.data.integration_key,
							storageCompanyId: response.data.key_company.storage_company_id
						});
						this.consumerLoginJson(response.data.integration_key);
					} else {
						this.setState({
							contentloader: false,
							integrationKeyStatus: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});


		const data = [];
		data["data"] = { customer_id: id };
		API.post("api/admin/customer/view-customer", data)
			.then((response) => {
				if (response) {
					this.setState(
						{
							customerData: response.data && response.data,
							storage_customer_id: response.data && response.data.storage_customer_id,
							PickupAddress: response.data && response.data.address1 !== "undefined" ? response.data.address1 : "",
							PickupCity: response.data && response.data.city !== "undefined" ? response.data.city : "",
							PickupState: response.data && response.data.state !== "undefined" ? response.data.state : "",
							PickupZipCode: response.data && response.data.zipCode !== "undefined" ? response.data.zipCode : "",
							PickupCountry: response.data && response.data.country !== "undefined" ? response.data.country : "",

							fileList:
								response.data && response.data.customer_profile !== "" && response.data.customer_profile !== undefined && response.data.customer_profile !== null ?
									[
										{
											uid: -1,
											status: "done",
											url: response.data.customer_profile,
										},
									]
									: [],
							previewImage: response.data.customer_profile,
							previewVisible: true,
							contentloader: false,
						},
						() => this.props.form.setFieldsValue({})
					);
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.post("api/admin/company/list", data)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							companyList: response.data.companyList.rows,
							isAccountIdRequired: response.data && response.data.companyList && response.data.companyList.rows[0] && response.data.companyList.rows[0].make_account_id_mandatory == 1 ? true : false,
							contentloader: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							contentloader: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.get(`api/admin/tag/list/customer`)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							tags: response.data,
							contentloader: false,
						});
					} else {
						message.error(response.message);
						this.setState({ contentloader: false });
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}


	consumerLoginJson = (integrationKey) => {
		this.setState({ contentloader: true, loading: true });

		const consumerLoginJson = JSON.stringify({
			companyIdTokenMoverInventory: this.state.integrationKey ? this.state.integrationKey : integrationKey,
			email: "<EMAIL>",
			password: "5PLaRAqq",
			deviceToken: "abcd",
			deviceType: 0,
		});

		axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson,
			{
				headers: {
					'Content-Type': 'application/json'
				}
			})
			.then((consumerLoginResponse) => {
				this.setState({ contentloader: false, loading: false, consumerLoginAccessToken: consumerLoginResponse.data.data.accessToken });
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false });
			});
	}

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {
		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};
	isCompanyExist = (id, companyData) => {
		let status = false;
		for (let i = 0; i < companyData.length; i++) {
			let company_id = companyData[i].company_id;
			if (company_id === id) {
				status = true;
				break;
			}
		}
		return status;
	};


	editCustomerJson = async (response) => {
		const editCustomerJson = JSON.stringify({
			id: this.state.storage_customer_id,
			importCustomerId: this.props.match.params.id,
			firstName: (response.first_name !== undefined) ? response.first_name : "",
			lastName: (response.last_name !== undefined) ? response.last_name : "",
			address: {
				addressLine1: (response.address1 !== undefined) ? response.address1 : "",
				addressLine2: (response.address2 !== undefined) ? response.address2 : "",
				city: (response.city !== undefined) ? response.city : "",
				state: (response.state !== undefined) ? response.state : "",
				zipcode: (response.zipCode !== undefined) ? response.zipCode : "",
				country: (response.country !== undefined) ? response.country : "",
			},
			email: [
				(response.email !== undefined) ? response.email : "",
				(response.email2 !== undefined) ? response.email2 : "",
			],
			phoneNumber: [
				(response.phone !== undefined) ? response.phone : "",
				(response.phone2 !== undefined) ? response.phone2 : "",
				(response.phone3 !== undefined) ? response.phone3 : "",
			],
			accountId: (response.account_id !== undefined) ? response.account_id : "",
			accountName: (response.account_name !== undefined) ? response.account_name : "",
			salesRep: (response.sales_rep !== undefined) ? response.sales_rep : "",
			companyId: this.state.storageCompanyId,
			importedTags: [

			],
		});

		axios.put(`${MOVER_STORAGE_API_URL}import/mover-inventory/customers`, editCustomerJson,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': this.state.consumerLoginAccessToken
				}
			})
			.then((editCustomerResponse) => {
				this.setState({ contentloader: false, loading: false });
				message.success("Customer update successfully.")
				this.props.history.goBack();
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false });
			});
	}

	handleSubmit = (e) => {
		e.preventDefault();
		let formData = new FormData();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];
				const tag = [];
				if (values.tags) {
					values.tags.forEach((item) => {
						tag.push(item.key);
					});
				}
				formData.append("customer_id", this.props.match.params.id);
				formData.append("first_name", (values.first_name !== undefined) ? values.first_name : "");
				formData.append("last_name", (values.last_name !== undefined) ? values.last_name : "");
				formData.append("company_id", (values.company_id.key !== undefined) ? values.company_id.key : "");
				formData.append("address1", this.state.PickupAddress);
				formData.append("address2", (values.address2 !== undefined) ? values.address2 : "");
				formData.append("city", (values.city !== undefined) ? values.city : "");
				formData.append("state", (values.state !== undefined) ? values.state : "");
				formData.append("zipCode", (values.zipCode !== undefined) ? values.zipCode : "");
				formData.append("country", (values.country !== undefined) ? values.country : "");
				formData.append("phone", (values.phone !== undefined) ? values.phone : "");
				formData.append("phone2", (values.phone2 !== undefined) ? values.phone2 : "");
				formData.append("phone3", (values.phone3 !== undefined) ? values.phone3 : "");
				formData.append("country_code", (values.country_code !== undefined) ? values.country_code : "");
				formData.append("email", (values.email !== undefined) ? values.email : "");
				formData.append("email2", (values.email2 !== undefined) ? values.email2 : "");
				formData.append("account_id", (values.account_id !== undefined) ? values.account_id : "");
				formData.append("account_name", (values.account_name !== undefined) ? values.account_name : "");
				formData.append("notes", (values.notes !== undefined) ? values.notes : "");
				formData.append("sales_rep", (values.sales_rep !== undefined) ? values.sales_rep : "");
				formData.append("tag", JSON.stringify(tag));
				if (values.photo && this.state.fileList[0] && this.state.fileList[0].originFileObj) {
					formData.append("photo", this.state.fileList[0].originFileObj);
					formData.append("retain_photo", true);
				} else if (this.state.fileList.length === 0) {
					formData.append("photo", "");
					formData.append("retain_photo", false);
				}

				data["data"] = formData;
				this.setState({ loading: true, contentloader: true });

				API.post("api/admin/customer/edit-customer", data)
					.then((response) => {
						if (response) {
							if (this.state.integrationKeyStatus && (this.state.storage_customer_id !== null && this.state.storage_customer_id !== "" && this.state.storage_customer_id !== undefined)) {
								this.editCustomerJson(values)
							}
							else {
								this.setState({ loading: false, contentloader: false });
								message.success(response.message);
								this.props.history.goBack();
							}
						} else {
							this.setState({ loading: false, contentloader: false });
							// message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false, contentloader: false });
					});
			}
		});
	};

	handleChangePickupAddress = PickupAddress => {
		this.setState({ PickupAddress });
	};

	handleSelectPickupAddress = (PickupAddress, placeId) => {

		if (placeId !== undefined && placeId !== "" && placeId !== null) {
			this.fetchPlaceDetails(placeId);
		}
		else {
			this.setState({ PickupAddress: PickupAddress });
		}
	};

	fetchPlaceDetails = async (placeId) => {
		let params = {
			placeId: placeId,
		};
		const data = [];
		data["data"] = params;
		this.setState({ loading: true, contentloader: true });

		API.post("api/admin/google/place/details", data)
			.then((response) => {
				const addressComponents = response.data && response.data.result && response.data.result.address_components;

				// Initialize variables to store city, state, and country
				let address, city, state, country, zipCode;

				// Iterate through the address components to find city, state, and country
				for (const component of addressComponents) {
					for (const type of component.types) {
						if (type === 'street_number' || type === 'route') {
							address = address ? `${address} ${component.long_name}` : component.long_name;
						} else if (type === 'locality') {
							city = component.long_name;
						} else if (type === 'administrative_area_level_1') {
							state = component.long_name;
						} else if (type === 'country') {
							country = component.long_name;
						} else if (type === 'postal_code') {
							zipCode = component.long_name;
						}
					}
				}

				this.setState({
					PickupAddress: address,
					PickupCity: city,
					PickupState: state,
					PickupCountry: country,
					PickupZipCode: zipCode
				});

				this.props.form.setFieldsValue({
					city: city,
					state: state,
					zipCode: zipCode,
					country: country,
				});
				this.setState({ loading: false, contentloader: false });

			})
			.catch((error) => {
				this.setState({ loading: false, contentloader: false });
				message.error(error.message);
			});
	};

	autocompleteDropdownStyle = {
		position: 'absolute',
		top: '100%',
		left: 0,
		width: '500px',
		zIndex: 1,
		backgroundColor: '#ffffff',
		border: '1px solid #ccc',
		boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
		maxHeight: '200px',
		overflowY: 'auto',
		padding: '5px',
	};

	render() {

		const { getFieldDecorator } = this.props.form;
		const { customerData, companyList } = this.state;

		const initialCompany =
			(customerData && customerData.company_id === "") ||
				(customerData &&
					customerData.company_id &&
					!this.isCompanyExist(customerData.company_id, companyList))
				? {}
				: { key: customerData && customerData.company_id };
		const initialTag =
			customerData &&
			customerData.customer_tag.map((item, index) =>
				item.m2m_customer_tag.tag_id === "" ? {} : { key: item.m2m_customer_tag.tag_id }
			);
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		const countryCodeSelector = getFieldDecorator("country_code", {
			initialValue: customerData && customerData.country_code,
		})(
			<Select disabled style={{ width: 70 }}>
				<Option value='1'>+1</Option>
			</Select>
		);
		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Edit Customer
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								{/* <Form.Item label="Customer Name">
									{getFieldDecorator("customer_name", {
										initialValue: customerData && customerData.first_name ? customerData.first_name + customerData.last_name : '' ,
										rules: [
											{
												required: true,
												message: "Please input customer name!",
												whitespace: true,
											},
										],
									})(<Input />)}
								</Form.Item> */}
								<Form.Item label='First Name'>
									{getFieldDecorator("first_name", {
										initialValue: customerData && customerData.first_name,
										placeholder: "First Name",
										rules: [
											{
												required: true,
												message: "Please input first name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='First Name' />)}
								</Form.Item>
								<Form.Item label='Last Name'>
									{getFieldDecorator("last_name", {
										initialValue:
											customerData && customerData.last_name
												? customerData.last_name !== "undefined"
													? customerData.last_name
													: ""
												: "",
										placeholder: "Last Name",
									})(<Input placeholder='Last Name' />)}
								</Form.Item>
								<Form.Item label='Company Name'>
									{getFieldDecorator("company_id", {
										initialValue: initialCompany,
										rules: [
											{
												required: true,
												message: "Please select company name",
											},
										],
									})(
										<Select
											size={"default"}
											labelInValue
											disabled={this.state.companyName}
											placeholder='Select Company Name'
											style={{ width: "100%" }}>
											{companyList
												? companyList.map((e) => (
													<Option key={e.company_name} value={e.company_id}>
														{e.company_name}
													</Option>
												))
												: null}
										</Select>
									)}
								</Form.Item>
								<Form.Item label='Primary Email'>
									{getFieldDecorator("email", {
										initialValue: customerData && customerData.email,
										rules: [
											{
												type: "email",
												message: "The input is not valid Email!",
											},
											{
												required: true,
												message: "Please input your Email!",
											},
										],
									})(<Input />)}
								</Form.Item>
								<Form.Item label='Account ID'>
									{getFieldDecorator("account_id", {
										initialValue: customerData && customerData.account_id,
										rules: [
											{
												required: this.state.isAccountIdRequired ? true : false,
												message: "Please enter Account ID",
											},
										],
									})(<Input placeholder='Account ID' />)}
								</Form.Item>
								<Form.Item label='Account Name'>
									{getFieldDecorator("account_name", {
										initialValue:
											customerData && customerData.account_name
												? customerData.account_name !== "undefined"
													? customerData.account_name
													: ""
												: "",
										placeholder: "Account Name",
									})(<Input placeholder='Account name' />)}
								</Form.Item>
								<Form.Item label='Primary Phone Number'>
									{getFieldDecorator("phone", {
										initialValue:
											customerData && customerData.phone
												? customerData.phone !== "undefined"
													? customerData.phone
													: ""
												: "",
										// rules: [
										// 	{
										// 		pattern: /^(\+)(\d{3}\s?){4}(\d{2})?$/gm,
										// 		message: "Please input valid Phone number!",
										// 	},
										// ],
									})(
										<Input
											// addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Primary Phone Number'
											customInput={Input}
											maxLength={20}
										// format='+### ### ### ### ###'
										/>
									)}
								</Form.Item>
								<Form.Item label='Tags'>
									{getFieldDecorator("tags", {
										initialValue: initialTag && initialTag[0] ? initialTag : [],
									})(
										<Select
											getPopupContainer={trigger => trigger.parentNode}
											mode='multiple'
											labelInValue
											allowClear
											style={{ width: "100%" }}
											placeholder='Please select tags'>
											{this.state.tags.map((item, index) => (
												<Option value={item.tag_id} key={item.tag_id}>
													<Tag color={item.color ? item.color : ""}>{item.name}</Tag>
												</Option>
											))}
										</Select>
									)}
								</Form.Item>
								<Form.Item label='Customer Notes'>
									{getFieldDecorator("notes", {
										initialValue:
											customerData && customerData.notes
												? customerData.notes !== "undefined"
													? customerData.notes
													: ""
												: "",
									})(<TextArea rows={4} placeholder='Customer Notes' />)}
								</Form.Item>
								<Form.Item label='Email 2'>
									{getFieldDecorator("email2", {
										initialValue: customerData && customerData.email2,
										rules: [
											{
												type: "email",
												message: "The input is not valid Email!",
											},
										],
									})(<Input />)}
								</Form.Item>
								<Form.Item label='Phone Number 2'>
									{getFieldDecorator("phone2", {
										initialValue:
											customerData && customerData.phone2
												? customerData.phone2 !== "undefined"
													? customerData.phone2
													: ""
												: "",
										// rules: [
										// 	{
										// 		pattern: /^(\+)(\d{3}\s?){4}(\d{2})?$/gm,
										// 		message: "Please input valid Phone number!",
										// 	},
										// ],
									})(
										<Input
											// addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Phone Number 2'
											customInput={Input}
											maxLength={20}
										// format='+### ### ### ###'
										/>
									)}
								</Form.Item>
								<Form.Item label='Phone Number 3'>
									{getFieldDecorator("phone3", {
										initialValue:
											customerData && customerData.phone3
												? customerData.phone3 !== "undefined"
													? customerData.phone3
													: ""
												: "",
										// rules: [
										// 	{
										// 		pattern: /^(\+)(\d{3}\s?){4}(\d{2})?$/gm,
										// 		message: "Please input valid Phone number!",
										// 	},
										// ],
									})(
										<Input
											// addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Phone Number 3'
											customInput={Input}
											maxLength={20}
										// format='+### ### ### ###'
										/>
									)}
								</Form.Item>
								{/* <Form.Item label="Primary Address">
									{getFieldDecorator("address", {
										initialValue:
											customerData && customerData.address
												? customerData.address !== "undefined"
													? customerData.address
													: ""
												: "",
									})(<TextArea rows={4} placeholder="Primary Address" />)}
								</Form.Item> */}
								{/* <Form.Item label='Address Line 1'>
									{getFieldDecorator("address1", {
										initialValue:
											customerData && customerData.address1
												? customerData.address1 !== "undefined"
													? customerData.address1
													: ""
												: "",
									})(<Input placeholder='Address Line 1' />)}
								</Form.Item> */}

								<PlacesAutocomplete
									value={this.state.PickupAddress}
									onChange={this.handleChangePickupAddress}
									onSelect={this.handleSelectPickupAddress}
								>
									{({ getInputProps, suggestions, getSuggestionItemProps, loading }) => (
										<div>
											<Form.Item label='Address Line 1'>
												<div style={{ position: 'relative' }}>
													<Input
														{...getInputProps({
															placeholder: 'Address Line 1 ...',
														})}
													/>
													{suggestions.length > 0 && (
														<div
															style={this.autocompleteDropdownStyle}
															className="autocomplete-dropdown-container"
														>
															{loading && <div>Loading...</div>}
															{suggestions.map(suggestion => {
																const style = {
																	backgroundColor: suggestion.active ? '#f1f2f6' : '#ffffff',
																	cursor: 'pointer',
																};
																return (
																	<div
																		key={suggestion.id} // Add a unique key for each suggestion
																		{...getSuggestionItemProps(suggestion, {
																			style,
																		})}
																	>
																		<List.Item>
																			<List.Item.Meta
																				description={suggestion.description}
																			/>
																		</List.Item>
																	</div>
																);
															})}
														</div>
													)}
												</div>
											</Form.Item>
										</div>
									)}
								</PlacesAutocomplete>




								<Form.Item label='Address Line 2'>
									{getFieldDecorator("address2", {
										initialValue:
											customerData && customerData.address2
												? customerData.address2 !== "undefined"
													? customerData.address2
													: ""
												: "",
									})(<Input placeholder='Address Line 2' />)}
								</Form.Item>
								<Form.Item label='City'>
									{getFieldDecorator("city", {
										initialValue: this.state.PickupCity
									})(<Input placeholder='City' />)}
								</Form.Item>
								<Form.Item label='State'>
									{getFieldDecorator("state", {
										initialValue: this.state.PickupState
									})(<Input placeholder='State' />)}
								</Form.Item>
								<Form.Item label='Zipcode'>
									{getFieldDecorator("zipCode", {
										initialValue: this.state.PickupZipCode
									})(<Input placeholder='Zipcode' />)}
								</Form.Item>
								<Form.Item label='Country'>
									{getFieldDecorator("country", {
										initialValue: this.state.PickupCountry
									})(<Input placeholder='Country' />)}
								</Form.Item>

								<Form.Item label='Sales Rep'>
									{getFieldDecorator("sales_rep", {
										initialValue:
											customerData && customerData.sales_rep
												? customerData.sales_rep !== "undefined"
													? customerData.sales_rep
													: ""
												: "",
									})(<Input placeholder='Sales Rep	' />)}
								</Form.Item>
								<Form.Item label='Total Shipment'>
									{getFieldDecorator("total_shipment", {
										initialValue:
											customerData && customerData.total_shipment
												? customerData.total_shipment !== "undefined"
													? customerData.total_shipment
													: ""
												: 0,
									})(<Input placeholder='Total Shipment	' disabled />)}
								</Form.Item>
								<Form.Item label='Profile Image'>
									{getFieldDecorator("photo")(
										<Upload
											listType='picture-card'
											fileList={this.state.fileList}
											multiple={false}
											onPreview={this.handlePreview}
											onChange={this.handleUpload}
											showUploadList={{
												showRemoveIcon: true,
												showPreviewIcon: false,
											}}
											beforeUpload={() => false} // return false so that antd doesn't upload the picture right away
										>
											{this.state.fileList && this.state.fileList.length < 1 && (
												<Button>
													<Icon type='upload' /> Click to Upload
												</Button>
											)}
										</Upload>
									)}
								</Form.Item>
								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditCustomer));
