import styled from 'styled-components';

export const GridMainContainer = styled.div`
    background-color: ${props => props.color};
    border-radius: 5px; 
    padding: 15px 0px;
`;

export const GridSubContainer = styled.div`
    padding-left:0px;
    padding-right:0px;
`;

export const Numbers = styled.h3`
    text-align: center; 
    font-size: 23px;
    margin-bottom: 0px;
    color: #ffffff;
`;

export const Label = styled.p`
    text-align: center; 
    margin-bottom: 0px;
    color: #ffffff;
`;

