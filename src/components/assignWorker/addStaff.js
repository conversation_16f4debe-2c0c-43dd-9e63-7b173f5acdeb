import React from "react";
import { connect } from "react-redux";

import { Button, Form, Icon, Input, Spin, message, Upload, Select } from "antd";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import Api from "../../api/api-handler";

const { TextArea } = Input;
const { Option } = Select;
// alert('hi');
const { changeCurrent } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddStaff extends React.Component {
	state = {
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		companyData: null,
	};

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {
		//---------------^^^^^----------------
		// this is equivalent to your "const img = event.target.files[0]"
		// here, antd is giving you an array of files, just like event.target.files
		// but the structure is a bit different that the original file
		// the original file is located at the `originFileObj` key of each of this files
		// so `event.target.files[0]` is actually fileList[0].originFileObj

		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};

	componentDidMount() {
		// this.props.changeCurrent("sub-categories");
		this.setState({ contentloader: true });

		const data = [];
		API.post("api/admin/staff/view-company-list", data).then((response) => {
			if (response) {
				this.setState({
					companyData: response.data && response.data,
					contentloader: false,
				});
			} else {
				message.error(response.message);
				this.setState({ contentloader: false });
			}
		});
	}

	handleSubmit = (e) => {
		e.preventDefault();
		let formData = new FormData();

		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];

				formData.append("company_id", values.company.key);
				formData.append("first_name", values.first_name);
				formData.append("last_name", values.last_name);
				formData.append("address", values.address);
				formData.append("phone", values.phone);
				formData.append("country_code", values.country_code);
				formData.append("email", values.email);
				formData.append(
					"photo",
					values.profile && values.profile !== "" && this.state.fileList.length
						? this.state.fileList[0].originFileObj
						: ""
				);
				formData.append("password", values.first_name.replace(/\s/g, "") + "@123");

				data["data"] = formData;
				this.setState({ loading: true });
				API.post("api/admin/staff/add", data).then((response) => {
					if (response.status === 1) {
						this.setState({ loading: false });
						message.success(response.message);
						this.props.history.goBack();
					} else {
						this.setState({ loading: false });
						message.error(response.message);
					}
				});
			}
		});
	};

	normFile = (e) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e && e.fileList;
	};

	render() {
		const { getFieldDecorator } = this.props.form;

		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		const countryCodeSelector = getFieldDecorator("country_code", {
			initialValue: "86",
		})(
			<Select style={{ width: 70 }}>
				<Option value='86'>+86</Option>
				<Option value='87'>+87</Option>
				<Option value='91'>+91</Option>
			</Select>
		);
		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add Staff
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent style={{ margin: "0 20px", height: "93%", overflowY: "auto" }}>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Company'>
									{getFieldDecorator("company", {
										rules: [
											{
												required: true,
												message: "Please select company",
											},
										],
									})(
										<Select
											size={"default"}
											labelInValue
											placeholder='Select Company'
											style={{ width: "100%" }}>
											{this.state.companyData
												? this.state.companyData.map((e) => (
													<Option key={e.company_id} value={e.company_id}>
														{e.company_name}
													</Option>
												))
												: null}
										</Select>
									)}
								</Form.Item>

								<Form.Item label='First Name'>
									{getFieldDecorator("first_name", {
										placeholder: "First Name",
										rules: [
											{
												required: true,
												message: "Please input first name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='First Name' />)}
								</Form.Item>

								<Form.Item label='Last Name'>
									{getFieldDecorator("last_name", {
										placeholder: "Last Name",
										rules: [
											{
												required: true,
												message: "Please input last name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Last Name' />)}
								</Form.Item>

								<Form.Item label='Profile'>
									{getFieldDecorator("profile", {
										valuePropName: "fileList",
										getValueFromEvent: this.normFile,
									})(
										<Upload
											listType='picture-card'
											// fileList={this.state.fileList}
											multiple={false}
											onPreview={this.handlePreview}
											onChange={this.handleUpload}
											beforeUpload={() => false} // return false so that antd doesn't upload the picture right away
										>
											<Button
												disabled={
													this.state.fileList && this.state.fileList[0] && this.state.fileList[0].name
														? true
														: false
												}>
												<Icon type='upload' />{" "}
												{this.state.fileList && this.state.fileList[0] && this.state.fileList[0].name
													? this.state.fileList[0].name
													: "Click to Upload"}
											</Button>
										</Upload>
									)}
								</Form.Item>

								<Form.Item label='Address'>
									{getFieldDecorator("address", {
										rules: [
											{
												required: true,
												message: "Please input staff address!",
												whitespace: true,
											},
										],
									})(<TextArea rows={4} placeholder='Staff Address' />)}
								</Form.Item>

								<Form.Item label='Phone Number'>
									{getFieldDecorator("phone", {
										rules: [
											{
												required: true,
												message: "Please input your phone number!",
											},
										],
									})(
										<Input
											addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Phone Number'
										/>
									)}
								</Form.Item>

								<Form.Item label='Email'>
									{getFieldDecorator("email", {
										rules: [
											{
												type: "email",
												message: "The input is not valid Email!",
											},
											{
												required: true,
												message: "Please input your Email!",
											},
										],
									})(<Input />)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Add Staff
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(AddStaff));
