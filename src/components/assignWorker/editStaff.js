import "../../static/css/add.css";

import { Button, Form, Icon, Input, Spin, message, Upload, Select } from "antd";
import Api from "../../api/api-handler";

import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import React from "react";
import appActions from "../../redux/app/actions";
import { connect } from "react-redux";

const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { TextArea } = Input;
const { Option } = Select;

class EditStaff extends React.Component {
	state = {
		staffData: null,
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		companyData: [],
	};
	componentDidMount() {
		const id = this.props.match.params.id;
		this.props.changeCurrent("staff");
		this.setState({ contentloader: true });

		const data1 = [];
		API.post("api/admin/staff/view-company-list", data1).then((response) => {
			if (response) {
				this.setState({
					companyData: response.data && response.data,
					contentloader: false,
				});
			} else {
				message.error(response.message);
				this.setState({ contentloader: false });
			}
		});

		const data = [];
		data["data"] = { staff_id: id };
		API.post("api/admin/staff/view-staff", data).then((response) => {
			if (response) {
				this.setState(
					{
						staffData: response.data && response.data,
						fileList: [{ uid: -1, status: "done", url: response.data.staff_profile }],
						previewImage: response.data.staff_profile,
						previewVisible: true,
						contentloader: false,
					},
					() => this.props.form.setFieldsValue({})
				);
			} else {
				message.error(response.message);
				this.setState({ contentloader: false });
			}
		});
	}
	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {
		this.setState({ fileList });
	};
	handleSubmit = (e) => {
		e.preventDefault();
		let formData = new FormData();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];
				formData.append("staff_id", this.props.match.params.id);
				formData.append("company_id", values.company.key);
				formData.append("first_name", values.first_name);
				formData.append("last_name", values.last_name);
				formData.append("address", values.address);
				formData.append("phone", values.phone);
				formData.append("country_code", values.country_code);
				formData.append("email", values.email);
				formData.append(
					"photo",
					values.profile && values.profile !== "" && this.state.fileList.length > 0
						? this.state.fileList[0].originFileObj
						: ""
				);

				data["data"] = formData;
				this.setState({ loading: true });

				API.post("api/admin/staff/edit-staff", data).then((response) => {
					if (response) {
						this.setState({ loading: false });
						message.success(response.message);
						this.props.history.goBack();
					} else {
						this.setState({ loading: false });
						message.error(response.message);
					}
				});
			}
		});
	};
	render() {
		const { getFieldDecorator } = this.props.form;
		const { staffData } = this.state;
		const initialCompany =
			staffData && staffData.company_id === "" ? {} : { key: staffData && staffData.company_id };
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		const countryCodeSelector = getFieldDecorator("country_code", {
			initialValue: staffData && staffData.country_code,
		})(
			<Select style={{ width: 70 }}>
				<Option value='86'>+86</Option>
				<Option value='87'>+87</Option>
				<Option value='91'>+91</Option>
			</Select>
		);
		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Edit Staff
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Company name'>
									{getFieldDecorator("company", {
										initialValue: initialCompany,
										rules: [
											{
												required: true,
												message: "Please input company name!",
											},
										],
									})(
										<Select
											// disabled="disabled"
											size={"default"}
											labelInValue
											placeholder='Select Company'
											style={{ width: "100%" }}>
											{this.state.companyData
												? this.state.companyData.map((e) => (
														<Option key={e.company_id} value={e.company_id}>
															{e.company_name}
														</Option>
												  ))
												: null}
										</Select>
									)}
								</Form.Item>

								<Form.Item label='First Name'>
									{getFieldDecorator("first_name", {
										initialValue: staffData && staffData.first_name,
										placeholder: "First Name",
										rules: [
											{
												required: true,
												message: "Please input first name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='First Name' />)}
								</Form.Item>

								<Form.Item label='Last Name'>
									{getFieldDecorator("last_name", {
										initialValue: staffData && staffData.last_name,
										placeholder: "Last Name",
										rules: [
											{
												required: true,
												message: "Please input last name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Last Name' />)}
								</Form.Item>

								<Form.Item label='Profile'>
									{getFieldDecorator("profile")(
										<Upload
											listType='picture-card'
											fileList={this.state.fileList}
											multiple={false}
											onPreview={this.handlePreview}
											onChange={this.handleUpload}
											beforeUpload={() => false} // return false so that antd doesn't upload the picture right away
										>
											<Button
												disabled={
													this.state.fileList && this.state.fileList[0] && this.state.fileList[0].url
														? true
														: false
												}>
												<Icon type='upload' />{" "}
												{this.state.fileList && this.state.fileList[0] && this.state.fileList[0].url
													? "Click to Upload"
													: "Click to Upload"}
											</Button>
										</Upload>
									)}
								</Form.Item>

								<Form.Item label='Address'>
									{getFieldDecorator("address", {
										initialValue: staffData && staffData.address,
										rules: [
											{
												required: true,
												message: "Please input staff address!",
												whitespace: true,
											},
										],
									})(<TextArea rows={4} placeholder='Staff Address' />)}
								</Form.Item>

								<Form.Item label='Phone Number'>
									{getFieldDecorator("phone", {
										initialValue: staffData && staffData.phone,
										rules: [
											{
												required: true,
												message: "Please input your phone number!",
											},
										],
									})(
										<Input
											addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Phone Number'
										/>
									)}
								</Form.Item>

								<Form.Item label='Email'>
									{getFieldDecorator("email", {
										initialValue: staffData && staffData.email,
										rules: [
											{
												type: "email",
												message: "The input is not valid Email!",
											},
											{
												required: true,
												message: "Please input your Email!",
											},
										],
									})(<Input />)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditStaff));
