import { Col, Icon, Input, Row, Spin, Form, Select, Button, message } from "antd";
import React from "react";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import appActions from "../../redux/app/actions";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { connect } from "react-redux";

const { changeCurrent } = appActions;
const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { Option } = Select;

class assignWorkerToJob extends React.Component {

    state = {
        workerList: [],
        shipmentTypeData: [],
        pageloading: false
    };
    componentDidMount() {
        this.setState({}, () => {
            this.fetchWorkerList()
            this.fetchAssignShipmentTypeStages()
        });
    }

    fetchWorkerList = () => {
        const data = [];
        let params = {
            company_id: this.props.location.state.company_id
        };
        data["data"] = params
        this.setState({ pageloading: true });
        API.post("api/admin/staff/list-shipment-type", data).then((response) => {
            if (response) {
                if (response.status === 1) {
                    this.setState({
                        workerList: response.data.rows,
                        pageloading: false,
                    });
                } else {
                    this.setState({
                        workerList: [],
                        pageloading: false,
                    });
                }
            }
            else {
                this.setState({
                    workerList: [],
                    pageloading: false,
                });
            }
        });
    };
    fetchAssignShipmentTypeStages = () => {
        const data = [];
        data["data"] = { shipmentId: this.props.location.state.jobId };
        this.setState({ pageloading: true });
        API.post("api/admin/assign-shipment-type-stages", data).then((response) => {
            if (response) {
                if (response.status === 1) {
                    this.setState({
                        shipmentTypeData: response.data,
                        pageloading: false,
                    });
                } else {
                    this.setState({
                        pageloading: false,
                    });
                }
            }
        });
    };

    handleSubmit = (e) => {
        e.preventDefault();
        let formData = new FormData();
        this.props.form.validateFieldsAndScroll((err, values) => {
            if (!err) {
                const data = [];
                formData.append("staff_id", (values.worker_id.key !== undefined) ? values.worker_id.key : "");
                formData.append("stage_id", (values.shipment_type_stage.key !== undefined) ? values.shipment_type_stage.key : "");
                formData.append("role", (values.roles !== undefined) ? values.roles : "");
                this.setState({ pageloading: true });

                data["data"] = formData;

                API.post("api/admin/staff/assign-to-shipment/" + this.props.match.params.id, data)
                    .then((response) => {
                        if (response.status === 1) {
                            this.setState({ pageloading: false });
                            message.success(response.message);
                            this.props.history.goBack();
                        } else {
                            this.setState({ pageloading: false });
                            message.error(response.message);
                        }
                    })
                    .catch((error) => {
                        this.setState({ pageloading: false });
                    });
            }
        });
    };


    onShipmentTypeStageChange = (e) => {
        const result = this.state.shipmentTypeData.find(stage => {
            if (stage.local_shipment_stage_id == e.key) {
                return stage;
            }
        });
    }


    render() {

        const { getFieldDecorator } = this.props.form;

        const formItemLayout = {
            labelCol: {
                xs: {
                    span: 24,
                },
                sm: {
                    span: 5,
                },
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: {
                    span: 12,
                    offset: 1,
                },
                md: {
                    span: 12,
                    offset: 1,
                },
                lg: {
                    span: 12,
                    offset: 1,
                },
                xl: {
                    span: 10,
                    offset: 1,
                },
            },
        };
        const tailFormItemLayout = {
            wrapperCol: {
                xs: {
                    span: 24,
                    offset: 6,
                },
                sm: {
                    span: 16,
                    offset: 6,
                },
                xl: {
                    offset: 6,
                },
            },
        };
        return (
            <LayoutContentWrapper>
                <div className='top_header' style={{ height: "100%" }}>
                    <Row>
                        <Col sm={12}>
                            <h2 style={{ marginBottom: "0" }}>
                                <Icon type='user' />
                                &emsp;Assign Worker
                            </h2>
                        </Col>
                        <Col
                            sm={12}
                            style={{
                                textAlign: "right",
                            }}>
                            <button className='backButton' onClick={() => this.props.history.goBack()}>
                                <i className='fa fa-chevron-left' aria-hidden='true' /> Back
                            </button>
                        </Col>
                    </Row>
                </div>
                <Spin spinning={this.state.pageloading} indicator={antIcon}>
                    <LayoutContent>
                        <Form {...formItemLayout} onSubmit={this.handleSubmit}>

                            <Form.Item label='Shipment Type Stage'>
                                {getFieldDecorator("shipment_type_stage", {
                                    rules: [
                                        {
                                            required: true,
                                            message: "Please select shipment type stage",
                                        },
                                    ],
                                })(
                                    <Select
                                        size={"default"}
                                        showSearch
                                        labelInValue
                                        optionFilterProp='children'
                                        onChange={(e) => {
                                            this.onShipmentTypeStageChange(e);
                                        }}
                                        placeholder='Select Shipment Type Stage'
                                        style={{ width: "100%" }}>
                                        {this.state.shipmentTypeData
                                            ? this.state.shipmentTypeData.map((e) => (
                                                <Option key={e.local_shipment_stage_id} value={e.local_shipment_stage_id}>
                                                    {e.name}
                                                </Option>
                                            ))
                                            : null}
                                    </Select>
                                )}
                            </Form.Item>

                            <Form.Item label='Select Worker'>
                                {getFieldDecorator("worker_id", {
                                    rules: [
                                        {
                                            required: true,
                                            message: "Please select worker name",
                                        },
                                    ],
                                })(
                                    <Select
                                        size={"default"}
                                        showSearch
                                        labelInValue
                                        optionFilterProp='children'
                                        placeholder='Select worker Name'
                                        style={{ width: "100%" }}
                                    >
                                        {this.state.workerList
                                            ? this.state.workerList.map((e) => (
                                                <Option
                                                    key={e.staff_id}
                                                    value={e.staff_id}
                                                >
                                                    {e.full_name}
                                                </Option>
                                            ))
                                            : null}
                                    </Select>
                                )}
                            </Form.Item>

                            <Form.Item label='Role'>
                                {getFieldDecorator("roles", {
                                    rules: [{ required: true, message: "Please select role" }],
                                })(
                                    <Select placeholder='Select role' onChange={this.OnChangeSelectUser}>
                                        <Option value='supervisor'>Supervisor</Option>
                                        <Option value='worker'>Worker</Option>
                                    </Select>
                                )}
                            </Form.Item>

                            <Form.Item {...tailFormItemLayout}>
                                <Button
                                    className='submitButton'
                                    loading={this.state.pageloading}
                                    type='primary'
                                    htmlType='submit'>
                                    Submit
                                </Button>
                            </Form.Item>
                        </Form>
                    </LayoutContent>
                </Spin>
            </LayoutContentWrapper>
        );
    }
}

export default Form.create()(connect(null, { changeCurrent })(assignWorkerToJob));
