import { Checkbox, Col, Icon, Input, message, Row, Spin, Table, Button, Tooltip, Modal } from "antd";
import React from "react";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";

const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let deleteStaffId

export default class assignWorkerManagement extends React.Component {
	state = {
		apiParam: {},
		assignWorkerList: [],
		shipmentTypeStages: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		assignModal: false,
		loading: false,
		confirmLoading: false,
		assignRoleName: "worker",
		supervisorStaffId: null,
		deleteModal: false
	};
	componentDidMount() {
		let params = {
			search: "",
			orderBy: "created_at",
			pageNo: "1",
			orderSequence: "DESC",
			pageSize: 25,
			company_id: this.props.location.state.company_id,
			// company_id: 28,
		};
		this.setState({ apiParam: params }, () => {
			this.fetchAssignWorkerList()
			this.fetchAssignShipmentTypeStages()
		});
	}
	fetchAssignWorkerList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		this.setState({ pageloading: true });
		API.post("api/admin/staff/list/" + this.props.match.params.id, data).then((response) => {
			const pagination = { ...this.state.pagination };
			if (response) {
				if (response.status === 1) {
					pagination.total = response.data.count;
					this.setState({
						assignWorkerList: response.data.rows,
						pagination,
						pageloading: false,
					});
				} else {
					this.setState({
						usersList: response.data,
						pagination,
						pageloading: false,
					});
					// message.error(res.message)
				}
			}
		});
	};

	fetchAssignShipmentTypeStages = () => {
		const data = [];
		data["data"] = { shipmentId: this.props.location.state.jobId };
		this.setState({ pageloading: true });
		API.post("api/admin/assign-shipment-type-stages", data).then((response) => {
			if (response) {
				if (response.status === 1) {
					this.setState({
						shipmentTypeStages: response.data,
						pageloading: false,
					});
				} else {
					this.setState({
						pageloading: false,
					});
				}
			}
		});
	};
	handleChange = (pagination, filters, sorter) => {
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		let params = {
			...this.state.apiParam,
			pageNo: pagination.current,
			orderBy:
				sorter.field === "createdAt"
					? "created_at"
					: sorter.field === "company.company_name"
						? "company_name"
						: sorter.field,
			// orderTable: sorter.field === "company.company_name" ? "staff_company" : "staff",
			orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
			pageSize: pagination.pageSize,
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(null, this.state.search) : this.fetchAssignWorkerList();
			}
		);
	};

	// assign(id, value, workerId, roleName) {
	// 	this.setState({
	// 		assignRoleName: "worker",
	// 	});
	// 	assignWorkerId = id;
	// 	jobWorkerId = workerId;
	// 	assignRole = value;
	// 	this.handleOk();
	// }

	handleCancel = () => {
		this.fetchAssignWorkerList();
		this.setState({
			assignModal: false,
			confirmLoading: false,
		});
	};
	handletoggle = (assignRole, unassignRole, assignId, assignWorkerId, stageId) => {
		const assignData = [];
		assignData["data"] = {
			staff_id: assignId,
			stageId,
			role: assignRole,
		};
		const assignUnlinkData = [];
		assignUnlinkData["data"] = {
			staff_worker_id: assignWorkerId,
			stageId,
			role: unassignRole,
		};

		API.put("api/admin/shipment/" + this.props.match.params.id + "/unlink/", assignUnlinkData).then(
			(response) => {
				if (response) {
					this.fetchAssignWorkerList();
					// this.setState({
					// 	loading: false,
					// });
					// message.success(response.message);
				} else {
					// this.setState({
					// 	loading: false,
					// });
					// message.error(response.message);
				}
			}
		);
		API.put("api/admin/shipment/" + this.props.match.params.id + "/link/", assignData).then(
			(response) => {
				if (response) {
					this.fetchAssignWorkerList();
					// this.setState({
					// 	loading: false,
					// });
					// message.success(response.message);
				} else {
					// this.setState({
					// 	loading: false,
					// });
					message.error(response.message);
				}
			}
		);
	};
	checkboxHandle = async (assignTitle, assignId, stageId, assignValue, assignWorkerId, getRole) => {
		const id = assignId;
		const workerId = assignWorkerId;

		const assignData = [];
		assignData["data"] = {
			staff_id: id,
			stageId,
			role: assignTitle === "AssignSupervisor" ? "supervisor" : "worker",
		};

		const assignUnlinkData = [];
		assignUnlinkData["data"] = {
			staff_worker_id: workerId,
			stageId,
			role: assignTitle === "AssignSupervisor" ? "supervisor" : "worker",
		};

		if (assignValue === 0) {
			API.put("api/admin/shipment/" + this.props.match.params.id + "/link/", assignData).then(
				(response) => {
					if (response) {
						this.fetchAssignWorkerList();
						// this.setState({
						// 	loading: false,
						// });
						// message.success(response.message);
					} else {
						// this.setState({
						// 	loading: false,
						// });
						message.error(response.message);
					}
				}
			);
		} else if (
			assignTitle === "AssignWorker" &&
			getRole !== "worker" &&
			assignValue === 1 &&
			assignWorkerId !== null
		) {
			this.handletoggle("worker", "supervisor", assignId, assignWorkerId, stageId);
		} else if (
			assignTitle === "AssignSupervisor" &&
			getRole !== "supervisor" &&
			assignValue === 1 &&
			assignWorkerId !== null
		) {
			this.handletoggle("supervisor", "worker", assignId, assignWorkerId, stageId);
		} else {
			API.put("api/admin/shipment/" + this.props.match.params.id + "/unlink/", assignUnlinkData).then(
				(response) => {
					if (response) {
						this.fetchAssignWorkerList();
						// this.setState({
						// 	loading: false,
						// });
						// message.success(response.message);
					} else {
						// this.setState({
						// 	loading: false,
						// });
						message.error(response.message);
					}
				}
			);
		}
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchAssignWorkerList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e.target.value,
				},
				search: e.target.value,
			},
			this.callback
		);
	};

	assignWorker() {
		this.props.history.push({
			pathname: `${this.props.match.url}/add-worker`,
			state: {
				company_id: this.props.location.state.company_id,
				jobId: this.props.match.params.id
			},
		});
	}

	delete(id) {
		this.setState({ deleteModal: true });
		deleteStaffId = id;
	}

	handleCancel = () => {
		this.setState({ deleteModal: false, pageloading: false });
	};
	handleOk = () => {
		const deleteData = [];
		deleteData["data"] = { assignId: deleteStaffId };
		this.setState({ pageloading: true });
		API.post(`api/admin/staff/delete-assign-worker-stage`, deleteData).then((response) => {
			if (response && response.status == 1) {
				this.fetchAssignWorkerList();	
				message.success(response.message);
			} else {
				message.error(response.message);
			}
			this.setState({
				deleteModal: false,
				pageloading: false,
			});
		});
	};

	render() {
		const columns = [
			{
				title: "User Name",
				dataIndex: "full_name",
				key: "full_name",
				align: "left",
				width: 100
			},
			{
				title: "User Email",
				dataIndex: "email",
				key: "email",
				align: "left",
				width: 100
			},
			{
				title: "User Role",
				dataIndex: "role",
				key: "role",
				align: "left",
				width: 50
			},
			{
				title: "Action",
				key: "action",
				width: 50,
				align: "center",
				render: (record, text) => {
					return (
						<Tooltip title='Delete'>
							<Button
								type='primary'
								className='c-btn c-round c-danger'
								icon='delete'
								onClick={() => this.delete(text.assign_job_worker_id)}></Button>
						</Tooltip>
					);
				},
			},

			// {
			// 	title: "Assign Supervisor",
			// 	dataIndex: "shipment_job_assign_worker_lists.role",
			// 	key: "shipment_job_assign_worker_lists.role",
			// 	render: (record, text) => {
			// 		return (
			// 			<div>
			// 				<Checkbox
			// 					name='supervisor'
			// 					defaultChecked={text["shipment_job_assign_worker_lists.role"] === "supervisor" && text["shipment_job_assign_worker_lists.local_shipment_stage_id"] == text.stage.local_shipment_stage_id ? true : false}
			// 					checked={text["shipment_job_assign_worker_lists.role"] === "supervisor" && text["shipment_job_assign_worker_lists.local_shipment_stage_id"] == text.stage.local_shipment_stage_id ? true : false}
			// 					onClick={() =>
			// 						this.checkboxHandle(
			// 							"AssignSupervisor",
			// 							text.staff_id,
			// 							text.stage.local_shipment_stage_id,
			// 							text["shipment_job_assign_worker_lists.local_shipment_stage_id"] == text.stage.local_shipment_stage_id ? text["shipment_job_assign_worker_lists.is_assigned"] : 0,
			// 							text["shipment_job_assign_worker_lists.assign_job_worker_id"],
			// 							text["shipment_job_assign_worker_lists.role"],
			// 							text["shipment_job_assign_worker_lists.role"] === "supervisor" ||
			// 								text["shipment_job_assign_worker_lists.role"] === "worker"
			// 								? true
			// 								: false
			// 						)
			// 					}
			// 				/>
			// 			</div>
			// 		);
			// 	},
			// },
			// {
			// 	title: "Assign Workers",
			// 	dataIndex: "shipment_job_assign_worker_lists.role",
			// 	key: "shipment_job_assign_worker_lists.is_assigned",
			// 	render: (record, text) => {
			// 		return (
			// 			<div>
			// 				<Checkbox
			// 					name='worker'
			// 					defaultChecked={text["shipment_job_assign_worker_lists.role"] === "worker" && text["shipment_job_assign_worker_lists.local_shipment_stage_id"] == text.stage.local_shipment_stage_id ? true : false}
			// 					checked={text["shipment_job_assign_worker_lists.role"] === "worker" && text["shipment_job_assign_worker_lists.local_shipment_stage_id"] == text.stage.local_shipment_stage_id ? true : false}
			// 					onClick={() =>
			// 						this.checkboxHandle(
			// 							"AssignWorker",
			// 							text.staff_id,
			// 							text.stage.local_shipment_stage_id,
			// 							text["shipment_job_assign_worker_lists.local_shipment_stage_id"] == text.stage.local_shipment_stage_id ? text["shipment_job_assign_worker_lists.is_assigned"] : 0,
			// 							text["shipment_job_assign_worker_lists.assign_job_worker_id"],
			// 							text["shipment_job_assign_worker_lists.role"],
			// 							text["shipment_job_assign_worker_lists.role"] === "supervisor" ||
			// 								text["shipment_job_assign_worker_lists.role"] === "worker"
			// 								? true
			// 								: false
			// 						)
			// 					}
			// 				/>
			// 			</div>
			// 		);
			// 	},
			// },
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<h3
							style={{
								display: "flex",
								textTransform: "capitalize",
								marginLeft: "5px",
								padding: "10px 0",
							}}>
							{this.props.location.state.name}&nbsp;&nbsp;
							{this.props.location.state.shipment}&nbsp; #{this.props.location.state.jobNumber}
						</h3>
						<Col sm={12}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' />
								&emsp;Worker Management
							</h2>
						</Col>
						<Col sm={12}>
							<Button
								className='addButton'
								style={{
									marginTop: "0",
								}}
								type='primary'
								onClick={() => this.assignWorker()}>
								Assign Worker
							</Button>
						</Col>
					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>

					<LayoutContent>
						{this.state.shipmentTypeStages.length > 0 && this.state.shipmentTypeStages.map((stage, index) =>
						(
							<div key={`table-${index}`} >
								<h2>
									{stage.name}
								</h2>

								<Table
									pagination={false}
									bordered={true}
									columns={columns}
									dataSource={this.state.assignWorkerList.filter(data => data.local_shipment_stage_id === stage.local_shipment_stage_id)}
								/>

							</div>
						))}
					</LayoutContent>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to unassign this worker?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}
