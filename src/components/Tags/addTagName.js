import React from "react";
import { connect } from "react-redux";

import { Button, Form, Icon, Input, Spin, message, Radio } from "antd";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import appActions from "../../redux/app/actions";
// import { DownloadOutline } from "@ant-design/icons";
import { CirclePicker } from "react-color";
import "../../static/css/add.css";
import Api from "../../api/api-handler";

const { changeCurrent } = appActions;
// const { Option } = Select;
const tagsOption = [
	{ label: "Customer", value: "CUSTOMER" },
	{ label: "Shipment", value: "SHIPMENT" },
	{ label: "Item", value: "ITEM" },
];
// const tagsColorOption = [
// 	{ label: "", value: "#FF0000" },
// 	{ label: "", value: "#000000" },
// ];
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddTagName extends React.Component {
	state = {
		loading: false,
		contentloader: false,
		companyList: [],
		color_hex: "",
		adminId: "",
		companyId: "",
		staffId: "",
	};

	componentDidMount() {
		const data = [];
		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});

		API.post("api/admin/company/list", data)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							companyList: response.data.companyList.rows,
							contentloader: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							contentloader: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}

	handleSubmit = (e) => {
		e.preventDefault();

		this.props.form.validateFieldsAndScroll((err, values) => {
			let params = {
				name: values.name,
				tag_for: values.tags_for,
				color: values.tag_color && values.tag_color.hex,
				admin_id:this.state.adminId,
				company_id:this.state.companyId,
				staff_id:this.state.staffId,

			};
			if (!err) {
				const data = [];

				data["data"] = params;
				this.setState({ loading: true });
				API.post("api/admin/tag", data)
					.then((response) => {
						if (response.status === 1) {
							this.setState({ loading: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						message.error(error.message);
						this.setState({ loading: false });
					});
			}
		});
	};
	setFieldValue = (name, code) => {
		this.setState({ color_hex: code });
	};
	render() {


		const { getFieldDecorator } = this.props.form;
		const { color_hex } = this.state;

		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add Tag
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent
					style={{
						margin: "0 20px",
						height: "93%",
						overflowY: "auto",
					}}>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Name'>
									{getFieldDecorator("name", {
										placeholder: "Tag Name",
										rules: [
											{
												required: true,
												message: "Please input tag name",
												whitespace: true,
											},
										],
									})(<Input placeholder='Tag Name' />)}
								</Form.Item>
								<Form.Item name='tag_color' label='Tag color'>
									{getFieldDecorator("tag_color", {
										rules: [
											{
												required: true,
												message: "Please input tag color",
											},
										],
									})(
										<CirclePicker
											width={"100%"}
											color={color_hex !== "" ? color_hex : ""}
											onChangeComplete={(color, event) => this.setFieldValue("tag_color", color.hex)}
										/>
									)}
								</Form.Item>
								<Form.Item name='tags_for' label='Type' valuePropName='checked'>
									{getFieldDecorator("tags_for", {
										rules: [
											{
												required: true,
												message: "Please input type",
												whitespace: true,
											},
										],
									})(<Radio.Group options={tagsOption} />)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Add Tag
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(AddTagName));
