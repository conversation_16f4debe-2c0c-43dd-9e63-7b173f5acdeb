import { Button, Col, Icon, Input, message, Modal, Row, Spin, Table, Tag, Tooltip } from "antd";
import React from "react";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import queryString from 'query-string';


const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let tagId, tagType;

export default class tagManagement extends React.Component {
	state = {
		apiParam: {},
		tags: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		adminId: "",
		companyId: "",
		staffId: "",

	};
	componentDidMount() {
		const queryParams = queryString.parse(window.location.search);
		let params = {
			search: "",
			order_by_fields: "",
			page_no: parseInt(queryParams.page) || "1",
			order_sequence: "DESC",
			page_size: 25,
		};
		this.setState({ apiParam: params }, () => this.fetchTags());
		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});
	}
	fetchTags = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		const { search, page_no, order_sequence, page_size, order_by_fields } = this.state.apiParam;
		this.setState({ pageloading: true });
		API.get(
			`api/admin/tag/list?order_by_fields=${order_by_fields}&order_sequence=${order_sequence}&page_no=${page_no}&search=${search}&page_size=${page_size}`
		)
			.then((response) => {

				const pagination = { ...this.state.pagination };
				if (response) {


					if (response.status === 1) {
						pagination.total = response.data.count;


						this.setState({
							tags: response.data.rows,
							pagination,
							pageloading: false,
						});



					} else {
						this.setState({
							usersList: response.data,
							pagination,
							pageloading: false,
						});
					}
				}
			})
			.catch((error) => {
				//console.log(error);
				this.setState({ pageloading: false });
			});
	};
	handleChange = (pagination, filters, sorter) => {
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		const queryParams = queryString.parse(window.location.search);
		queryParams.page = pagination.current;
		const newUrl = `${window.location.pathname}?${queryString.stringify(queryParams)}`;
		window.history.pushState(null, null, newUrl);
		let params = {
			...this.state.apiParam,
			page_no: pagination.current,
			order_by_fields: sorter.field ? sorter.field : "name",
			order_sequence: sorter.order === "ascend" ? "ASC" : "DESC",
			page_size: pagination.pageSize,
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchTags();
			}
		);
	};
	addRoom() {
		this.props.history.push(`${this.props.match.url}/add`);
	}
	delete(id, type) {
		this.setState({ deleteModal: true });
		tagId = id;
		tagType = type
	}
	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}

	handleCancel = () => {
		this.setState({ deleteModal: false, confirmLoading: false });
	};
	handleOk = () => {
		const id = tagId;
		let params = {
			type: tagType
		};
		const data = [];
		data["data"] = params;
		this.setState({ confirmLoading: true });
		API.delete(`api/admin/tag/${id}`, data).then((response) => {
			if (response && response.status == 1) {
				this.fetchTags();
				message.success(response.message);
			} else {
				message.error(response.message);
			}
			this.setState({
				deleteModal: false,
				confirmLoading: false,
			});
		});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchTags, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					page_no: 1,
				},
				search: e,
			},
			this.callback
		);
	};
	render() {
		const columns = [
			{
				title: "Tag Name",
				dataIndex: "name",
				key: "name",
				sorter: true,

			},
			{
				title: "Type",
				dataIndex: "tag_for",
				key: "tag_for",
				sorter: true,
			},
			{
				title: "Tag",
				dataIndex: "name",
				key: "name",
				render: (record, text) => <>{record ? <Tag color={text["color"]}>{record}</Tag> : ""}</>,
			},
			{
				title: "Action",
				key: "action",
				render: (record, text) => {
					return (
						<div className='icons' style={{ display: "flex", justifyContent: "flex-start" }}>


							<Tooltip title='Edit'>
								<Button
									type='primary'
									className='c-btn c-round c-warning'
									icon='edit'
									onClick={() => this.edit(text.tag_id)}></Button>
							</Tooltip>
							<Tooltip title='Delete'>
								<Button
									type='primary'
									className='c-btn c-round c-danger'
									icon='delete'
									onClick={() => this.delete(text.tag_id, text.tag_for)}></Button>
							</Tooltip>

						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<Col sm={8}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' /> &emsp;Tag Management
							</h2>
						</Col>
						<Col sm={6}>
							{this.state.search ? (
								<p
									style={{
										margin: "5px",
										textAlign: "center",
										color: "#303030",
									}}>
									Matching Records: {this.state.pagination.total}
								</p>
							) : (
								""
							)}
						</Col>

						<Col sm={10}>
							<Row>
								<Col sm={16} style={{ textAlign: "right" }}>
									<Search
										placeholder='Search tag name'
										onChange={(e) => this.handleSearch(e.target.value)}
										value={this.state.search}
										style={{ width: 200 }}
									/>
								</Col>
								{
									<Col sm={8}>
										<Button
											className='addButton'
											style={{ marginTop: "0" }}
											type='primary'
											onClick={() => this.addRoom()}>
											+ Add Tag
										</Button>
									</Col>
								}
							</Row>
						</Col>
					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div style={{ marginTop: "-1.5rem", overflowX: "auto" }}>
							<Table
								bordered={true}
								columns={columns}
								pagination={{
									// pageSize: 10,
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 25,
									pageSizeOptions: ["25", "50"],
									current: this.state.apiParam.page_no
								}}
								dataSource={this.state.tags}
								onChange={this.handleChange}
							/>
						</div>
					</LayoutContent>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to delete this tag?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}
