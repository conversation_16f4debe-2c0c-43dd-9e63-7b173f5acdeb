import { Button, Form, Icon, Input, message, Radio, Spin } from "antd";
import React from "react";
import { CirclePicker } from "react-color";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";

const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const tagsOption = [
	{ label: "Customer", value: "CUSTOMER" },
	{ label: "Shipment", value: "SHIPMENT" },
	{ label: "Item", value: "ITEM" },
];

class EditTagName extends React.Component {
	state = {
		tagData: null,
		loading: false,
		contentloader: false,
		companyList: [],
		color_hex: "",
	};
	componentDidMount() {
		const id = this.props.match.params.id;
		this.props.changeCurrent("tag");
		this.setState({ contentloader: true });
		API.get(`api/admin/tag/${id}`)
			.then((response) => {
				if (response) {
					this.setState(
						{
							tagData: response.data,
							color_hex: response.data.color,
						},
						() => this.props.form.setFieldsValue({})
					);
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
		API.post("api/admin/company/list")
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							companyList: response.data.companyList.rows,
							contentloader: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							contentloader: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}
	handleSubmit = (e) => {
		e.preventDefault();
		const id = this.props.match.params.id;
		let formData = new FormData();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];
				formData.append("name", values.name);
				formData.append("tag_for", values.tags_for);
				formData.append("color", this.state.color_hex);

				data["data"] = formData;
				this.setState({ loading: true });

				API.put(`api/admin/tag/${id}`, data)
					.then((response) => {
						if (response) {
							this.setState({ loading: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false });
					});
			}
		});
	};
	setFieldValue = (name, code) => {
		this.setState({ color_hex: code });
	};
	render() {
		const { getFieldDecorator } = this.props.form;
		const { tagData, color_hex } = this.state;
		// let initialColor = null;
		// if (tagData !== null) {
		// 	initialColor =
		// 		tagData && tagData !== null && tagData.color === ""
		// 			? {}
		// 			: tagData.color;
		// }
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};
		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i className='fas fa-edit' />
						&emsp;Edit Tag
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Tag name'>
									{getFieldDecorator("name", {
										initialValue: tagData && tagData.name,
										rules: [
											{
												required: true,
												message: "Please input tag name!",
												whitespace: true,
											},
										],
									})(<Input />)}
								</Form.Item>
								<Form.Item name='tag_color' label='Tag color'>
									{getFieldDecorator("tag_color", {
										rules: [
											{
												required: true,
												message: "Please input tag color!",
											},
										],
										initialValue: color_hex !== "" ? color_hex : "",
									})(
										<CirclePicker
											width={"100%"}
											color={color_hex !== "" ? color_hex : ""}
											onChangeComplete={(color, event) => this.setFieldValue("tag_color", color.hex)}
										/>
									)}
								</Form.Item>
								<Form.Item name='tags_for' label='Type'>
									{getFieldDecorator("tags_for", {
										initialValue: tagData && tagData.tag_for,
										rules: [
											{
												required: true,
												message: "Please input type",
												whitespace: true,
											},
										],
									})(<Radio.Group options={tagsOption} />)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditTagName));
