import { Button, Form, Icon, Input, message, Select, Spin, Upload } from "antd";
import React from "react";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';


const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { TextArea } = Input;
const { Option } = Select;

class EditGroupPassword extends React.Component {
    state = {
        contentloader: false
    }

    compareToFirstPassword = (rule, value, callback) => {
        const form = this.props.form;
        if (value && value !== form.getFieldValue("password")) {
            callback("The password confirmation does not match!");
        } else {
            callback();
        }
    };

    validateToNextPassword = (rule, value, callback) => {
        const form = this.props.form;
        if (value && this.state.confirmDirty) {
            form.validateFields(["confirm"], { force: true });
        }
        callback();
    };

    handleSubmit = (e) => {
        e.preventDefault();
        this.props.form.validateFieldsAndScroll((err, values) => {
            if (!err) {
                let params = {
                    company_id: this.props.match.params.id,
                    new_password: values.password,
                    code: values.resetpasswordcode,
                    confirm_password: values.confirm,
                };
                console.log("params", params);
                const data = [];
                data["data"] = params;
                this.setState({ contentloader: true });
                API.post("api/admin/cms/update-company-password", data)
                    .then((response) => {
                        if (response) {
                            this.setState({ contentloader: false });
                            message.success(response.message);
                            this.props.history.goBack();
                        } else {
                            this.setState({ contentloader: false });
                            message.error(response.message);
                        }
                    })
                    .catch((error) => {
                        this.setState({ contentloader: false });
                    });
            }
        });
    };


    render() {
        const { getFieldDecorator } = this.props.form;

        const formItemLayout = {
            labelCol: {
                xs: {
                    span: 24,
                },
                sm: {
                    span: 5,
                },
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: {
                    span: 12,
                    offset: 1,
                },
                md: {
                    span: 12,
                    offset: 1,
                },
                lg: {
                    span: 12,
                    offset: 1,
                },
                xl: {
                    span: 10,
                    offset: 1,
                },
            },
        };
        const tailFormItemLayout = {
            wrapperCol: {
                xs: {
                    span: 24,
                    offset: 6,
                },
                sm: {
                    span: 16,
                    offset: 6,
                },
                xl: {
                    offset: 6,
                },
            },
        };



        return (
            <LayoutContentWrapper>
                <div className='add_header'>
                    <h2 style={{ marginBottom: "0" }}>
                        <i class='fas fa-edit' />
                        &emsp;Reset Group Password
                    </h2>
                    <button className='backButton' onClick={() => this.props.history.goBack()}>
                        <i className='fa fa-chevron-left' aria-hidden='true' /> Back
                    </button>
                </div>
                <LayoutContent>
                    <div>
                        <Spin spinning={this.state.contentloader} indicator={antIcon}>
                            <Form {...formItemLayout} onSubmit={this.handleSubmit}>

                                <Form.Item label='Password' hasFeedback>
                                    {getFieldDecorator("password", {
                                        rules: [
                                            {
                                                required: true,
                                                message: "Please enter your password!"
                                            }
                                            ,
                                            {
                                                validator: this.validateToNextPassword,
                                            }
                                        ],
                                    })(<Input.Password placeholder='Enter Password' />)}
                                </Form.Item>

                                <Form.Item label='Confirm' hasFeedback>
                                    {getFieldDecorator("confirm", {
                                        rules: [
                                            {
                                                required: true,
                                                message: "Please enter your password!"
                                            }
                                            ,
                                            {
                                                validator: this.compareToFirstPassword,
                                            }
                                        ],
                                    })(<Input.Password placeholder='Confirm Password' />)}
                                </Form.Item>

                                <Form.Item {...tailFormItemLayout}>
                                    <Button
                                        className='submitButton'
                                        loading={this.state.loading}
                                        type='primary'
                                        htmlType='submit'>
                                        Submit
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Spin>
                    </div>
                </LayoutContent>
            </LayoutContentWrapper>
        );
    }
}

export default Form.create()(connect(null, { changeCurrent })(EditGroupPassword));
