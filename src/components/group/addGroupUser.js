import { Button, Form, Icon, Input, message, Select, Spin, Upload } from "antd";
import React from "react";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";



const { TextArea } = Input;
const { Option } = Select;
// alert('hi');
const { changeCurrent } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddGroupUser extends React.Component {
    state = {
        loading: false,
        contentloader: false,
        previewVisible: false,
        previewImage: "",
        fileList: [],
        groupList: [],
        groupDataFromField: null,

    };

    componentDidMount() {

        this.setState({
            loading: true,
            contentloader: true
        });

        API.get(
            `api/admin/group/list`
        )
            .then((response) => {
                if (response.status === 1) {
                    this.setState({
                        groupList: response.data.rows,
                        loading: false,
                        contentloader: false
                    });

                }
                else {
                    this.setState({
                        groupList: [],
                        loading: false,
                        contentloader: false
                    });
                }

            })
            .catch((error) => {
                this.setState({
                    ploading: false,
                    contentloader: false
                });
            });
    }

    handlePreview = (file) => {
        this.setState({
            previewImage: file.thumbUrl,
            previewVisible: true,
        });
    };
    handleUpload = ({ fileList }) => {
        // you store them in state, so that you can make a http req with them later
        if (fileList.length > 1) {
            message.error("Only one image is allowed!");
        } else {
            this.setState({ fileList });
        }
    };

    OnGroupChange = (values) => {
        this.setState({ groupDataFromField: values });
    };

    handleSubmit = (e) => {
        e.preventDefault();
        let formData = new FormData();

        this.props.form.validateFieldsAndScroll((err, values) => {
            if (!err) {
                const data = [];
                formData.append("company_name", (values.company_name !== undefined) ? values.company_name : "");
                formData.append("company_identity", (values.company_identity !== undefined) ? values.company_identity.replace(/\s/g, "") : "");
                formData.append("email", (values.email !== undefined) ? values.email : "");
                formData.append("phone", (values.phone !== undefined) ? values.phone : "");
                formData.append("group_id", this.props.match.params.id);
                formData.append("password", values.password);
                data["data"] = formData;
                this.setState({ loading: true, contentloader: true });

                API.post("api/admin/create/superAdminStaff", data)
                    .then((response) => {
                        if (response.status === 1) {
                            this.setState({ loading: false, contentloader: false });
                            message.success(response.message);
                            this.props.history.goBack();
                        } else {
                            this.setState({ loading: false, contentloader: false });
                            message.error(response.message);
                        }
                    })
                    .catch((error) => {
                        this.setState({ loading: false, contentloader: false });
                    });
            }
        });
    };

    normFile = (e) => {
        if (Array.isArray(e)) {
            return e;
        }
        return e && e.fileList;
    };

    render() {
        const { getFieldDecorator } = this.props.form;

        const formItemLayout = {
            labelCol: {
                xs: {
                    span: 24,
                },
                sm: {
                    span: 5,
                },
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: {
                    span: 12,
                    offset: 1,
                },
                md: {
                    span: 12,
                    offset: 1,
                },
                lg: {
                    span: 12,
                    offset: 1,
                },
                xl: {
                    span: 10,
                    offset: 1,
                },
            },
        };
        const tailFormItemLayout = {
            wrapperCol: {
                xs: {
                    span: 24,
                    offset: 6,
                },
                sm: {
                    span: 16,
                    offset: 6,
                },
                xl: {
                    offset: 6,
                },
            },
        };

        const countryCodeSelector = getFieldDecorator("country_code", {
            initialValue: "1",
        })(
            <Select disabled style={{ width: 70 }}>
                <Option value='1'>+1</Option>
            </Select>
        );
        return (
            <LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
                <div className='add_header'>
                    <h2 style={{ marginBottom: "0" }}>
                        <i class='fas fa-plus'></i>&emsp;Add Group User
                    </h2>
                    <button className='backButton' onClick={() => this.props.history.goBack()}>
                        <i className='fa fa-chevron-left' aria-hidden='true'></i> Back
                    </button>
                </div>
                <LayoutContent
                    style={{
                        margin: "0 20px",
                        height: "93%",
                        overflowY: "auto",
                    }}>
                    <div>
                        <Spin spinning={this.state.contentloader} indicator={antIcon}>
                            <Form {...formItemLayout} onSubmit={this.handleSubmit}>
                                <Form.Item label='Name'>
                                    {getFieldDecorator("company_name", {
                                        placeholder: "Enter Name",
                                        rules: [
                                            {
                                                required: true,
                                                message: "Please input name!",
                                                whitespace: true,
                                            },
                                        ],
                                    })(<Input placeholder='Name' />)}
                                </Form.Item>
                                <Form.Item label='Company ID'>
                                    {getFieldDecorator("company_identity", {
                                        placeholder: "User ID",
                                        rules: [
                                            {
                                                required: true,
                                                message: "Please input company id!",
                                                whitespace: true,
                                            },
                                        ],
                                    })(<Input placeholder='Company ID' />)}
                                </Form.Item>

                                <Form.Item label='Email'>
                                    {getFieldDecorator("email", {
                                        rules: [
                                            {
                                                type: "email",
                                                message: "The input is not valid Email!",
                                            },
                                            {
                                                required: true,
                                                message: "Please input your Email!",
                                            },
                                        ],
                                    })(<Input placeholder='Email' />)}
                                </Form.Item>

                                <Form.Item label='Phone Number'>
                                    {getFieldDecorator("phone", {
                                    })(
                                        <Input
                                            style={{ width: "100%" }}
                                            placeholder='User Phone Number'
                                            customInput={Input}
                                            maxLength={20}
                                        />
                                    )}
                                </Form.Item>

                                <Form.Item label='Password' hasFeedback>
                                    {getFieldDecorator("password", {
                                        rules: [{
                                            required: true,
                                            message: "Please enter your password!"
                                        }],
                                    })(<Input.Password placeholder='Enter Password' />)}
                                </Form.Item>

                                <Form.Item {...tailFormItemLayout}>
                                    <Button
                                        className='submitButton'
                                        loading={this.state.loading}
                                        type='primary'
                                        htmlType='submit'>
                                        Add Group User
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Spin>
                    </div>
                </LayoutContent>
            </LayoutContentWrapper>
        );
    }
}

export default Form.create()(connect(null, { changeCurrent })(AddGroupUser));
