import { Button, Form, Icon, Input, InputNumber, message, Spin, Checkbox } from "antd";
import React from "react";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";

const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});

class EditGroup extends React.Component {
	state = {
		groupData: null,
		loading: false,
		contentloader: false,
		adminId: "",
		companyId: "",
		staffId: "",

	};
	componentDidMount() {


		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});

		const id = this.props.match.params.id;
		this.props.changeCurrent("item-list");
		this.setState({ contentloader: true });
		API.get(`api/admin/group/${id}`)
			.then((response) => {
				if (response) {
					this.setState(
						{
							groupData: response.data && response.data,
							contentloader: false,
						},
						() => this.props.form.setFieldsValue({})
					);
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}
	handleSubmit = (e) => {
		e.preventDefault();
		const id = this.props.match.params.id;
		let formData = new FormData();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];
				formData.append("name", values.name);
				formData.append("make_account_id_mandatory", values.make_account_id_mandatory == true ? 1 : 0);
				formData.append("pdf_time_stamp_checked", values.pdf_time_stamp_checked == true ? 1 : 0);
				formData.append("admin_id", this.state.adminId);
				formData.append("company_id", this.state.companyId);
				formData.append("staff_id", this.state.staffId);
				data["data"] = formData;
				this.setState({ loading: true });


				API.put(`api/admin/group/${id}`, data)
					.then((response) => {
						if (response) {
							this.setState({ loading: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false });
					});
			}
		});
	};
	render() {
		const { getFieldDecorator } = this.props.form;
		const { groupData } = this.state;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};
		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Edit Group
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Group name'>
									{getFieldDecorator("name", {
										initialValue: groupData && groupData.name,
										rules: [
											{
												required: true,
												message: "Please input group name!",
												whitespace: true,
											},
										],
									})(<Input />)}
								</Form.Item>

								<Form.Item label='Make Account Id Mandatory'>
									{getFieldDecorator(`make_account_id_mandatory`, {
										valuePropName: "checked",
										initialValue: groupData && groupData.make_account_id_mandatory,
									})(
										<Checkbox>Make Account Id Mandatory</Checkbox>)}
								</Form.Item>


								<Form.Item label='PDF Time Stamp Checked'>
									{getFieldDecorator(`pdf_time_stamp_checked`, {
										valuePropName: "checked",
										initialValue: groupData && groupData.pdf_time_stamp_checked,
									})(
										<Checkbox>PDF Time Stamp Checked</Checkbox>)}
								</Form.Item>


								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditGroup));
