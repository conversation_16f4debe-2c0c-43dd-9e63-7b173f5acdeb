.jp-card.jp-card-amex.jp-card-identified .jp-card-front .jp-card-logo.jp-card-amex,
.jp-card.jp-card-amex.jp-card-identified .jp-card-front:after,
.jp-card.jp-card-dankort.jp-card-identified .jp-card-logo.jp-card-dankort,
.jp-card.jp-card-discover.jp-card-identified .jp-card-logo.jp-card-discover,
.jp-card.jp-card-maestro.jp-card-identified .jp-card-logo.jp-card-maestro,
.jp-card.jp-card-mastercard.jp-card-identified .jp-card-logo.jp-card-mastercard,
.jp-card.jp-card-visa.jp-card-identified .jp-card-logo.jp-card-visa {
  opacity: 1;
}
.jp-card.jp-card-safari.jp-card-identified .jp-card-back:before,
.jp-card.jp-card-safari.jp-card-identified .jp-card-front:before {
  background-image: -webkit-linear-gradient(
    -25deg,
    rgba(255, 255, 255, 0) 50%,
    rgba(255, 255, 255, 0.2) 70%,
    rgba(255, 255, 255, 0) 90%
  );
  background-image: linear-gradient(
    -25deg,
    rgba(255, 255, 255, 0) 50%,
    rgba(255, 255, 255, 0.2) 70%,
    rgba(255, 255, 255, 0) 90%
  );
}
.jp-card.jp-card-ie-10.jp-card-flipped,
.jp-card.jp-card-ie-11.jp-card-flipped {
  -webkit-transform: 0deg;
  -moz-transform: 0deg;
  -ms-transform: 0deg;
  -o-transform: 0deg;
  transform: 0deg;
}
.jp-card.jp-card-ie-10.jp-card-flipped .jp-card-back,
.jp-card.jp-card-ie-10.jp-card-flipped .jp-card-front,
.jp-card.jp-card-ie-11.jp-card-flipped .jp-card-back,
.jp-card.jp-card-ie-11.jp-card-flipped .jp-card-front {
  -webkit-transform: rotateY(0);
  -moz-transform: rotateY(0);
  -ms-transform: rotateY(0);
  -o-transform: rotateY(0);
  transform: rotateY(0);
}
.jp-card.jp-card-ie-10.jp-card-flipped .jp-card-back:after,
.jp-card.jp-card-ie-11.jp-card-flipped .jp-card-back:after {
  left: 18%;
}
.jp-card.jp-card-ie-10.jp-card-flipped .jp-card-back .jp-card-cvc,
.jp-card.jp-card-ie-11.jp-card-flipped .jp-card-back .jp-card-cvc {
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
  left: 5%;
}
.jp-card.jp-card-ie-10.jp-card-flipped .jp-card-back .jp-card-shiny,
.jp-card.jp-card-ie-11.jp-card-flipped .jp-card-back .jp-card-shiny {
  left: 84%;
}
.jp-card.jp-card-ie-10.jp-card-flipped .jp-card-back .jp-card-shiny:after,
.jp-card.jp-card-ie-11.jp-card-flipped .jp-card-back .jp-card-shiny:after {
  left: -480%;
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
}
.jp-card.jp-card-ie-10.jp-card-amex .jp-card-back,
.jp-card.jp-card-ie-11.jp-card-amex .jp-card-back {
  display: none;
}
.jp-card-logo {
  height: 36px;
  width: 60px;
  font-style: italic;
  box-sizing: border-box;
}
.jp-card-logo:after,
.jp-card-logo:before {
  box-sizing: border-box;
}
.jp-card-logo.jp-card-amex {
  text-transform: uppercase;
  font-size: 4px;
  font-weight: 700;
  color: #ffffff;
  border: 1px solid #eeeeee;
}
.jp-card-logo.jp-card-amex:after {
  width: 28px;
  display: block;
  position: absolute;
  left: 16px;
  content: "express";
  bottom: 11px;
  text-align: right;
  padding-right: 2px;
}
.jp-card-logo.jp-card-amex:before {
  width: 28px;
  display: block;
  position: absolute;
  left: 16px;
  height: 28px;
  content: "american";
  top: 3px;
  text-align: left;
  padding-left: 2px;
  padding-top: 11px;
  background: #267ac3;
}
.jp-card-logo.jp-card-discover {
  text-align: center;
  font-weight: 700;
  text-transform: uppercase;
  background: #ff6600;
  color: #111111;
  font-style: normal;
  font-size: 10px;
  overflow: hidden;
  z-index: 1;
  padding-top: 9px;
  letter-spacing: .03em;
  border: 1px solid #eeeeee;
}
.jp-card-logo.jp-card-visa {
  text-align: center;
  font-weight: 700;
  text-transform: uppercase;
  background: #ffffff;
  color: #1a1876;
  font-size: 15px;
  line-height: 18px;
}
.jp-card.jp-card-amex.jp-card-flipped {
  -webkit-transform: none;
  -moz-transform: none;
  -ms-transform: none;
  -o-transform: none;
  transform: none;
}
.jp-card.jp-card-amex.jp-card-identified .jp-card-back:before,
.jp-card.jp-card-amex.jp-card-identified .jp-card-front:before {
  background-color: #108168;
}
.jp-card.jp-card-amex.jp-card-identified .jp-card-front .jp-card-cvc {
  visibility: visible;
}
.jp-card-logo.jp-card-discover:after {
  content: " ";
  content: "network";
  display: block;
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  top: 10px;
  left: 27px;
  background-color: #ffffff;
  background-image: -webkit-radial-gradient(#ffffff, #ff6600);
  background-image: radial-gradient(#ffffff, #ff6600);
  font-size: 4px;
  line-height: 24px;
  text-indent: -7px;
}
.jp-card-logo.jp-card-discover:before {
  content: " ";
  display: block;
  position: absolute;
  background: #ffffff;
  width: 200px;
  height: 200px;
  border-radius: 200px;
  bottom: -5%;
  right: -80%;
  z-index: -1;
}
.jp-card {
  font-family: "Helvetica Neue";
  line-height: 1;
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 315px;
  border-radius: 10px;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -ms-transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transition: all .4s linear;
  -moz-transition: all .4s linear;
  transition: all .4s linear;
}
.jp-card > *,
.jp-card > :after,
.jp-card > :before {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  font-family: inherit;
}
.jp-card .jp-card-back:before,
.jp-card .jp-card-front:before {
  content: " ";
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  border-radius: 10px;
  -webkit-transition: all .4s ease;
  -moz-transition: all .4s ease;
  transition: all .4s ease;
}
.jp-card .jp-card-back .jp-card-display,
.jp-card .jp-card-front .jp-card-display {
  color: #ffffff;
  font-weight: 400;
  opacity: .5;
  -webkit-transition: opacity .4s linear;
  -moz-transition: opacity .4s linear;
  transition: opacity .4s linear;
}
.jp-card .jp-card-back .jp-card-display.jp-card-focused,
.jp-card .jp-card-front .jp-card-display.jp-card-focused {
  opacity: 1;
  font-weight: 500;
}
.jp-card .jp-card-back .jp-card-shiny:before,
.jp-card .jp-card-front .jp-card-shiny:before {
  content: " ";
  display: block;
  width: 70%;
  height: 60%;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  background: #d9d9d9;
  position: absolute;
  top: 20%;
}
.jp-card .jp-card-front {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -ms-transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transition: all .4s linear;
  -moz-transition: all .4s linear;
  transition: all .4s linear;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  border-radius: 10px;
  background: #dddddd;
  z-index: 2;
}
.jp-card .jp-card-front .jp-card-logo.jp-card-discover {
  right: 12%;
  top: 18%;
}
.jp-card .jp-card-front .jp-card-cvc {
  font-family: "Bitstream Vera Sans Mono", Consolas, Courier, monospace;
  font-size: 14px;
}
.jp-card .jp-card-front .jp-card-shiny {
  width: 50px;
  height: 35px;
  border-radius: 5px;
  background: #cccccc;
  position: relative;
}
.jp-card .jp-card-front .jp-card-logo {
  position: absolute;
  opacity: 0;
  right: 5%;
  top: 8%;
  -webkit-transition: .4s;
  -moz-transition: .4s;
  transition: .4s;
}
.jp-card .jp-card-front .jp-card-lower {
  width: 80%;
  position: absolute;
  left: 10%;
  bottom: 30px;
}
.jp-card .jp-card-front .jp-card-lower .jp-card-cvc {
  visibility: hidden;
  float: right;
  position: relative;
  bottom: 5px;
}
.jp-card .jp-card-front .jp-card-lower .jp-card-number {
  font-family: "Bitstream Vera Sans Mono", Consolas, Courier, monospace;
  font-size: 24px;
  clear: both;
  margin-bottom: 30px;
}
.jp-card .jp-card-front .jp-card-lower .jp-card-expiry {
  font-family: "Bitstream Vera Sans Mono", Consolas, Courier, monospace;
  letter-spacing: 0;
  position: relative;
  float: right;
  width: 25%;
}
.jp-card .jp-card-front .jp-card-lower .jp-card-expiry:after {
  font-family: "Helvetica Neue";
  font-weight: 700;
  font-size: 7px;
  white-space: pre;
  display: block;
  opacity: .5;
  position: absolute;
  content: attr(data-after);
  text-align: right;
  right: 100%;
  margin-right: 5px;
  margin-top: 2px;
  bottom: 0;
}
.jp-card .jp-card-front .jp-card-lower .jp-card-expiry:before {
  font-family: "Helvetica Neue";
  font-weight: 700;
  font-size: 7px;
  white-space: pre;
  display: block;
  opacity: .5;
  content: attr(data-before);
  margin-bottom: 2px;
  text-transform: uppercase;
}
.jp-card .jp-card-front .jp-card-lower .jp-card-name {
  text-transform: uppercase;
  font-family: "Bitstream Vera Sans Mono", Consolas, Courier, monospace;
  font-size: 20px;
  max-height: 45px;
  position: absolute;
  bottom: 0;
  width: 190px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: horizontal;
  overflow: hidden;
  text-overflow: ellipsis;
}
.jp-card .jp-card-back {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform-style: preserve-3d;
  -moz-transform-style: preserve-3d;
  -ms-transform-style: preserve-3d;
  -o-transform-style: preserve-3d;
  transform-style: preserve-3d;
  -webkit-transition: all .4s linear;
  -moz-transition: all .4s linear;
  transition: all .4s linear;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  border-radius: 10px;
  background: #dddddd;
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
}
.jp-card .jp-card-back .jp-card-cvc {
  font-family: "Bitstream Vera Sans Mono", Consolas, Courier, monospace;
  font-size: 14px;
  position: absolute;
  top: 40%;
  left: 85%;
  -webkit-transition-delay: .6s;
  -moz-transition-delay: .6s;
  transition-delay: .6s;
}
.jp-card .jp-card-back .jp-card-shiny {
  width: 50px;
  height: 35px;
  border-radius: 5px;
  background: #cccccc;
  position: relative;
  position: absolute;
  top: 66%;
  left: 2%;
}
.jp-card .jp-card-back .jp-card-bar {
  background-color: #444444;
  background-image: -webkit-linear-gradient(#444444, #333333);
  background-image: linear-gradient(#444444, #333333);
  width: 100%;
  height: 20%;
  position: absolute;
  top: 10%;
}
.jp-card .jp-card-back .jp-card-shiny:after {
  content: "This card has been issued by Jesse Pollak and is licensed for anyone to use anywhere for free.\AIt comes with no warranty.\A For support issues, please visit: github.com/jessepollak/card.";
  position: absolute;
  left: 120%;
  top: 5%;
  color: #ffffff;
  font-size: 7px;
  width: 230px;
  opacity: .5;
}
.jp-card .jp-card-back:after {
  content: " ";
  display: block;
  background-color: #ffffff;
  background-image: -webkit-linear-gradient(#ffffff, #ffffff);
  background-image: linear-gradient(#ffffff, #ffffff);
  width: 80%;
  height: 16%;
  position: absolute;
  top: 40%;
  left: 2%;
}
.jp-card .jp-card-front:after {
  content: " ";
  display: block;
}
.jp-card.jp-card-discover.jp-card-identified .jp-card-back:before,
.jp-card.jp-card-discover.jp-card-identified .jp-card-front:before {
  background-color: #86b8cf;
}
.jp-card.jp-card-discover.jp-card-identified .jp-card-front:after {
  -webkit-transition: .4s;
  -moz-transition: .4s;
  transition: .4s;
  content: " ";
  display: block;
  background-color: #ff6600;
  background-image: -webkit-linear-gradient(#ff6600, #ffa366, #ff6600);
  background-image: linear-gradient(#ff6600, #ffa366, #ff6600);
  height: 50px;
  width: 50px;
  border-radius: 25px;
  position: absolute;
  left: 100%;
  top: 15%;
  margin-left: -25px;
  box-shadow: inset 1px 1px 3px 1px rgba(0, 0, 0, 0.5);
}
.jp-card.jp-card-maestro.jp-card-identified .jp-card-back .jp-card-logo.jp-card-maestro,
.jp-card.jp-card-maestro.jp-card-identified .jp-card-front .jp-card-logo.jp-card-maestro,
.jp-card.jp-card-mastercard.jp-card-identified .jp-card-back .jp-card-logo.jp-card-mastercard,
.jp-card.jp-card-mastercard.jp-card-identified .jp-card-front .jp-card-logo.jp-card-mastercard {
  box-shadow: none;
}
.jp-card-logo.jp-card-maestro {
  font-weight: 700;
  text-align: center;
  line-height: 36px;
  text-shadow: 1px 1px rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 14px;
  z-index: 1;
}
.jp-card-logo.jp-card-mastercard {
  font-weight: 700;
  text-align: center;
  line-height: 36px;
  text-shadow: 1px 1px rgba(0, 0, 0, 0.6);
  color: #ffffff;
  font-size: 9px;
  z-index: 1;
}
.jp-card-logo.jp-card-visa:after {
  content: " ";
  display: block;
  width: 100%;
  height: 25%;
  background: #e79800;
}
.jp-card-logo.jp-card-visa:before {
  content: " ";
  display: block;
  width: 100%;
  height: 25%;
  background: #1a1876;
}
.jp-card.jp-card-visa.jp-card-identified .jp-card-back:before,
.jp-card.jp-card-visa.jp-card-identified .jp-card-front:before {
  background-color: #191278;
}
.jp-card-logo.jp-card-mastercard:after {
  content: " ";
  display: block;
  width: 36px;
  top: 0;
  position: absolute;
  height: 36px;
  border-radius: 18px;
  right: 0;
  background: #ffab00;
  z-index: -2;
}
.jp-card-logo.jp-card-mastercard:before {
  content: " ";
  display: block;
  width: 36px;
  top: 0;
  position: absolute;
  height: 36px;
  border-radius: 18px;
  left: 0;
  background: red;
  z-index: -1;
}
.jp-card.jp-card-mastercard.jp-card-identified .jp-card-back:before,
.jp-card.jp-card-mastercard.jp-card-identified .jp-card-front:before {
  background-color: #0061a8;
}
.jp-card-logo.jp-card-dankort {
  width: 60px;
  height: 36px;
  padding: 3px;
  border-radius: 8px;
  border: 1px solid #000000;
  background-color: #ffffff;
}
.jp-card-logo.jp-card-dankort .dk {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.jp-card-logo.jp-card-dankort .dk:before {
  background-color: #ed1c24;
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 6px;
}
.jp-card-logo.jp-card-dankort .dk:after {
  content: '';
  position: absolute;
  top: 50%;
  margin-top: -7.7px;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 7px 7px 10px 0;
  border-color: transparent #ed1c24 transparent transparent;
  z-index: 1;
}
.jp-card-logo.jp-card-dankort .d {
  position: absolute;
  top: 50%;
  width: 50%;
  display: block;
  height: 15.4px;
  margin-top: -7.7px;
  background: #ffffff;
  left: 0;
  border-radius: 0 8px 10px 0;
}
.jp-card-logo.jp-card-dankort .k {
  position: absolute;
  top: 50%;
  width: 50%;
  display: block;
  height: 15.4px;
  margin-top: -7.7px;
  background: #ffffff;
  right: 0;
}
.jp-card-logo.jp-card-dankort .d:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  background: #ed1c24;
  border-radius: 2px 4px 6px 0;
  height: 5px;
  width: 7px;
  margin: -3px 0 0 -4px;
}
.jp-card-logo.jp-card-dankort .k:after {
  content: '';
  position: absolute;
  right: 50%;
  width: 0;
  height: 0;
  border-style: solid;
  margin-right: -1px;
  bottom: 0;
  border-width: 0 5px 8px 0;
  border-color: transparent transparent #ed1c24;
}
.jp-card-logo.jp-card-dankort .k:before {
  content: '';
  position: absolute;
  right: 50%;
  width: 0;
  height: 0;
  border-style: solid;
  margin-right: -1px;
  top: 0;
  border-width: 8px 5px 0 0;
  border-color: #ed1c24 transparent transparent;
}
.jp-card.jp-card-dankort.jp-card-identified .jp-card-back:before,
.jp-card.jp-card-dankort.jp-card-identified .jp-card-front:before {
  background-color: #0055c7;
}
.jp-card-logo.jp-card-maestro:after {
  content: " ";
  display: block;
  width: 36px;
  top: 0;
  position: absolute;
  height: 36px;
  border-radius: 18px;
  right: 0;
  background: #cc0000;
  z-index: -2;
}
.jp-card-logo.jp-card-maestro:before {
  content: " ";
  display: block;
  width: 36px;
  top: 0;
  position: absolute;
  height: 36px;
  border-radius: 18px;
  left: 0;
  background: #0064cb;
  z-index: -1;
}
.jp-card.jp-card-maestro.jp-card-identified .jp-card-back:before,
.jp-card.jp-card-maestro.jp-card-identified .jp-card-front:before {
  background-color: #0b2c5f;
}
.jp-card-container {
  -webkit-perspective: 1000px;
  -moz-perspective: 1000px;
  perspective: 1000px;
  width: 350px;
  max-width: 100%;
  height: 200px;
  margin: auto;
  z-index: 1;
  position: relative;
}
.jp-card.jp-card-flipped {
  -webkit-transform: rotateY(180deg);
  -moz-transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  -o-transform: rotateY(180deg);
  transform: rotateY(180deg);
}
.jp-card.jp-card-identified {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}
.jp-card.jp-card-identified .jp-card-back,
.jp-card.jp-card-identified .jp-card-front {
  background-color: #000000;
  background-color: rgba(0, 0, 0, 0.5);
}
.jp-card.jp-card-identified .jp-card-back:before,
.jp-card.jp-card-identified .jp-card-front:before {
  -webkit-transition: all .4s ease;
  -moz-transition: all .4s ease;
  transition: all .4s ease;
  background-image: -webkit-linear-gradient(
    -25deg,
    rgba(255, 255, 255, 0) 50%,
    rgba(255, 255, 255, 0.2) 70%,
    rgba(255, 255, 255, 0) 90%
  );
  background-image: linear-gradient(
    -25deg,
    rgba(255, 255, 255, 0) 50%,
    rgba(255, 255, 255, 0.2) 70%,
    rgba(255, 255, 255, 0) 90%
  );
  opacity: 1;
}
.jp-card.jp-card-identified .jp-card-back .jp-card-logo,
.jp-card.jp-card-identified .jp-card-front .jp-card-logo {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
}
.jp-card.jp-card-identified.no-radial-gradient .jp-card-back:before,
.jp-card.jp-card-identified.no-radial-gradient .jp-card-front:before {
  background-image: -webkit-linear-gradient(
    -25deg,
    rgba(255, 255, 255, 0) 50%,
    rgba(255, 255, 255, 0.2) 70%,
    rgba(255, 255, 255, 0) 90%
  );
  background-image: linear-gradient(
    -25deg,
    rgba(255, 255, 255, 0) 50%,
    rgba(255, 255, 255, 0.2) 70%,
    rgba(255, 255, 255, 0) 90%
  );
}
