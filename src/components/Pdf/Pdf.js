// import logo from "./logo.svg";
import { renderToString } from 'react-dom/server'
import {
	Document,
	Font,
	Image,
	// PDFDownloadLink,
	Page,
	pdf,
	StyleSheet,
	Text,
	View,
} from "@react-pdf/renderer";
import { But<PERSON>, message } from "antd";
import ReactDOMServer from "react-dom/server";
import { saveAs } from "file-saver";
import moment from "moment";
import React from "react";
import RobotoBold from "../../fonts/Roboto-Bold.ttf";
import RobotoMed from "../../fonts/Roboto-Medium.ttf";
import Roboto from "../../fonts/Roboto.ttf";
import img from "../../image/logo.png";
import noSign from "../../image/white.jpg";
import mainImage from "../../image/logo.png";

const format = "MM/DD/YYYY";
// const noSign =
// 	"https://openxcell-development-public.s3.ap-south-1.amazonaws.com/mover-inventory/jobItem/159/original/2a50e3843d0a13a4f5b1eedbacf5463c.jpg";
Font.register({
	family: "Roboto",
	format: "truetype",
	fonts: [
		{
			src: Roboto,
		},
		{
			src: RobotoBold,
			fontWeight: 800,
		},
		{
			src: RobotoMed,
			fontWeight: 600,
		},
	],
});
const styles = StyleSheet.create({
	page: {
		// margin: "0 auto",
		padding: 1,
		// width: "21cm";
		// height: 29.7;
		fontFamily: "Roboto",
	},
	section: {
		margin: 10,
		// padding: 5,
		flexGrow: 0.83,
		marginTop: 0,
		// marginBottom: 15,
		// textAlign: "center",
		// border: "1pt solid #6c757d",
	},
	header: {
		display: "flex",
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "center",
		textAlign: "center",
		padding: 10,
		borderBottom: "1pt solid #6c757d",
		// columnGap: 5,
	},

	header2: {
		display: "flex",
		flexDirection: "row",
		justifyContent: "normal",
		alignItems: "center",
		textAlign: "center",
		padding: 10,
		borderBottom: "1pt solid #6c757d",
		// columnGap: 5,
	},
	h4: {
		fontSize: 9,
		// width: 50,
	},
	para: {
		// width: 50,
		// minWidth: "50px",
		// textAlign: "left",
		marginTop: 1,
		fontSize: 7,
	},
	pageNumbers: {
		// position: "absolute",
		// top: 0,
		// left: 0,
		// right: 0,
		fontSize: 10,
		// marginRight: 10,
		textAlign: "center",
		fontWeight: "bold",
	},
	jobName: {
		display: "flex",
		justifyContent: "space-between",
		flexDirection: "row-reverse",
		margin: 10,
		marginTop: 5,
		marginBottom: 3,
		// padding: "0 0 10px 0",
		fontSize: 10,
		fontWeight: "bold",
	},
	firstPage: {
		display: "flex",
		justifyContent: "flex-start",
		flexDirection: "row",
		margin: 10,
		marginBottom: 15,
		fontSize: 10,
	},
	firstPage_info: {
		display: "flex",
		justifyContent: "center",
		width: "100%",
		textAlign: "center",
		margin: 10,
	},
	address: {
		display: "flex",
		justifyContent: "space-around",
		flexDirection: "row",
		margin: 10,
		marginTop: 15,
		// textAlign: "center",
	},
	address_title: {
		fontWeight: 600,
		borderBottom: "1pt solid #ccc",
		paddingBottom: 7,
		marginBottom: 5,
	},
	address_name: {
		fontSize: 10,
	},
	common: {
		display: "flex",
		flexDirection: "row",
	},
});

const toDate = (date) => {
	let s = new Date(date).toLocaleTimeString([], {
		day: "numeric",
		month: "short",
		year: "numeric",
		hour: "2-digit",
		minute: "2-digit",
	});
	return s;
};

const toDateNew = (date) => {
	let d = new Date(date);
	let month = (d.getMonth() + 1).toString();
	let day = d.getDate().toString();
	let year = d.getFullYear();
	if (month.length < 2) {
		month = '0' + month;
	}
	if (day.length < 2) {
		day = '0' + day;
	}
	return [year, month, day].join('-');
};

const formatPhoneNumber = (phoneNumberString) => {
	let cleaned = ("" + phoneNumberString).replace(/\D/g, "");
	let match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
	if (match) {
		return "(" + match[1] + ") " + match[2] + " - " + match[3];
	}
	return null;
};
const countException = (data) => {
	let count = 0;
	data.forEach((data) => {
		if (data.exceptions.length > 0) {
			count = count + 1;
		} else {
		}
	});
	return count;
};
const countDisassembled = (data) => {
	let count = 0;
	data.forEach((data) => {
		if (data.is_disassembled === "YES") {
			count = count + 1;
		} else {
		}
	});
	return count;
};

const countfirearm = (data) => {
	let count = 0;
	data.forEach((data) => {
		if (data.is_firearm === "true" || data.is_firearm === true) {
			count = count + 1;
		} else {
		}
	});
	return count;
};

const countElectronics = (data) => {
	let count = 0;
	data.forEach((data) => {
		if (data.is_electronics === "YES") {
			count = count + 1;
		} else {
		}
	});
	return count;
};
const countHighValueItem = (data) => {
	let count = 0;
	data.forEach((data) => {
		if (data.is_high_value === "true") {
			count = count + 1;
		} else {
			//console.log(data.shipment_inventory_exceptions);
			//console.log("Without exception");
		}
	});
	return count;
};
const getSupervisor = (job_worker) => {
	const supervisor = job_worker
		.filter((worker) => worker.role === "supervisor")
		.map((worker) => (worker ? worker.first_name.trim() + " " + worker.last_name : " - "));
	return supervisor[0];
};
const getWorker = (job_worker) => {
	const worker = job_worker
		.filter((worker) => worker.role === "worker")
		.map((worker) => (worker ? worker.first_name.trim() + " " + worker.last_name : " - "));
	return worker;
};

const supervisorSignatureRequireAtOriginToAllPages = (data) => {
	let imageData = null
	const check = data && data.shipment_type.shipment_stage;
	check.forEach((newData) => {
		if (newData.supervisor_signature_require_at_origin_to_all_pages === "yes" || newData.supervisor_signature_require_at_origin_to_all_pages === 1) {
			imageData = newData.supervisor_signature
		}
	});
	return imageData;
};

const supervisorSignatureRequireAtOriginToAllPagesDate = (data) => {
	let imageDate = null
	const check = data && data.shipment_type.shipment_stage;
	check.forEach((newData) => {
		if (newData.supervisor_signature_require_at_origin_to_all_pages === "yes" || newData.supervisor_signature_require_at_origin_to_all_pages === 1) {
			imageDate = toDateNew(newData.created_at)
		}
	});
	return imageDate;
};

const customerSignatureRequireAtOriginToAllPages = (data) => {
	let imageData = null
	const check = data && data.shipment_type.shipment_stage;
	check.forEach((newData) => {
		if (newData.customer_signature_require_at_origin_to_all_pages === "yes" || newData.customer_signature_require_at_origin_to_all_pages === 1) {
			imageData = newData.customer_signature
		}
	});
	return imageData;
};

const customerSignatureRequireAtOriginToAllPagesDate = (data) => {
	let imageDate = null
	const check = data && data.shipment_type.shipment_stage;
	check.forEach((newData) => {
		if (newData.customer_signature_require_at_origin_to_all_pages === "yes" || newData.customer_signature_require_at_origin_to_all_pages === 1) {
			imageDate = toDateNew(newData.created_at)
		}
	});
	return imageDate;
};

const supervisorSignatureRequireAtDestinationToAllPages = (data) => {
	let imageData = null
	const check = data && data.shipment_type.shipment_stage;
	check.forEach((newData) => {
		if (newData.supervisor_signature_require_at_destination_to_all_pages === "yes" || newData.supervisor_signature_require_at_destination_to_all_pages === 1) {
			imageData = newData.supervisor_signature
		}
	});
	return imageData;
};

const supervisorSignatureRequireAtDestinationToAllPagesDate = (data) => {
	let imageDate = null
	const check = data && data.shipment_type.shipment_stage;
	check.forEach((newData) => {
		if (newData.supervisor_signature_require_at_destination_to_all_pages === "yes" || newData.supervisor_signature_require_at_destination_to_all_pages === 1) {
			imageDate = toDateNew(newData.created_at)
		}
	});
	return imageDate;
};

const customerSignatureRequireAtDestinationToAllPages = (data) => {
	let imageData = null
	const check = data && data.shipment_type.shipment_stage;
	check.forEach((newData) => {
		if (newData.customer_signature_require_at_destination_to_all_pages === "yes" || newData.customer_signature_require_at_destination_to_all_pages === 1) {
			imageData = newData.customer_signature
		}
	});
	return imageData;
};

const customerSignatureRequireAtDestinationToAllPagesDate = (data) => {
	let imageDate = null
	const check = data && data.shipment_type.shipment_stage;
	check.forEach((newData) => {
		if (newData.customer_signature_require_at_destination_to_all_pages === "yes" || newData.customer_signature_require_at_destination_to_all_pages === 1) {
			imageDate = toDateNew(newData.created_at)
		}
	});
	return imageDate;
};

const MyDoc = ({ data }) => {
	const getImg =
		data && data.job_company && data.job_company.company_logo ? data.job_company.company_logo : img;
	return (
		<Document>
			<Page size='A4' style={[styles.page, { padding: "8px" }]} wrap>
				{/* <Text
					style={styles.pageNumbers}
					render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`}
					fixed
				/>
				<View style={styles.jobName} fixed>
					<Text>{toDate(Date.now())}</Text>
					<Text>{data ? "#" + data.job_number : ""}</Text>
				</View> */}
				<View style={styles.jobName} fixed>
					<View>
						<Text
							style={styles.pageNumbers}
							render={({ pageNumber, totalPages }) => `${pageNumber} / ${totalPages}`}
						/>
					</View>
					<View style={{ display: "flex", flexDirection: "row", flexWrap: "wrap" }}>
						<Text style={{ padding: "0 15px 0 0" }}>
							{data && data.customer_job
								? data.customer_job.first_name + " " + data.customer_job.last_name
								: ""}
						</Text>
						<Text style={{ padding: "0 15px 0 0", maxWidth: "175px" }}>
							{data && data.shipment_name ? data.shipment_name : " - "}
						</Text>
						<Text style={{ padding: "0 15px 0 0" }}>
							Origin: {data && data.pickup_city ? data.pickup_city + ", " : ""}
							{data && data.pickup_state ? data.pickup_state : ""}
						</Text>
						<Text style={{ padding: "0 15px 0 0" }}>
							Dest: {data && data.delivery_city ? data.delivery_city + ", " : ""}
							{data && data.delivery_state ? data.delivery_state : ""}
						</Text>
					</View>
				</View>
				<View style={{ width: "100%", height: "90%" }}>
					<div style={{
						display: "flex",
						flexDirection: "row",
						flexWrap: "wrap",
					}} >
						<View style={styles.firstPage}>
							<Image
								style={{ width: 90, height: 100, marginRight: 10 }}
								src={{
									uri: getImg,
									method: "GET",
									headers: { "Cache-Control": "no-cache" },
									body: "",
								}}
							/>
							<View>
								<Text style={{ fontSize: 14 }}>
									{data && data.job_company && data.job_company.company_name
										? data.job_company.company_name
										: ""}
								</Text>
								<Text style={{ marginTop: 10 }}>
									{data && data.job_company && data.job_company.address1 ? data.job_company.address1 : ""}{" "}
									{data &&
										data.job_company &&
										data.job_company.address2 &&
										data.job_company.address2 !== undefined &&
										data.job_company.address2 !== null &&
										data.job_company.address2 !== "null"
										? data.job_company.address2
										: ""}
								</Text>
								<Text style={{ marginTop: 2 }}>
									{data && data.job_company && data.job_company.city ? data.job_company.city : ""}{" "}
									{data && data.job_company && data.job_company.state ? data.job_company.state + ", " : ""}
									{data && data.job_company && data.job_company.zipCode ? data.job_company.zipCode : ""}
								</Text>
								<Text style={{ marginTop: 2 }}>
									{data && data.job_company && data.job_company.country ? data.job_company.country : ""}
								</Text>
								<Text style={{ marginTop: 10 }}>
									{data && data.job_company && data.job_company.phone
										? formatPhoneNumber(data.job_company.phone)
										: ""}
								</Text>
								<Text style={{ marginTop: 2 }}>
									{data && data.job_company && data.job_company.email ? data.job_company.email : ""}
								</Text>
							</View>
						</View>

						<View style={styles.firstPage} >
							<Image
								style={{ width: 90, height: 100, marginRight: 10 }}
								src={{
									uri: mainImage,
									method: "GET",
									headers: { "Cache-Control": "no-cache" },
									body: "",
								}}
							/>
							<View>
								<Text style={{ fontSize: 14 }}>
									Powered by Mover Inventory
								</Text>
								<Text style={{ marginTop: 10 }}>
									A customer portal is available
								</Text>
								<Text style={{ marginTop: 10 }}>
									to view  photos, notes, and
								</Text>
								<Text style={{ marginTop: 10 }}>
									add comments for each item.
								</Text>
							</View>
						</View>
					</div>
					<View style={styles.firstPage_info}>
						<Text style={{ fontWeight: 800, fontSize: 24 }}>
							{data && data.customer_job
								? data.customer_job.first_name + " " + data.customer_job.last_name
								: ""}
						</Text>
						<Text style={{ fontSize: 11, marginTop: 4 }}>{data ? data.shipment_name : ""}</Text>
						<Text style={{ fontSize: 11, marginTop: 3 }}>
							{data && data.customer_job ? formatPhoneNumber(data.customer_job.phone) : ""}
							{"  "}
							{data && data.customer_job ? data.customer_job.email : ""}
						</Text>
					</View>
					<View style={styles.address}>
						<View>
							<Text style={[styles.address_title, { width: 60 }]}>Origin</Text>
							<Text style={{ marginTop: 10, fontSize: 11 }}>
								{data && data.pickup_date ? moment(data.pickup_date).format(format) : ""}
							</Text>
							<Text style={{ marginTop: 10, fontSize: 11 }}>
								{data && data.pickup_address ? data.pickup_address : ""}{" "}
								{data && data.pickup_address2 ? data.pickup_address2 : ""}
							</Text>
							<Text style={{ marginTop: 3, fontSize: 11 }}>
								{data && data.pickup_city ? data.pickup_city : ""}{" "}
								{data && data.pickup_state ? data.pickup_state + ", " : ""}
								{data && data.pickup_zipcode ? data.pickup_zipcode : ""}
							</Text>
							<Text style={{ marginTop: 3, fontSize: 11 }}>
								{data && data.pickup_country ? data.pickup_country : ""}
							</Text>
						</View>
						<View>
							<Text style={styles.address_title}>Destination</Text>
							<Text style={{ marginTop: 10, fontSize: 11 }}>
								{data && data.delivery_date ? moment(data.delivery_date).format(format) : ""}
							</Text>
							<Text style={{ marginTop: 10, fontSize: 11 }}>
								{data && data.delivery_address ? data.delivery_address : ""}{" "}
								{data && data.delivery_address2 ? data.delivery_address2 : ""}
							</Text>
							<Text style={{ marginTop: 3, fontSize: 11 }}>
								{data && data.delivery_city ? data.delivery_city : ""}{" "}
								{data && data.delivery_state ? data.delivery_state + ", " : ""}
								{data && data.delivery_zipcode ? data.delivery_zipcode : ""}
							</Text>
							<Text style={{ marginTop: 3, fontSize: 11 }}>
								{data && data.delivery_country ? data.delivery_country : ""}
							</Text>
						</View>
					</View>
					{/* Job Summary */}
					<View style={styles.address}>
						<View>
							<Text style={styles.address_title}>Job Summary</Text>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Total Items: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.total_items ? data.total_items : 0}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Total Volume: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.total_volume ? data.total_volume : 0} Cu Ft.
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Disassembled Items: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.total_disassembled_items ? data.total_disassembled_items : 0}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>High Value Items: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.total_highValue_items ? data.total_highValue_items : 0}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Pro Gear Items: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.total_pro_gear_items ? data.total_pro_gear_items : 0}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>
									Items With Exceptions:{" "}
								</Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.itemData ? countException(data.itemData) : 0}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Supervisor: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.job_worker ? getSupervisor(data.job_worker) : " - "}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Worker: </Text>
								<View
									style={{
										display: "flex",
										flexDirection: "row",
										flexWrap: "wrap",
										width: 165,
									}}>
									{data && data.job_worker
										? getWorker(data.job_worker).map((worker, ind) => {
											return <Text style={{ marginTop: 10, fontSize: 11 }}>{worker}, </Text>;
										})
										: " - "}
								</View>
							</View>
						</View>
						<View>
							<Text style={{ marginBottom: 35 }}>{""}</Text>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Total Cartons: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data
										? data.total_cartons +
										" (CP " +
										data.total_cartons_cp +
										" PBO " +
										data.total_cartons_pbo +
										")"
										: 0}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Total Weight: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.total_weight ? data.total_weight : 0} Lbs.
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Electronics: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.total_electronics_items ? data.total_electronics_items : 0}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>High Value Total: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									${data && data.total_high_value ? data.total_high_value : 0}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Pro Gear Weight: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.total_pro_gear_weight ? data.total_pro_gear_weight : 0} Lbs.
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Pads Used: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.total_pads_used ? data.total_pads_used : 0}
								</Text>
							</View>
							<View style={styles.common}>
								<Text style={{ marginTop: 10, fontSize: 11, fontWeight: 600 }}>Firearms Total Qty: </Text>
								<Text style={{ marginTop: 10, fontSize: 11 }}>
									{data && data.itemData ? countfirearm(data.itemData) : 0}
								</Text>
							</View>
						</View>
					</View>
					<View
						style={{
							position: "absolute",
							bottom: "-26%",
							width: "100%",
							height: "300px",
							backgroundColor: "#fff",
							zIndex: 1000000,
						}}></View>
				</View>
				{/* Items */}
				{/* <View style={{ display: "flex", flexDirection: "row", flexGrow: 0.1 }} break> */}
				<View style={styles.section} break>

					{
						(data && data.total_high_value == 0) && (data && data.total_pro_gear_weight == 0) ?
							<View
								style={{
									border: "1pt solid #6c757d",
									// flexGrow:0.83
								}}>
								<View style={[styles.header, {
									padding: "0 1px", justifyContent: "flex-start",
									position: "reletive",
									padding: "2px"
								}]} fixed>
									<Text style={[styles.h4, { width: "25px", padding: "5px 0" }]}>Label</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "15px" }]}>Item</Text>
									<Text style={[styles.h4, { width: "100px", padding: "5px 0", marginLeft: "18px" }]}>Room</Text>
									<Text style={[styles.h4, { width: "35px", padding: "5px 0", marginLeft: "20px" }]}>Carton</Text>
									<Text style={[styles.h4, { width: "75px", padding: "5px 0", marginLeft: "15px" }]}>Electronics</Text>
									{/* <Text style={[styles.h4, { width: "50px", padding: "5px 0" }]}>High Value</Text> */}
									{/* <Text style={[styles.h4, { width: "50px", padding: "5px 0" }]}>Pro Gear</Text> */}
									<Text style={[styles.h4, { width: "75px", padding: "5px 0", marginLeft: "15px" }]}>Disassembled</Text>
									<Text style={[styles.h4, { width: "20px", padding: "5px 0", fontSize: 5, marginLeft: "16px" }]}>
										Shipper Initials
									</Text>
									<Text style={[styles.h4, { width: "18px", padding: "5px 0", fontSize: 5, marginLeft: "16px" }]}>
										Carrier Initials
									</Text>
								</View>

								{data && data.itemData ? (
									data.itemData.map((item, index) => {
										return (
											<View
												key={index}
												style={{
													display: "flex",
													flexDirection: "column",
													padding: "2px 3px",
													borderBottom: data.itemData.length === index + 1 ? "none" : "1pt solid #6c757d",
												}}>
												<View style={[styles.header, { borderBottom: "none", padding: "0 3px 0 0" }]}>
													{
														item.isManualLabel === 1 || item.isManualLabel === "true" || item.isManualLabel === true ?

															<Text style={[styles.para, { width: "25px" }]}>
																<text>{"M-" + item.color.charAt(0).toUpperCase() + "/"} </text>
																<text>{item.lot_no + "/"}</text>
																<text>{item.label_no + ""}</text>
															</Text>
															:
															<>

																{item.item_qr.type === "Generic" ?
																	<Text style={[styles.para, { width: "25px" }]}>
																		{"G" + item.item_qr.label_number}
																	</Text>
																	:
																	<>

																		{item.item_qr.type === "External" ?
																			<Text style={[styles.para, { width: "25px" }]}>
																				{"E" + item.item_qr.label_number}
																			</Text>
																			:
																			<Text style={[styles.para, { width: "25px" }]}>
																				{item.item_qr.label_number}
																			</Text>
																		}
																	</>
																}

															</>

													}
													<Text
														style={[
															styles.para,
															{
																width: "125px",
																fontWeight: "600",
																// textAlign: item.item_name.length > 15 ? "left" : "center",
																textAlign: "center",
															},
														]}>
														{item.item_name}
													</Text>
													<Text style={[styles.para, { width: "100px" }]}>{item.room.name}</Text>
													<Text style={[styles.para, { width: "35px" }]}>
														{(item.is_carton === "YES" || item.is_carton === "Yes" || item.is_carton === "true" || item.is_carton === "TRUE") && (item.packed_by === "OWNER" || item.packed_by === "Packed by Owner")
															? "Y / PBO"
															: (item.is_carton === "YES" || item.is_carton === "Yes" || item.is_carton === "true" || item.is_carton === "TRUE") && (item.packed_by === "MOVER" || item.packed_by === "Carrier Packed")
																? "Y / CP"
																: "No"}
													</Text>
													<Text style={[styles.para, { width: "75px" }]}>
														{item.is_electronics === "YES" || item.is_electronics === "true" ? `Y / ${item.serial_number}` : "No"}
													</Text>
													{/* <Text style={[styles.para, { width: "50px" }]}>
														{item.is_high_value === "true" ? ((item.seal_number !== undefined && item.seal_number !== "") ? `Y/${item.seal_number}` : `Y / ${"$ " + item.declared_value}`) : "No"}
													</Text>
													<Text style={[styles.para, { width: "50px" }]}>
														{item.is_pro_gear === "true" ? `Y- ${item.progear_name == "Member" || item.progear_name == "member" ? "M" : "S"} / ${item.pro_gear_weight + " lbs."}` : "No"}
													</Text> */}
													<Text style={[styles.para, { width: "75px" }]}>
														{
															(item.is_disassembled === "YES" || item.is_disassembled === "true" || item.is_disassembled === "TRUE") && (item.disassembled_by === "By Customer")
																? "Y / By Customer"
																: (item.is_disassembled === "YES" || item.is_disassembled === "true") && item.disassembled_by === "By Company"
																	? `Y / By Company`
																	: "No"}
													</Text>
													<View
														style={{
															width: "15px",
															height: "15px",
															marginTop: 1,
															marginRight: 2,
															border: "1px solid #ccc",
														}}></View>
													<View
														style={{
															width: "15px",
															height: "15px",
															marginTop: 1,
															border: "1px solid #ccc",
														}}></View>
												</View>
												{/* {item.notes ||
											(item.exceptions && item.exceptions.length > 0 && ( */}
												<View style={{ fontSize: 7, padding: "0 3px 2px 3px" }}>
													{item.exceptions && item.exceptions.length > 0 ? (
														<View>
															{item.exceptions.map((e, i) => (
																<Text style={{ marginBottom: 1 }}>
																	<Text style={{ fontWeight: "bold", letterSpacing: 1 }}>E-{i + 1}: </Text>
																	{e.eid.length > 0
																		? e.eid.map((eid, i) => {
																			return <>{eid.exception_name ? eid.exception_name + ", " : ""}</>;
																		})
																		: ""}
																	{e.lid.length > 0
																		? e.lid.map((lid, i) => {
																			return <>{lid.location_name ? lid.location_name + ", " : ""}</>;
																		})
																		: ""}
																	{e.notes ? e.notes + ", " : ""}
																</Text>
															))}
														</View>
													) : (
														<Text></Text>
													)}
													{item.notes ? (
														<Text style={{ lineHeight: 1.15 }}>
															<Text style={{ fontWeight: "bold" }}>Other Notes: </Text>
															{item.notes}
														</Text>
													) : (
														<Text></Text>
													)}

													{item.is_firearm == true || item.is_firearm == "true" ? (
														<Text style={{ lineHeight: 1.15 }}>
															<Text style={{ fontWeight: "bold" }}>Firearm Serial Number: {item.firmarm_serial_number}</Text>
														</Text>
													) : (
														<Text></Text>
													)}

													{item.comments.length > 0 ? (
														<Text style={{ lineHeight: 1.15 }}>
															<Text style={{ fontWeight: "bold" }}>Customer Comments :</Text>
															<Text>
																{
																	item.comments.map((e, i) => (
																		<> - {e.comment}. </>

																	))
																}
															</Text>
														</Text>
													) : (
														<Text>No Item</Text>
													)}



												</View>
												{/* ))} */}
											</View>
										);
									})
								) : (
									<View>
										<Text>No Item</Text>
									</View>
								)}
							</View>

							:
							<View
								style={{
									border: "1pt solid #6c757d",
									// flexGrow:0.83
								}}>
								<View style={[styles.header, {
									padding: "0 1px", justifyContent: "flex-start",
									position: "reletive",
									padding: "2px"
								}]} fixed>
									<Text style={[styles.h4, { width: "25px", padding: "5px 0" }]}>Label</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0" }]}>Item</Text>
									<Text style={[styles.h4, { width: "100px", padding: "5px 0" }]}>Room</Text>
									<Text style={[styles.h4, { width: "35px", padding: "5px 0" }]}>Carton</Text>
									<Text style={[styles.h4, { width: "75px", padding: "5px 0" }]}>Electronics</Text>
									<Text style={[styles.h4, { width: "50px", padding: "5px 0" }]}>High Value</Text>
									<Text style={[styles.h4, { width: "50px", padding: "5px 0" }]}>Pro Gear</Text>
									<Text style={[styles.h4, { width: "75px", padding: "5px 0" }]}>Disassembled</Text>
									<Text style={[styles.h4, { width: "20px", padding: "5px 0", fontSize: 5 }]}>
										Shipper Initials
									</Text>
									<Text style={[styles.h4, { width: "18px", padding: "5px 0", fontSize: 5 }]}>
										Carrier Initials
									</Text>
								</View>

								{data && data.itemData ? (
									data.itemData.map((item, index) => {
										return (
											<View
												key={index}
												style={{
													display: "flex",
													flexDirection: "column",
													padding: "2px 3px",
													borderBottom: data.job_items.length === index + 1 ? "none" : "1pt solid #6c757d",
												}}>
												<View style={[styles.header, { borderBottom: "none", padding: "0 3px 0 0" }]}>
													{
														item.isManualLabel === 1 || item.isManualLabel === "true" || item.isManualLabel === true ?

															<Text style={[styles.para, { width: "25px" }]}>
																<text>{"M-" + item.color.charAt(0).toUpperCase() + "/"} </text>
																<text>{item.lot_no + "/"}</text>
																<text>{item.label_no + ""}</text>
															</Text>
															:
															<>

																{item.item_qr.type === "Generic" ?
																	<Text style={[styles.para, { width: "25px" }]}>
																		{"G" + item.item_qr.label_number}
																	</Text>
																	:
																	<>

																		{item.item_qr.type === "External" ?
																			<Text style={[styles.para, { width: "25px" }]}>
																				{"E" + item.item_qr.label_number}
																			</Text>
																			:
																			<Text style={[styles.para, { width: "25px" }]}>
																				{item.item_qr.label_number}
																			</Text>
																		}
																	</>
																}

															</>

													}
													<Text
														style={[
															styles.para,
															{
																width: "125px",
																fontWeight: "600",
																// textAlign: item.item_name.length > 15 ? "left" : "center",
																textAlign: "center",
															},
														]}>
														{item.item_name}
													</Text>
													<Text style={[styles.para, { width: "100px" }]}>{item.room.name}</Text>
													<Text style={[styles.para, { width: "35px" }]}>
														{(item.is_carton === "YES" || item.is_carton === "Yes" || item.is_carton === "true" || item.is_carton === "TRUE") && (item.packed_by === "OWNER" || item.packed_by === "Packed by Owner")
															? "Y / PBO"
															: (item.is_carton === "YES" || item.is_carton === "Yes" || item.is_carton === "true" || item.is_carton === "TRUE") && (item.packed_by === "MOVER" || item.packed_by === "Carrier Packed")
																? "Y / CP"
																: "No"}
													</Text>
													<Text style={[styles.para, { width: "75px" }]}>
														{item.is_electronics === "YES" || item.is_electronics === "true" ? `Y / ${item.serial_number}` : "No"}
													</Text>
													<Text style={[styles.para, { width: "50px" }]}>
														{item.is_high_value === "true" ? ((item.seal_number !== undefined && item.seal_number !== "") ? `Y/${item.seal_number}` : `Y / ${"$ " + item.declared_value}`) : "No"}
													</Text>
													<Text style={[styles.para, { width: "50px" }]}>
														{item.is_pro_gear === "true" ? `Y- ${item.progear_name == "Member" || item.progear_name == "member" ? "M" : "S"} / ${item.pro_gear_weight + " lbs."}` : "No"}
													</Text>
													<Text style={[styles.para, { width: "75px" }]}>
														{
															(item.is_disassembled === "YES" || item.is_disassembled === "true" || item.is_disassembled === "TRUE") && (item.disassembled_by === "By Customer")
																? "Y / By Customer"
																: (item.is_disassembled === "YES" || item.is_disassembled === "true") && item.disassembled_by === "By Company"
																	? `Y / By Company`
																	: "No"}
													</Text>
													<View
														style={{
															width: "15px",
															height: "15px",
															marginTop: 1,
															marginRight: 2,
															border: "1px solid #ccc",
														}}></View>
													<View
														style={{
															width: "15px",
															height: "15px",
															marginTop: 1,
															border: "1px solid #ccc",
														}}></View>
												</View>
												{/* {item.notes ||
											(item.exceptions && item.exceptions.length > 0 && ( */}
												<View style={{ fontSize: 7, padding: "0 3px 2px 3px" }}>
													{item.exceptions && item.exceptions.length > 0 ? (
														<View>
															{item.exceptions.map((e, i) => (
																<Text style={{ marginBottom: 1 }}>
																	<Text style={{ fontWeight: "bold", letterSpacing: 1 }}>E-{i + 1}: </Text>
																	{e.eid.length > 0
																		? e.eid.map((eid, i) => {
																			return <>{eid.exception_name ? eid.exception_name + ", " : ""}</>;
																		})
																		: ""}
																	{e.lid.length > 0
																		? e.lid.map((lid, i) => {
																			return <>{lid.location_name ? lid.location_name + ", " : ""}</>;
																		})
																		: ""}
																	{e.notes ? e.notes + ", " : ""}
																</Text>
															))}
														</View>
													) : (
														<Text></Text>
													)}
													{item.notes ? (
														<Text style={{ lineHeight: 1.15 }}>
															<Text style={{ fontWeight: "bold" }}>Other Notes: </Text>
															{item.notes}
														</Text>
													) : (
														<Text></Text>
													)}

													{item.is_firearm == true || item.is_firearm == "true" ? (
														<Text style={{ lineHeight: 1.15 }}>
															<Text style={{ fontWeight: "bold" }}>Firearm Serial Number: {item.firmarm_serial_number}</Text>
														</Text>
													) : (
														<Text></Text>
													)}

													{item.comments.length > 0 ? (
														<Text style={{ lineHeight: 1.15 }}>
															<Text style={{ fontWeight: "bold" }}>Customer Comments :</Text>
															<Text>
																{
																	item.comments.map((e, i) => (
																		<> - {e.comment}. </>

																	))
																}
															</Text>
														</Text>
													) : (
														<Text>No Item</Text>
													)}



												</View>
												{/* ))} */}
											</View>
										);
									})
								) : (
									<View>
										<Text>No Item</Text>
									</View>
								)}
							</View>
					}

					{(data && data.total_pro_gear_weight == 0) ?
						<View></View>
						:
						<View style={{
							marginTop: "20px",
							// flexGrow: 0.83
						}} >
							<Text style={{ padding: "0 15px 0 0", marginBottom: "10px" }}>
								Pro-Gear Items Details
							</Text>
							<View
								style={{
									border: "1pt solid #6c757d",
								}}>
								<View style={[styles.header, {
									padding: "0 1px", justifyContent: "flex-start",
									position: "reletive",
									padding: "2px"
								}]} fixed>
									<Text style={[styles.h4, { width: "50px", padding: "5px 0" }]}>Label</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "140px" }]}>Item</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "100px" }]}>Room</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "70px" }]}>Pro_Gear_Weight</Text>

								</View>


								{data && data.itemData ? (
									data.itemData.map((item, index) => {
										if (item.is_pro_gear === "true")
											return (
												<View
													key={index}
													style={{
														display: "flex",
														flexDirection: "column",
														padding: " 2px 5px",
														borderBottom: data.job_items.length === index + 1 ? "none" : "1pt solid #6c757d",
													}}>
													<View style={[styles.header, { borderBottom: "none", padding: "0 3px 0 0" }]}>
														{
															item.isManualLabel === 1 || item.isManualLabel === "true" || item.isManualLabel === true ?

																<Text style={[styles.para, { width: "25px" }]}>
																	<text>{"M-" + item.color.charAt(0).toUpperCase() + "/"} </text>
																	<text>{item.lot_no + "/"}</text>
																	<text>{item.label_no + ""}</text>
																</Text>
																:
																<>

																	{item.item_qr.type === "Generic" ?
																		<Text style={[styles.para, { width: "25px" }]}>
																			{"G" + item.item_qr.label_number}
																		</Text>
																		:
																		<>

																			{item.item_qr.type === "External" ?
																				<Text style={[styles.para, { width: "25px" }]}>
																					{"E" + item.item_qr.label_number}
																				</Text>
																				:
																				<Text style={[styles.para, { width: "25px" }]}>
																					{item.item_qr.label_number}
																				</Text>
																			}
																		</>
																	}

																</>

														}
														<Text
															style={[
																styles.para,
																{
																	width: "125px",
																	fontWeight: "600",
																	// textAlign: item.item_name.length > 15 ? "left" : "center",
																	textAlign: "center",
																	marginLeft: "50px"
																},
															]}>
															{item.item_name}
														</Text>
														<Text style={[styles.para, { width: "100px" }]}>{item.room.name}</Text>
														<Text style={[styles.para, { width: "50px" }]}>
															{item.is_pro_gear === "true" ? `Y- ${item.progear_name == "Member" || item.progear_name == "member" ? "M" : "S"} / ${item.pro_gear_weight + " lbs."}` : "No"}
														</Text>
													</View>
													{/* {item.notes ||
											(item.exceptions && item.exceptions.length > 0 && ( */}
													<View style={{ fontSize: 7, padding: "0 3px 2px 3px" }}>
														{item.exceptions && item.exceptions.length > 0 ? (
															<View>
																{item.exceptions.map((e, i) => (
																	<Text style={{ marginBottom: 1 }}>
																		<Text style={{ fontWeight: "bold", letterSpacing: 1 }}>E-{i + 1}: </Text>
																		{e.eid.length > 0
																			? e.eid.map((eid, i) => {
																				return <>{eid.exception_name ? eid.exception_name + ", " : ""}</>;
																			})
																			: ""}
																		{e.lid.length > 0
																			? e.lid.map((lid, i) => {
																				return <>{lid.location_name ? lid.location_name + ", " : ""}</>;
																			})
																			: ""}
																		{e.notes ? e.notes + ", " : ""}
																	</Text>
																))}
															</View>
														) : (
															<Text></Text>
														)}
														{item.notes ? (
															<Text style={{ lineHeight: 1.15 }}>
																<Text style={{ fontWeight: "bold" }}>Other Notes: </Text>
																{item.notes}
															</Text>
														) : (
															<Text></Text>
														)}

														{item.comments.length > 0 ? (
															<Text style={{ lineHeight: 1.15 }}>
																<Text style={{ fontWeight: "bold" }}>Customer Comments :</Text>
																<Text>
																	{
																		item.comments.map((e, i) => (
																			<> - {e.comment}. </>

																		))
																	}
																</Text>
															</Text>
														) : (
															<Text></Text>
														)}



													</View>
													{/* ))} */}
												</View>
											);
									})
								) : (
									<View>
										<Text>No Item</Text>
									</View>
								)}

							</View>
						</View>
					}

					{(data && data.total_highValue_items == 0) ?
						<View></View>
						:
						<View style={{
							marginTop: "20px",
							//  flexGrow: 0.83 
						}}>
							<Text style={{ padding: "0 15px 0 0", marginBottom: "10px" }}>
								High Value Items Details
							</Text>
							<View
								style={{
									border: "1pt solid #6c757d",
								}}>
								<View style={[styles.header, {
									padding: "0 1px", justifyContent: "flex-start",
									position: "reletive",
									padding: "2px"
								}]} fixed>
									<Text style={[styles.h4, { width: "50px", padding: "5px 0" }]}>Label</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "140px" }]}>Item</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "100px" }]}>Room</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "70px" }]}>Declared Value</Text>

								</View>


								{data && data.itemData ? (
									data.itemData.map((item, index) => {
										if (item.is_high_value === "true")
											return (
												<View
													key={index}
													style={{
														display: "flex",
														flexDirection: "column",
														padding: " 2px 5px",
														borderBottom: data.job_items.length === index + 1 ? "none" : "1pt solid #6c757d",
													}}>
													<View style={[styles.header, { borderBottom: "none", padding: "0 3px 0 0" }]}>
														{
															item.isManualLabel === 1 || item.isManualLabel === "true" || item.isManualLabel === true ?

																<Text style={[styles.para, { width: "25px" }]}>
																	<text>{"M-" + item.color.charAt(0).toUpperCase() + "/"} </text>
																	<text>{item.lot_no + "/"}</text>
																	<text>{item.label_no + ""}</text>
																</Text>
																:
																<>

																	{item.item_qr.type === "Generic" ?
																		<Text style={[styles.para, { width: "25px" }]}>
																			{"G" + item.item_qr.label_number}
																		</Text>
																		:
																		<>

																			{item.item_qr.type === "External" ?
																				<Text style={[styles.para, { width: "25px" }]}>
																					{"E" + item.item_qr.label_number}
																				</Text>
																				:
																				<Text style={[styles.para, { width: "25px" }]}>
																					{item.item_qr.label_number}
																				</Text>
																			}
																		</>
																	}

																</>

														}
														<Text
															style={[
																styles.para,
																{
																	width: "125px",
																	fontWeight: "600",
																	// textAlign: item.item_name.length > 15 ? "left" : "center",
																	textAlign: "center",
																	marginLeft: "50px"
																},
															]}>
															{item.item_name}
														</Text>
														<Text style={[styles.para, { width: "100px" }]}>{item.room.name}</Text>
														<Text style={[styles.para, { width: "50px" }]}>
															{item.is_high_value === "true" ? ((item.seal_number !== undefined && item.seal_number !== "") ? `Y/${item.seal_number}` : `Y / ${"$ " + item.declared_value}`) : "No"}
														</Text>
													</View>
													{/* {item.notes ||
											(item.exceptions && item.exceptions.length > 0 && ( */}
													<View style={{ fontSize: 7, padding: "0 3px 2px 3px" }}>
														{item.exceptions && item.exceptions.length > 0 ? (
															<View>
																{item.exceptions.map((e, i) => (
																	<Text style={{ marginBottom: 1 }}>
																		<Text style={{ fontWeight: "bold", letterSpacing: 1 }}>E-{i + 1}: </Text>
																		{e.eid.length > 0
																			? e.eid.map((eid, i) => {
																				return <>{eid.exception_name ? eid.exception_name + ", " : ""}</>;
																			})
																			: ""}
																		{e.lid.length > 0
																			? e.lid.map((lid, i) => {
																				return <>{lid.location_name ? lid.location_name + ", " : ""}</>;
																			})
																			: ""}
																		{e.notes ? e.notes + ", " : ""}
																	</Text>
																))}
															</View>
														) : (
															<Text></Text>
														)}
														{item.notes ? (
															<Text style={{ lineHeight: 1.15 }}>
																<Text style={{ fontWeight: "bold" }}>Other Notes: </Text>
																{item.notes}
															</Text>
														) : (
															<Text></Text>
														)}



														{item.comments.length > 0 ? (
															<Text style={{ lineHeight: 1.15 }}>
																<Text style={{ fontWeight: "bold" }}>Customer Comments :</Text>
																<Text>
																	{
																		item.comments.map((e, i) => (
																			<> - {e.comment}. </>

																		))
																	}
																</Text>
															</Text>
														) : (
															<Text></Text>
														)}



													</View>
													{/* ))} */}
												</View>
											);
									})
								) : (
									<View>
										<Text>No Item</Text>
									</View>
								)}

							</View>
						</View>
					}
					{(data && data.total_electronics_items == 0) ?
						<View></View>
						:

						<View style={{
							marginTop: "20px",
							// flexGrow: 0.83
						}}>
							<Text style={{ padding: "0 15px 0 0", marginBottom: "10px" }}>
								Electronics Items Details
							</Text>
							<View
								style={{
									border: "1pt solid #6c757d",
								}}>
								<View style={[styles.header, {
									padding: "0 1px", justifyContent: "flex-start",
									position: "reletive",
									padding: "2px"
								}]} fixed>
									<Text style={[styles.h4, { width: "50px", padding: "5px 0" }]}>Label</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "140px" }]}>Item</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "100px" }]}>Room</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "70px" }]}>Electronics</Text>

								</View>


								{data && data.itemData ? (
									data.itemData.map((item, index) => {
										if (item.is_electronics === "YES" || item.is_electronics === "true")
											return (
												<View
													key={index}
													style={{
														display: "flex",
														flexDirection: "column",
														padding: " 2px 5px",
														borderBottom: data.job_items.length === index + 1 ? "none" : "1pt solid #6c757d",
													}}>
													<View style={[styles.header, { borderBottom: "none", padding: "0 3px 0 0" }]}>
														{
															item.isManualLabel === 1 || item.isManualLabel === "true" || item.isManualLabel === true ?

																<Text style={[styles.para, { width: "25px" }]}>
																	<text>{"M-" + item.color.charAt(0).toUpperCase() + "/"} </text>
																	<text>{item.lot_no + "/"}</text>
																	<text>{item.label_no + ""}</text>
																</Text>
																:
																<>

																	{item.item_qr.type === "Generic" ?
																		<Text style={[styles.para, { width: "25px" }]}>
																			{"G" + item.item_qr.label_number}
																		</Text>
																		:
																		<>

																			{item.item_qr.type === "External" ?
																				<Text style={[styles.para, { width: "25px" }]}>
																					{"E" + item.item_qr.label_number}
																				</Text>
																				:
																				<Text style={[styles.para, { width: "25px" }]}>
																					{item.item_qr.label_number}
																				</Text>
																			}
																		</>
																	}

																</>

														}
														<Text
															style={[
																styles.para,
																{
																	width: "125px",
																	fontWeight: "600",
																	// textAlign: item.item_name.length > 15 ? "left" : "center",
																	textAlign: "center",
																	marginLeft: "50px"
																},
															]}>
															{item.item_name}
														</Text>
														<Text style={[styles.para, { width: "100px" }]}>{item.room.name}</Text>
														<Text style={[styles.para, { width: "75px" }]}>
															{item.is_electronics === "YES" || item.is_electronics === "true" ? `Y / ${item.serial_number}` : "No"}
														</Text>
													</View>
													{/* {item.notes ||
											(item.exceptions && item.exceptions.length > 0 && ( */}
													<View style={{ fontSize: 7, padding: "0 3px 2px 3px" }}>
														{item.exceptions && item.exceptions.length > 0 ? (
															<View>
																{item.exceptions.map((e, i) => (
																	<Text style={{ marginBottom: 1 }}>
																		<Text style={{ fontWeight: "bold", letterSpacing: 1 }}>E-{i + 1}: </Text>
																		{e.eid.length > 0
																			? e.eid.map((eid, i) => {
																				return <>{eid.exception_name ? eid.exception_name + ", " : ""}</>;
																			})
																			: ""}
																		{e.lid.length > 0
																			? e.lid.map((lid, i) => {
																				return <>{lid.location_name ? lid.location_name + ", " : ""}</>;
																			})
																			: ""}
																		{e.notes ? e.notes + ", " : ""}
																	</Text>
																))}
															</View>
														) : (
															<Text></Text>
														)}
														{item.notes ? (
															<Text style={{ lineHeight: 1.15 }}>
																<Text style={{ fontWeight: "bold" }}>Other Notes: </Text>
																{item.notes}
															</Text>
														) : (
															<Text></Text>
														)}



														{item.comments.length > 0 ? (
															<Text style={{ lineHeight: 1.15 }}>
																<Text style={{ fontWeight: "bold" }}>Customer Comments :</Text>
																<Text>
																	{
																		item.comments.map((e, i) => (
																			<> - {e.comment}. </>

																		))
																	}
																</Text>
															</Text>
														) : (
															<Text></Text>
														)}



													</View>
													{/* ))} */}
												</View>
											);
									})
								) : (
									<View>
										<Text>No Item</Text>
									</View>
								)}

							</View>
						</View>
					}

					{(data && data.firearms_total_quantity == 0) ?
						<View></View>
						:

						<View style={{
							marginTop: "20px",
							// flexGrow: 0.83 
						}}>
							<Text style={{ padding: "0 15px 0 0", marginBottom: "10px" }}>
								Firearms Items Details
							</Text>
							<View
								style={{
									border: "1pt solid #6c757d",
								}}>
								<View style={[styles.header, {
									padding: "0 1px", justifyContent: "flex-start",
									position: "reletive",
									padding: "2px"
								}]} fixed>
									<Text style={[styles.h4, { width: "50px", padding: "5px 0" }]}>Label</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "140px" }]}>Item</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "100px" }]}>Room</Text>
									<Text style={[styles.h4, { width: "125px", padding: "5px 0", marginLeft: "70px" }]}>Serial No.</Text>

								</View>


								{data && data.itemData ? (
									data.itemData.map((item, index) => {
										if (item.is_firearm == true || item.is_firearm == "true")
											return (
												<View
													key={index}
													style={{
														display: "flex",
														flexDirection: "column",
														padding: " 2px 5px",
														borderBottom: data.job_items.length === index + 1 ? "none" : "1pt solid #6c757d",
													}}>
													<View style={[styles.header, { borderBottom: "none", padding: "0 3px 0 0" }]}>
														{
															item.isManualLabel === 1 || item.isManualLabel === "true" || item.isManualLabel === true ?

																<Text style={[styles.para, { width: "25px" }]}>
																	<text>{"M-" + item.color.charAt(0).toUpperCase() + "/"} </text>
																	<text>{item.lot_no + "/"}</text>
																	<text>{item.label_no + ""}</text>
																</Text>
																:
																<>

																	{item.item_qr.type === "Generic" ?
																		<Text style={[styles.para, { width: "25px" }]}>
																			{"G" + item.item_qr.label_number}
																		</Text>
																		:
																		<>

																			{item.item_qr.type === "External" ?
																				<Text style={[styles.para, { width: "25px" }]}>
																					{"E" + item.item_qr.label_number}
																				</Text>
																				:
																				<Text style={[styles.para, { width: "25px" }]}>
																					{item.item_qr.label_number}
																				</Text>
																			}
																		</>
																	}

																</>

														}
														<Text
															style={[
																styles.para,
																{
																	width: "125px",
																	fontWeight: "600",
																	// textAlign: item.item_name.length > 15 ? "left" : "center",
																	textAlign: "center",
																	marginLeft: "50px"
																},
															]}>
															{item.item_name}
														</Text>
														<Text style={[styles.para, { width: "100px" }]}>{item.room.name}</Text>
														<Text style={[styles.para, { width: "75px" }]}>
															{item.is_firearm == true || item.is_firearm == "true" ? item.firmarm_serial_number : ""}
														</Text>
													</View>
													{/* {item.notes ||
											(item.exceptions && item.exceptions.length > 0 && ( */}
													<View style={{ fontSize: 7, padding: "0 3px 2px 3px" }}>
														{item.exceptions && item.exceptions.length > 0 ? (
															<View>
																{item.exceptions.map((e, i) => (
																	<Text style={{ marginBottom: 1 }}>
																		<Text style={{ fontWeight: "bold", letterSpacing: 1 }}>E-{i + 1}: </Text>
																		{e.eid.length > 0
																			? e.eid.map((eid, i) => {
																				return <>{eid.exception_name ? eid.exception_name + ", " : ""}</>;
																			})
																			: ""}
																		{e.lid.length > 0
																			? e.lid.map((lid, i) => {
																				return <>{lid.location_name ? lid.location_name + ", " : ""}</>;
																			})
																			: ""}
																		{e.notes ? e.notes + ", " : ""}
																	</Text>
																))}
															</View>
														) : (
															<Text></Text>
														)}
														{item.notes ? (
															<Text style={{ lineHeight: 1.15 }}>
																<Text style={{ fontWeight: "bold" }}>Other Notes: </Text>
																{item.notes}
															</Text>
														) : (
															<Text></Text>
														)}



														{item.comments.length > 0 ? (
															<Text style={{ lineHeight: 1.15 }}>
																<Text style={{ fontWeight: "bold" }}>Customer Comments :</Text>
																<Text>
																	{
																		item.comments.map((e, i) => (
																			<> - {e.comment}. </>

																		))
																	}
																</Text>
															</Text>
														) : (
															<Text></Text>
														)}



													</View>
													{/* ))} */}
												</View>
											);
									})
								) : (
									<View>
										<Text>No Item</Text>
									</View>
								)}

							</View>
						</View>
					}

					{/* <View
							style={{
								fontSize: 8,
								width: "5%",
								border: "1px solid #6c757d",
								borderBottom: "2px solid #6c757d",
								borderLeft: "none",
								flexGrow: 0.5,
							}}>
							<Text style={{ borderBottom: "1px solid #6c757d", padding: "6.35px 0" }}>
								Shipment initials
							</Text>
						</View>
						<View
							style={{
								fontSize: 8,
								width: "5%",
								border: "1px solid #6c757d",
								borderBottom: "2px solid #6c757d",
								borderLeft: "none",
							}}>
							<Text style={{ borderBottom: "1px solid #6c757d", padding: "11.15px 0" }}>
								Carrier initials
							</Text>
						</View> */}
					{/* </View> */}

					<View
						style={{
							position: "absolute",
							padding: "5px 10px",
							top: "685px",
							zIndex: 1000000,
						}}
						fixed>
						<Text
							style={{
								fontWeight: 600,
								fontSize: 10,
								textTransform: "capitalize",
							}}>
							<Text style={{ color: "red" }}>  Warning:</Text> Before signing, check your shipment, count items
							& describe any loss or damage next to the item.
						</Text>
						<View style={{ display: "flex", flexDirection: "row", flexGrow: 1, marginTop: 5 }}>
							<View
								style={{
									display: "flex",
									flexDirection: "row",
									// flexGrow: 1,
									width: "50%",
									border: "1px solid #ccc",
									fontSize: 8,
									// marginTop: 5,
									// justifyContent: "space-around",
								}}>
								<View
									style={{
										width: "50px",
										borderRight: "1px solid #ccc",
										fontSize: 10,
										fontWeight: "bold",
										display: "flex",
										flexDirection: "column",
										alignItems: "center",
										justifyContent: "center",
									}}>
									<Text style={{ textAlign: "center" }}>At</Text>
									<Text style={{ textAlign: "center" }}>Origin</Text>
								</View>
								<View style={{ display: "flex", flexDirection: "column" }}>
									<View style={{ display: "flex", flexDirection: "row", borderBottom: "1px solid #ccc" }}>
										<div style={{
											borderRight: "1px solid #ccc",
											textAlign: "center",
											width: "200px",
										}}
										>
											<Text>
												Contractor, Carrier or Rep.(Driver) Signature
											</Text>
											<Image
												style={{ width: 40, height: 35, marginLeft: "80px" }}
												src={supervisorSignatureRequireAtOriginToAllPages(data) ? supervisorSignatureRequireAtOriginToAllPages(data) : noSign}
											/>
										</div>
										<Text style={{ padding: "0 10px", paddingBottom: 25, textAlign: "center", size: "5px" }}> {supervisorSignatureRequireAtOriginToAllPagesDate(data) ? supervisorSignatureRequireAtOriginToAllPagesDate(data) : "Date"}</Text>
									</View>
									<View style={{ display: "flex", flexDirection: "row" }}>
										<div style={{
											borderRight: "1px solid #ccc",
											textAlign: "center",
											width: "200px",
										}}
										>
											<Text>
												Customer or Authorized Agent Signature
											</Text>
											<Image
												style={{ width: 40, height: 35, marginLeft: "80px" }}
												src={customerSignatureRequireAtOriginToAllPages(data) ? customerSignatureRequireAtOriginToAllPages(data) : noSign}
											/>
										</div>
										<Text style={{ padding: "0 10px", paddingBottom: 25, textAlign: "center", size: "5px" }}>{customerSignatureRequireAtOriginToAllPagesDate(data) ? customerSignatureRequireAtOriginToAllPagesDate(data) : "Date"}</Text>
									</View>
								</View>
							</View>
							<View
								style={{
									display: "flex",
									flexDirection: "row",
									// flexGrow: 1,
									width: "50%",
									border: "1px solid #ccc",
									borderLeft: "none",
									fontSize: 8,
									// marginTop: 5,
									// justifyContent: "space-between",
								}}>
								<View
									style={{
										width: "50px",
										borderRight: "1px solid #ccc",
										fontSize: 10,
										fontWeight: "bold",
										display: "flex",
										flexDirection: "column",
										alignItems: "center",
										justifyContent: "center",
									}}>
									<Text style={{ textAlign: "center" }}>At</Text>
									<Text style={{ textAlign: "center" }}>Destination</Text>
								</View>
								<View
									style={{
										display: "flex",
										flexDirection: "column",
									}}>
									<View style={{ display: "flex", flexDirection: "row", borderBottom: "1px solid #ccc" }}>
										<div style={{
											borderRight: "1px solid #ccc",
											textAlign: "center",
											width: "200px",
										}}
										>
											<Text>
												Contractor, Carrier or Rep.(Driver) Signature
											</Text>
											<Image
												style={{ width: 40, height: 35, marginLeft: "80px" }}
												src={supervisorSignatureRequireAtDestinationToAllPages(data) ? supervisorSignatureRequireAtDestinationToAllPages(data) : noSign}
											/>
										</div>
										<Text style={{ padding: "0 10px", paddingBottom: 25, textAlign: "center" }}>{supervisorSignatureRequireAtDestinationToAllPagesDate(data) ? supervisorSignatureRequireAtDestinationToAllPagesDate(data) : "Date"}</Text>

									</View>
									<View style={{ display: "flex", flexDirection: "row" }}>
										<div style={{
											borderRight: "1px solid #ccc",
											textAlign: "center",
											width: "200px",
										}}
										>
											<Text>
												Customer or Authorized Agent Signature
											</Text>
											<Image
												style={{ width: 40, height: 35, marginLeft: "80px" }}
												src={customerSignatureRequireAtDestinationToAllPages(data) ? customerSignatureRequireAtDestinationToAllPages(data) : noSign}
											/>
										</div>
										<Text style={{ padding: "0 10px", paddingBottom: 25, textAlign: "center" }}>{customerSignatureRequireAtDestinationToAllPagesDate(data) ? customerSignatureRequireAtDestinationToAllPagesDate(data) : "Date"}</Text>
									</View>
								</View>
							</View>
						</View>
					</View>

				</View>
				{/* footer */}




				{/* signature history */}
				<View
					style={[
						styles.section,
						{
							border: "1pt solid #6c757d",
							// marginTop: 5,
						},
					]}
					break>
					<View style={styles.header2}>
						<Text style={[styles.h4, { width: "20px", alignItems: "center" }]}>No.</Text>
						<Text style={[styles.h4, { width: "80px", alignItems: "center" }]}>Stage</Text>
						<Text style={[styles.h4, { width: "270px", alignItems: "center" }]}>User</Text>
						<Text style={[styles.h4, { width: "270px", alignItems: "center" }]}>Customer</Text>
						<Text style={[styles.h4, { width: "80px", alignItems: "center" }]}>Date/Time</Text>
					</View>
					{data && data.shipment_type && data.shipment_type.shipment_stage ? (
						data.shipment_type.shipment_stage.map((stage, index) => {
							return (
								<View style={[styles.header2, { minHeight: "250px", marginTop: "0px" }]} key={index} break={(index + 1) % 9 === 0}>
									<Text style={[styles.para, { width: "20px", alignItems: "center" }]}>{index + 1}</Text>
									<Text style={[styles.para, { width: "80px", alignItems: "center" }]}>{stage.name}</Text>
									<div style={{ minHeight: "250px", width: "250px", padding: "5px 10px" }} >
										<Image
											style={{ width: 60, height: 60, margin: "5px 0px 0px 50px" }}
											src={{
												uri: stage.supervisor_signature ? stage.supervisor_signature : noSign,
												method: "GET",
												headers: { "Cache-Control": "no-cache" },
												body: "",
											}}
										/>
										<Text style={[styles.para, { width: "190px", textAlign: "center", alignItems: "center", marginTop: "5px" }]}> {stage.supervisor_signature ? (stage.why_supervisor_signature_require_note ? stage.why_supervisor_signature_require_note : "") : ""}</Text>
									</div>
									<div style={{ minHeight: "250px", width: "250px", padding: "5px 10px" }} >
										<Image
											style={{ width: 60, height: 60, margin: "5px 0px 0px 50px" }}
											src={{
												uri: stage.customer_signature ? stage.customer_signature : noSign,
												method: "GET",
												headers: { "Cache-Control": "no-cache" },
												body: "",
											}}
										/>
										<Text style={[styles.para, { width: "190px", textAlign: "center", marginTop: "5px" }]}>{stage.customer_signature ? (stage.why_customer_signature_require_note ? stage.why_customer_signature_require_note : "") : ""}</Text>
									</div>
									<Text style={[styles.para, { width: "80px", alignItems: "center", padding: "5px" }]}>
										{stage.created_at ? toDate(stage.created_at) : " - "}
									</Text>
								</View>
							);
						})
					) : (
						<View>
							<Text style={{ marginTop: "25px", textAlign: "center" }}>No Signature History</Text>
						</View>
					)}
				</View>
			</Page>
		</Document >
	);

};

function App(props) {
	const [btnLoading, setBtnLoading] = React.useState(false);
	return (
		<div>
			<Button
				onClick={async () => {
					setBtnLoading(true);
					const doc = <MyDoc data={props.data} />
					const html = ReactDOMServer.renderToStaticMarkup(<MyDoc data={props.data} />)
					const asPdf = pdf([]);
					asPdf.updateContainer(doc);
					const blob = await asPdf.toBlob();
					saveAs(blob, `${props.data.customer_job.first_name}-${props.data.job_number}.pdf`);
					setBtnLoading(false);
					message.success("Pdf Downloaded!");
				}}
				style={{
					color: "#fff",
					backgroundColor: "#1890ff",
					borderColor: "#1890ff",
					// padding: "5px 10px",
					borderRadius: "4px",
					letterSpacing: "1px",
					boxShadow: "none",
					textDecoration: "uppercase",
				}}
				type='primary'
				icon='printer'
				loading={btnLoading}>
				Print
			</Button>
		</div>
	);
}

export default App;
