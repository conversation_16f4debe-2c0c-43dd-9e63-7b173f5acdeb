import { Button, Form, Icon, Input, message, Spin } from "antd";
import React from "react";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";

const { changeCurrent } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddRoomList extends React.Component {
	state = {
		loading: false,
		contentloader: false,
		adminId: "",
		companyId: "",
		staffId: "",

	};


	componentDidMount() {
		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});
	}



	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {
			let params = {
				room_name: values.name,
				admin_id: this.state.adminId,
				company_id: this.state.companyId,
				staff_id: this.state.staffId,
			};


			if (!err) {
				const data = [];

				data["data"] = params;
				this.setState({ loading: true });
				API.post("api/admin/room", data)
					.then((response) => {
						if (response.status === 1) {
							this.setState({ loading: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false });
					});
			}
		});
	};

	render() {
		const { getFieldDecorator } = this.props.form;


		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add Room
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent
					style={{
						margin: "0 20px",
						height: "93%",
						overflowY: "auto",
					}}>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Name'>
									{getFieldDecorator("name", {
										placeholder: "Room Name",
										rules: [
											{
												required: true,
												message: "Please input room name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Room Name' />)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Add Room
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(AddRoomList));
