import { But<PERSON>, Col, Icon, Input, message, Modal, Row, Spin, Table, Tooltip, Checkbox } from "antd";
import React from "react";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import queryString from 'query-string';

const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let shipment_room_id;

export default class roomListManagement extends React.Component {
	state = {
		apiParam: {},
		roomList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		adminId: "",
		companyId: "",
		staffId: "",
		batchAction: [],
		isCheckedAllQrCodes: false,
		filterStatus: "Active"
	};
	componentDidMount() {
		const queryParams = queryString.parse(window.location.search);
		let params = {
			search: "",
			order_by_fields: "created_at",
			page_no: parseInt(queryParams.page) || "1",
			order_sequence: "ASC",
			page_size: 25,
			filter: "Active",
		};
		this.setState({ apiParam: params }, () => this.fetchRoomList());
		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});
	}
	fetchRoomList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		const { search, page_no, order_sequence, page_size, filter } = this.state.apiParam;
		this.setState({ pageloading: true });
		API.get(
			`api/admin/room/list?order_by_fields=name&order_sequence=${order_sequence}&page_no=${page_no}&search=${search}&page_size=${page_size}&filter=${filter}`
		)
			.then((response) => {
				const pagination = { ...this.state.pagination };
				if (response) {

					if (response.status === 1) {
						pagination.total = response.data.count;

						this.setState({
							roomList: response.data.rows,
							pagination,
							pageloading: false,
						});

					} else {
						this.setState({
							usersList: response.data,
							pagination,
							pageloading: false,
						});
						// message.error(res.message)
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	};
	handleChange = (pagination, filters, sorter) => {
		scrollTo();
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		const queryParams = queryString.parse(window.location.search);
		queryParams.page = pagination.current;
		const newUrl = `${window.location.pathname}?${queryString.stringify(queryParams)}`;
		window.history.pushState(null, null, newUrl);
		let params = {
			...this.state.apiParam,
			page_no: pagination.current,
			order_by_fields: sorter.field === "createdAt" ? "created_at" : sorter.field,
			order_sequence: sorter.order === "ascend" ? "ASC" : sorter.order === "descend" ? "DESC" : "ASC",
			page_size: pagination.pageSize,
			filter: filters.status && filters.status[0] ? filters.status[0] : "Active",
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
				filterStatus: filters.status && filters.status[0] ? filters.status[0] : "Active",
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchRoomList();
			}
		);
	};
	addRoom() {
		this.props.history.push(`${this.props.match.url}/add`);
	}
	delete(id) {
		this.setState({ deleteModal: true });
		shipment_room_id = id;
	}
	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}

	handleCancel = () => {
		this.setState({
			deleteModal: false,
			confirmLoading: false,
		});
	};

	batchDelete() {
		let params = {
			roomList: this.state.batchAction
		};
		const data = [];
		data["data"] = params;
		this.setState({ confirmLoading: true });
		API.post("api/admin/room/batch-delete-room-list", data)
			.then((response) => {
				if (response) {
					this.setState({
						confirmLoading: false,
						batchAction: [],
					});
					this.fetchRoomList();
					message.success(response.message);
				}
				else {
					this.setState({
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
	}

	batchStatusChange(flag) {
		let params = {
			roomList: this.state.batchAction,
			isActiveFlag: flag
		};
		const data = [];
		data["data"] = params;
		this.setState({ confirmLoading: true });
		API.post("api/admin/room/batch-room-list-status-change", data)
			.then((response) => {
				if (response) {
					this.setState({
						confirmLoading: false,
						batchAction: [],
					});
					this.fetchRoomList();
					message.success(response.message);
				}
				else {
					this.setState({
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
	}


	handleOk = () => {
		const id = shipment_room_id;
		this.setState({ confirmLoading: true });
		API.delete(`api/admin/room/${id}`)
			.then((response) => {
				if (response && response.status == 1) {
					this.setState({
						batchAction: [],
					});
					this.fetchRoomList();
					message.success(response.message);
				} else {
					message.error(response.message);
				}
				this.setState({
					deleteModal: false,
					confirmLoading: false,
				});
			})
			.catch((error) => {
				this.setState({
					confirmLoading: false,
					deleteModal: false,
				});
			});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchRoomList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					page_no: 1,
				},
				search: e,
			},
			this.callback
		);
	};
	handleStatusChange = (id) => {
		const statusData = [];
		statusData["data"] = { shipment_room_id: id, isInactive: true };
		API.post("api/admin/room/change-room-status", statusData).then((response) => {
			if (response.status === 1) {
				this.setState({
					batchAction: [],
				});
				this.fetchRoomList({
					page: this.state.pagination.current ? this.state.pagination.current : 1,
				});
				message.success(response.message);
			} else {
				message.error(response.message);
			}
		});
	};


	checkBoxHandler = (event, id) => {
		if (event.target.checked) {
			this.setState(prevState => ({
				batchAction: [...prevState.batchAction, id]
			}))
		}
		else {
			this.setState({
				batchAction: this.state.batchAction.filter((data) => {
					return data !== id
				})
			})
		}
	}

	onSelectChange = (newSelectedRowKeys, selectedRows) => {
		const selectedIds = selectedRows.map(row => row.shipment_room_id);
		this.setState({
			batchAction: selectedIds,
		});
	};

	handleCheckAll = (event) => {
		if (event.target.checked) {
			const { roomList } = this.state;
			const filteredRoomList = roomList.filter(record => record.totalItems === 0);
			const selectedIds = filteredRoomList.map(record => record.shipment_room_id);
			this.setState({
				batchAction: selectedIds,
			});
		}
		else {
			this.setState({
				batchAction: [],
			});
		}
	};

	render() {
		const columns = [
			{
				title: <Checkbox
					checked={this.state.batchAction.length > 0}
					onChange={(e) => this.handleCheckAll(e)}
				/>,
				key: 'select',
				width: '5%',
				render: (text, record) => (
					<Checkbox
						disabled={record.totalItems > 0}
						checked={this.state.batchAction.includes(record.shipment_room_id)}
						onChange={(e) => this.checkBoxHandler(e, record.shipment_room_id)}
					/>
				),
				align: 'center',
			},
			{
				title: "Name",
				dataIndex: "name",
				key: "name",
				width: "40%",
				sorter: true,
			},
			{
				title: "Status",
				dataIndex: "status",
				key: "status",
				width: "15%",
				align: "center",
				filterMultiple: false,
				filtered: true,
				defaultFilteredValue: ["active"],
				filters: [
					{ text: "Active", value: "Active" },
					{ text: "Inactive", value: "Inactive" },
				],
				render: (text, record) => {
					return (
						<div>
							<button
								className={record.status === "Active" ? "statusButtonActive" : "statusButtoninactive"}
								onClick={() => this.handleStatusChange(record.shipment_room_id)}>
								<Icon type='swap' />
								{record.status === "Active" ? " Active" : " Inactive"}
							</button>

						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				align: "center",
				render: (record, text) => {
					return (
						<div className='icons'>
							<Tooltip title='Edit'>
								<Button
									type='primary'
									className='c-btn c-round c-warning'
									icon='edit'
									onClick={() => this.edit(text.shipment_room_id)}></Button>
							</Tooltip>
							<Tooltip title='Delete'>
								<Button
									type='primary'
									className='c-btn c-round c-danger'
									icon='delete'
									onClick={() => this.delete(text.shipment_room_id)}></Button>
							</Tooltip>
						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<Col sm={8}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' /> &emsp;Room List Management
							</h2>
						</Col>
						<Col sm={6}>
							{this.state.search ? (
								<p
									style={{
										margin: "5px",
										textAlign: "center",
										color: "#303030",
									}}>
									Matching Records: {this.state.pagination.total}
								</p>
							) : (
								""
							)}
						</Col>

						{this.state.batchAction.length > 1 ?
							<Col sm={10}>
								<Row>
									{this.state.filterStatus == "Inactive" ?
										<Col sm={16}>
											<Button
												className='addButton'
												style={{ marginTop: "0" }}
												type='primary'
												onClick={() => this.batchStatusChange(true)}>
												Batch Active
											</Button>
										</Col>
										: ""}

									{this.state.filterStatus == "Active" ?
										<Col sm={16}>
											<Button
												className='addButton'
												style={{ marginTop: "0" }}
												type='primary'
												onClick={() => this.batchStatusChange(false)}>
												Batch Inactive
											</Button>
										</Col>
										: ""}

									<Col sm={8}>
										<Button
											className='addButton'
											style={{ marginTop: "0" }}
											type='danger'
											onClick={() => this.batchDelete()}>
											Batch Delete
										</Button>
									</Col>

								</Row>
							</Col>
							:

							<Col sm={10}>
								<Row>
									<Col sm={16} style={{ textAlign: "right" }}>
										<Search
											placeholder='Search room name'
											onChange={(e) => this.handleSearch(e.target.value)}
											value={this.state.search}
											style={{ width: 200 }}
										/>
									</Col>
									{
										<Col sm={8}>
											<Button
												className='addButton'
												style={{ marginTop: "0" }}
												type='primary'
												onClick={() => this.addRoom()}>
												+ Add Room
											</Button>
										</Col>
									}
								</Row>
							</Col>
						}
					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div >
							<Table
								bordered={true}
								columns={columns}
								pagination={{
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 25,
									pageSizeOptions: ["25", "50"],
									current: this.state.apiParam.page_no,
								}}
								dataSource={this.state.roomList}
								onChange={this.handleChange}
							/>

						</div>
					</LayoutContent>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to delete this room?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}
