import styled from "styled-components";

const LayoutContentWrapper = styled.div`
  padding: 40px 20px;
  display: flex;
  flex-flow: row wrap;
  overflow: hidden;

  @media only screen and (max-width: 767px) {
    padding: 50px 20px;
  }

  @media (max-width: 580px) {
    padding: 15px;
  }

  .title-container{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
  }

  .action-button-container{
    display:flex;
    align-items:center;
    justify-content:flex-start;
    .item{
      margin-left : 5px; 
    }
  }
  
`;

export { LayoutContentWrapper };
