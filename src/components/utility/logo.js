import React from 'react';
import { Link } from 'react-router-dom';
import { siteConfig } from '../../settings';
import LogoIcon from '../../static/images/logo.png';

export default ({ collapsed }) => {
  return (
    <div className="isoLogoWrapper">
      {collapsed ? (
        <div>
          <h3>
            <Link to="/dashboard">
              <img src={LogoIcon} style={{ height: "32px", width: '32px' }} alt="Mover Inventory" />
            </Link>
          </h3>
        </div>
      ) : (
        <h3>
          <Link to="/dashboard">{siteConfig.siteName}</Link>
        </h3>
      )}
    </div>
  );
};
