import { Button, Form, Icon, Input, message, Select, Spin, Upload, List } from "antd";
import React from "react";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import PlacesAutocomplete, {
	geocodeByAddress,
} from 'react-places-autocomplete';
import { GOOGLE_API_KEY } from "../../static/data/constants";

const { TextArea } = Input;
const { Option } = Select;
// alert('hi');
const { changeCurrent } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddCompany extends React.Component {
	state = {
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		groupList: [],
		groupDataFromField: null,
		PickupAddress: "",
		PickupCity: "",
		PickupCountry: "",
		PickupState: "",
		PickupZipCode: "",
	};

	componentDidMount() {

		this.setState({
			loading: true,
			contentloader: true
		});

		API.get(
			`api/admin/group/list`
		)
			.then((response) => {
				if (response.status === 1) {
					this.setState({
						groupList: response.data.rows,
						loading: false,
						contentloader: false
					});

				}
				else {
					this.setState({
						groupList: [],
						loading: false,
						contentloader: false
					});
				}

			})
			.catch((error) => {
				this.setState({
					ploading: false,
					contentloader: false
				});
			});
	}

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {
		// you store them in state, so that you can make a http req with them later
		if (fileList.length > 1) {
			message.error("Only one image is allowed!");
		} else {
			this.setState({ fileList });
		}
	};

	OnGroupChange = (values) => {
		this.setState({ groupDataFromField: values });
	};

	handleSubmit = (e) => {
		e.preventDefault();
		let formData = new FormData();

		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];
				formData.append("company_name", (values.company_name !== undefined) ? values.company_name : "");
				formData.append("company_identity", (values.company_identity !== undefined) ? values.company_identity.replace(/\s/g, "") : "");
				formData.append("email", (values.email !== undefined) ? values.email : "");
				formData.append("phone", (values.phone !== undefined) ? values.phone : "");
				formData.append("group_id", (values.group_id !== undefined && values.group_id.key !== undefined) ? values.group_id.key : "");
				formData.append("country_code", (values.country_code !== undefined) ? values.country_code : "");
				formData.append("address1", this.state.PickupAddress);
				formData.append("address2", (values.address2 !== undefined) ? values.address2 : "");
				formData.append("city", (values.city !== undefined) ? values.city : "");
				formData.append("state", (values.state !== undefined) ? values.state : "");
				formData.append("zipCode", (values.zipCode !== undefined) ? values.zipCode : "");
				formData.append("country", (values.country !== undefined) ? values.country : "");
				formData.append("notes", (values.notes !== undefined) ? values.notes : "");
				formData.append(
					"photo",
					values.logo && values.logo !== "" && this.state.fileList.length > 0
						? this.state.fileList[0].originFileObj
						: ""
				);
				formData.append("password", values.company_identity.replace(/\s/g, "") + "@123");
				data["data"] = formData;
				this.setState({ loading: true, contentloader: true });

				API.post("api/admin/company/add", data)
					.then((response) => {
						if (response.status === 1) {
							this.setState({ loading: false, contentloader: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false, contentloader: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false, contentloader: false });
					});
			}
		});
	};

	normFile = (e) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e && e.fileList;
	};

	handleChangePickupAddress = PickupAddress => {
		this.setState({ PickupAddress });
	};

	handleSelectPickupAddress = (PickupAddress, placeId) => {
		if (placeId !== undefined && placeId !== "" && placeId !== null) {
			this.fetchPlaceDetails(placeId);
		}
		else {
			this.setState({ PickupAddress: PickupAddress });
		}
	};

	fetchPlaceDetails = async (placeId) => {

		let params = {
			placeId: placeId,
		};
		const data = [];
		data["data"] = params;
		this.setState({ loading: true, contentloader: true });

		API.post("api/admin/google/place/details", data)
			.then((response) => {
				const addressComponents = response.data && response.data.result && response.data.result.address_components;

				// Initialize variables to store city, state, and country
				let address, city, state, country, zipCode;

				// Iterate through the address components to find city, state, and country
				for (const component of addressComponents) {
					for (const type of component.types) {
						if (type === 'street_number' || type === 'route') {
							address = address ? `${address} ${component.long_name}` : component.long_name;
						} else if (type === 'locality') {
							city = component.long_name;
						} else if (type === 'administrative_area_level_1') {
							state = component.long_name;
						} else if (type === 'country') {
							country = component.long_name;
						} else if (type === 'postal_code') {
							zipCode = component.long_name;
						}
					}
				}

				this.setState({
					PickupAddress: address,
					PickupCity: city,
					PickupState: state,
					PickupCountry: country,
					PickupZipCode: zipCode
				});

				// Update form fields after getting new address details
				this.props.form.setFieldsValue({
					city: city,
					state: state,
					zipCode: zipCode,
					country: country,
				});
				this.setState({ loading: false, contentloader: false });
			})
			.catch((error) => {
				this.setState({ loading: false, contentloader: false });
				message.error(error.message);
			});
	};

	autocompleteDropdownStyle = {
		position: 'absolute',
		top: '100%',
		left: 0,
		width: '500px',
		zIndex: 1,
		backgroundColor: '#ffffff',
		border: '1px solid #ccc',
		boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
		maxHeight: '200px',
		overflowY: 'auto',
		padding: '5px',
	};

	render() {
		const { getFieldDecorator } = this.props.form;

		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		const countryCodeSelector = getFieldDecorator("country_code", {
			initialValue: "1",
		})(
			<Select disabled style={{ width: 70 }}>
				<Option value='1'>+1</Option>
			</Select>
		);
		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add Company
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent
					style={{
						margin: "0 20px",
						height: "93%",
						overflowY: "auto",
					}}>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Company Name'>
									{getFieldDecorator("company_name", {
										placeholder: "Company Name",
										rules: [
											{
												required: true,
												message: "Please input company name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Company Name' />)}
								</Form.Item>
								<Form.Item label='Company ID'>
									{getFieldDecorator("company_identity", {
										placeholder: "Company ID",
										rules: [
											{
												required: true,
												message: "Please input company id!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Company ID' />)}
								</Form.Item>

								<Form.Item label='Company Group'>
									{getFieldDecorator("group_id", {

									})(
										<Select
											size={"default"}
											showSearch
											labelInValue
											optionFilterProp='children'
											placeholder='Select Company Group Name'
											style={{ width: "100%" }}
										>{this.state.groupList
											? this.state.groupList.map((e) => (
												<Option
													key={e.group_id}
													value={e.group_id}
													onClick={this.OnGroupChange.bind(null, e)}>
													{e.name}
												</Option>
											))
											: null}
										</Select>
									)}
								</Form.Item>

								<Form.Item label='Company Email'>
									{getFieldDecorator("email", {
										rules: [
											{
												type: "email",
												message: "The input is not valid Company Email!",
											},
											{
												required: true,
												message: "Please input your Company Email!",
											},
										],
									})(<Input placeholder='Company Email' />)}
								</Form.Item>

								<Form.Item label='Company Phone Number'>
									{getFieldDecorator("phone", {
										// rules: [
										// 	{
										// 		pattern: /^(\+\d{1,2}\s)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
										// 		message: "Please input 10 digit number!",
										// 	},
										// ],
									})(
										<Input
											// addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Company Phone Number'
											customInput={Input}
											maxLength={20}
										// format='(###) ###-####'
										/>
									)}
								</Form.Item>

								{/* <Form.Item label='Address Line 1'>
									{getFieldDecorator("address1", {})(<Input placeholder='Company Address Line1' />)}
								</Form.Item> */}

								<PlacesAutocomplete
									value={this.state.PickupAddress}
									onChange={this.handleChangePickupAddress}
									onSelect={this.handleSelectPickupAddress}
								>
									{({ getInputProps, suggestions, getSuggestionItemProps, loading }) => (
										<div>
											<Form.Item label='Address Line 1'>
												<div style={{ position: 'relative' }}>
													<Input
														{...getInputProps({
															placeholder: 'Address Line 1 ...',
														})}
													/>
													{suggestions.length > 0 && (
														<div
															style={this.autocompleteDropdownStyle}
															className="autocomplete-dropdown-container"
														>
															{loading && <div>Loading...</div>}
															{suggestions.map(suggestion => {
																const style = {
																	backgroundColor: suggestion.active ? '#f1f2f6' : '#ffffff',
																	cursor: 'pointer',
																};
																return (
																	<div
																		key={suggestion.id} // Add a unique key for each suggestion
																		{...getSuggestionItemProps(suggestion, {
																			style,
																		})}
																	>
																		<List.Item>
																			<List.Item.Meta
																				description={suggestion.description}
																			/>
																		</List.Item>
																	</div>
																);
															})}
														</div>
													)}
												</div>
											</Form.Item>

										</div>
									)}
								</PlacesAutocomplete>

								<Form.Item label='Address Line 2'>
									{getFieldDecorator("address2", {})(<Input placeholder='Company Address Line2' />)}
								</Form.Item>

								<Form.Item label='Company City'>
									{getFieldDecorator("city", {
										initialValue: this.state.PickupCity
									})(<Input placeholder='Company City' />)}
								</Form.Item>

								<Form.Item label='Company State'>
									{getFieldDecorator("state", {
										initialValue: this.state.PickupState
									})(<Input placeholder='Company State' />)}
								</Form.Item>

								<Form.Item label='Company Zipcode'>
									{getFieldDecorator("zipCode", {
										initialValue: this.state.PickupZipCode
									})(<Input placeholder='Company Zipcode' />)}
								</Form.Item>

								<Form.Item label='Company Country'>
									{getFieldDecorator("country", {
										initialValue: this.state.PickupCountry
									})(<Input placeholder='Company Country' />)}
								</Form.Item>

								<Form.Item label='Company Notes'>
									{getFieldDecorator("notes", {})(<TextArea rows={4} placeholder='Company Notes' />)}
								</Form.Item>

								<Form.Item label='Company Logo'>
									{getFieldDecorator("logo", {
										valuePropName: "fileList",
										getValueFromEvent: this.normFile,
									})(
										<Upload
											listType='picture-card'
											// fileList={this.state.fileList}
											multiple={false}
											onPreview={this.handlePreview}
											onChange={this.handleUpload}
											beforeUpload={() => false} // return false so that antd doesn't upload the picture right away
											showUploadList={{
												showRemoveIcon: true,
												showPreviewIcon: false,
											}}>
											{this.state.fileList && this.state.fileList.length < 1 && (
												<Button>
													<Icon type='upload' /> Click to Upload
												</Button>
											)}
										</Upload>
									)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Add Company
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(AddCompany));
