import { Button, Form, Icon, Input, message, Select, Spin, Upload, List } from "antd";
import React from "react";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';
import PlacesAutocomplete, {
	geocodeByAddress,
} from 'react-places-autocomplete';
import { GOOGLE_API_KEY } from "../../static/data/constants";

const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { TextArea } = Input;
const { Option } = Select;

class EditCompany extends React.Component {
	state = {
		companyData: null,
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		integrationKeyStatus: false,
		integrationKey: "",
		storageCompanyId: "",
		superAdminAccessToken: "",
		consumerLoginAccessToken: "",
		groupList: [],
		PickupAddress: "",
		PickupCity: "",
		PickupCountry: "",
		PickupState: "",
		PickupZipCode: "",

	};
	componentDidMount() {
		const id = this.props.match.params.id;
		this.props.changeCurrent("company");
		this.setState({ contentloader: true });

		const data = [];
		data["data"] = { company_id: id };
		API.post("api/admin/company/view-company", data)
			.then((response) => {
				if (response) {
					this.setState(
						{

							companyData: response.data && response.data,
							PickupAddress: response.data && response.data.address1 !== "undefined" ? response.data.address1 : "",
							PickupCity: response.data && response.data.city !== "undefined" ? response.data.city : "",
							PickupState: response.data && response.data.state !== "undefined" ? response.data.state : "",
							PickupZipCode: response.data && response.data.zipCode !== "undefined" ? response.data.zipCode : "",
							PickupCountry: response.data && response.data.country !== "undefined" ? response.data.country : "",

							fileList:
								response.data && response.data.company_logo !== "" && response.data.company_logo !== undefined && response.data.company_logo !== null ?
									[
										{
											uid: -1,
											status: "done",
											url: response.data.company_logo,
										},
									] : [],

							previewImage: response.data.company_logo,
							previewVisible: true,
							contentloader: false,

						},
						() => this.props.form.setFieldsValue({})
					);
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({ contentloader: false });
			});


		API.get(
			`api/admin/group/list`
		)
			.then((response) => {
				if (response.status === 1) {
					this.setState({
						groupList: response.data.rows,
						contentloader: false
					});

				}
				else {
					this.setState({
						groupList: [],
						contentloader: false
					});
				}

			})
			.catch((error) => {
				this.setState({
					contentloader: false
				});
			});
	}

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {
		// you store them in state, so that you can make a http req with them later
		if (fileList.length > 1) {
			message.error("Only one image is allowed!");
		} else {
			this.setState({ fileList });
		}
	};


	handleSubmit = (e) => {
		e.preventDefault();
		let formData = new FormData();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];

				formData.append("company_id", this.props.match.params.id);
				formData.append("company_name", values.company_name);
				formData.append("address1", this.state.PickupAddress);
				formData.append("group_id", (values.group_id !== undefined && values.group_id.key !== undefined) ? values.group_id.key : "");
				formData.append("address2", values.address2);
				formData.append("city", values.city);
				formData.append("state", values.state);
				formData.append("zipCode", values.zipCode);
				formData.append("country", values.country);
				formData.append("phone", values.phone);
				formData.append("country_code", values.country_code);
				formData.append("company_identity", values.company_identity);
				formData.append("email", values.email);
				formData.append("notes", values.notes);
				formData.append(
					"photo",
					values.logo && values.logo !== "" && this.state.fileList.length > 0
						? this.state.fileList[0].originFileObj
						: ""
				);

				data["data"] = formData;
				this.setState({ loading: true, contentloader: true });

				API.post("api/admin/company/edit-company", data)
					.then((response) => {
						if (response) {
							this.setState({ loading: false, contentloader: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false, contentloader: false });
							// message.error(response.message);
						}
					})
					.catch((error) => {
						// message.error(error.message);
						this.setState({ loading: false, contentloader: false });
					});
			}
		});
	};

	handleChangePickupAddress = PickupAddress => {
		this.setState({ PickupAddress });
	};

	handleSelectPickupAddress = (PickupAddress, placeId) => {
		if (placeId !== undefined && placeId !== "" && placeId !== null) {
			this.fetchPlaceDetails(placeId);
		}
		else {
			this.setState({ PickupAddress: PickupAddress });
		}
	};

	fetchPlaceDetails = async (placeId) => {
		let params = {
			placeId: placeId,
		};
		const data = [];
		data["data"] = params;
		this.setState({ loading: true, contentloader: true });

		API.post("api/admin/google/place/details", data)
			.then((response) => {

				const addressComponents = response.data && response.data.result && response.data.result.address_components;

				// Initialize variables to store city, state, and country
				let address, city, state, country, zipCode;

				// Iterate through the address components to find city, state, and country
				for (const component of addressComponents) {
					for (const type of component.types) {
						if (type === 'street_number' || type === 'route') {
							address = address ? `${address} ${component.long_name}` : component.long_name;
						} else if (type === 'locality') {
							city = component.long_name;
						} else if (type === 'administrative_area_level_1') {
							state = component.long_name;
						} else if (type === 'country') {
							country = component.long_name;
						} else if (type === 'postal_code') {
							zipCode = component.long_name;
						}
					}
				}

				this.setState({
					PickupAddress: address,
					PickupCity: city,
					PickupState: state,
					PickupCountry: country,
					PickupZipCode: zipCode
				});
				this.props.form.setFieldsValue({
					city: city,
					state: state,
					zipCode: zipCode,
					country: country,
				});
				this.setState({ loading: false, contentloader: false });
			})
			.catch((error) => {
				this.setState({ loading: false, contentloader: false });
				message.error(error.message);
			});
	};

	autocompleteDropdownStyle = {
		position: 'absolute',
		top: '100%',
		left: 0,
		width: '500px',
		zIndex: 1,
		backgroundColor: '#ffffff',
		border: '1px solid #ccc',
		boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
		maxHeight: '200px',
		overflowY: 'auto',
		padding: '5px',
	};


	render() {
		const { getFieldDecorator } = this.props.form;
		const { companyData } = this.state;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		console.log("companyData", companyData)
		const initialCompanyGroup =
			companyData && companyData.group_id === "" ? {} : { key: companyData && companyData.group_id };

		const countryCodeSelector = getFieldDecorator("country_code", {
			initialValue: companyData && companyData.country_code,
		})(
			<Select disabled style={{ width: 70 }}>
				<Option value='1'>+1</Option>
			</Select>
		);

		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Edit Company
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Company Name'>
									{getFieldDecorator("company_name", {
										initialValue: companyData && companyData.company_name,
										rules: [
											{
												required: true,
												message: "Please input company name!",
												whitespace: true,
											},
										],
									})(<Input />)}
								</Form.Item>
								<Form.Item label='Company ID'>
									{getFieldDecorator("company_identity", {
										initialValue: companyData && companyData.company_identity,
										placeholder: "Company ID",
										rules: [
											{
												required: true,
												message: "Please input company id!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Company ID' />)}
								</Form.Item>

								<Form.Item label='Company Group'>
									{getFieldDecorator("group_id", {
										initialValue: initialCompanyGroup
									})(
										<Select
											size={"default"}
											showSearch
											labelInValue
											optionFilterProp='children'
											placeholder='Select Company Group Name'
											style={{ width: "100%" }}
										>{this.state.groupList
											? this.state.groupList.map((e) => (
												<Option
													key={e.group_id}
													value={e.group_id}
												>
													{e.name}
												</Option>
											))
											: null}
										</Select>
									)}
								</Form.Item>


								<Form.Item label='Company Email'>
									{getFieldDecorator("email", {
										initialValue: companyData && companyData.email,
										rules: [
											{
												type: "email",
												message: "The input is not valid Company Email!",
											},
											{
												required: true,
												message: "Please input your Company Email!",
											},
										],
									})(<Input />)}
								</Form.Item>

								<Form.Item label='Company Phone Number'>
									{getFieldDecorator("phone", {
										initialValue:
											companyData && companyData.phone && companyData.phone !== "undefined"
												? companyData.phone
												: "",
									})(
										<Input
											// addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Company Phone Number'
											customInput={Input}
											maxLength={20}
										// format='(###) ###-####'
										/>
									)}
								</Form.Item>

								{/* <Form.Item label='Address Line 1'>
									{getFieldDecorator("address1", {
										initialValue:
											companyData && companyData.address1 && companyData.address1 !== "undefined"
												? companyData.address1
												: "",
									})(<Input placeholder='Company Address Line 1' />)}
								</Form.Item> */}

								<PlacesAutocomplete
									value={this.state.PickupAddress}
									onChange={this.handleChangePickupAddress}
									onSelect={this.handleSelectPickupAddress}
								>
									{({ getInputProps, suggestions, getSuggestionItemProps, loading }) => (
										<div>
											<Form.Item label='Address Line 1'>
												<div style={{ position: 'relative' }}>
													<Input
														{...getInputProps({
															placeholder: 'Address Line 1 ...',
														})}
													/>
													{suggestions.length > 0 && (
														<div
															style={this.autocompleteDropdownStyle}
															className="autocomplete-dropdown-container"
														>
															{loading && <div>Loading...</div>}
															{suggestions.map(suggestion => {
																const style = {
																	backgroundColor: suggestion.active ? '#f1f2f6' : '#ffffff',
																	cursor: 'pointer',
																};
																return (
																	<div
																		key={suggestion.id} // Add a unique key for each suggestion
																		{...getSuggestionItemProps(suggestion, {
																			style,
																		})}
																	>
																		<List.Item>
																			<List.Item.Meta
																				description={suggestion.description}
																			/>
																		</List.Item>
																	</div>
																);
															})}
														</div>
													)}
												</div>
											</Form.Item>

										</div>
									)}
								</PlacesAutocomplete>

								<Form.Item label='Address Line 2'>
									{getFieldDecorator("address2", {
										initialValue:
											companyData && companyData.address2 && companyData.address2 !== "undefined"
												? companyData.address2
												: "",
									})(<Input placeholder='Company Address Line 2' />)}
								</Form.Item>

								<Form.Item label='Company City'>
									{getFieldDecorator("city", {
										initialValue: this.state.PickupCity
									})(<Input placeholder='Company City' />)}
								</Form.Item>

								<Form.Item label='Company State'>
									{getFieldDecorator("state", {
										initialValue: this.state.PickupState
									})(<Input placeholder='Company State' />)}
								</Form.Item>

								<Form.Item label='Company Zipcode'>
									{getFieldDecorator("zipCode", {
										initialValue: this.state.PickupZipCode
									})(<Input placeholder='Company Zipcode' />)}
								</Form.Item>

								<Form.Item label='Company Country'>
									{getFieldDecorator("country", {
										initialValue: this.state.PickupCountry
									})(<Input placeholder='Company Country' />)}
								</Form.Item>

								<Form.Item label='Company Notes'>
									{getFieldDecorator("notes", {
										initialValue:
											companyData && companyData.notes && companyData.notes !== "undefined"
												? companyData.notes
												: "",
									})(<TextArea rows={4} placeholder='Company Notes' />)}
								</Form.Item>

								<Form.Item label='Company Logo'>
									{getFieldDecorator("logo")(
										<Upload
											listType='picture-card'
											fileList={this.state.fileList}
											multiple={false}
											onChange={this.handleUpload}
											beforeUpload={() => false} // return false so that antd doesn't upload the picture right away
											showUploadList={{
												showRemoveIcon: true,
												showPreviewIcon: false,
											}}>
											{this.state.fileList && this.state.fileList.length < 1 && (
												<Button>
													<Icon type='upload' /> Click to Upload
												</Button>
											)}
										</Upload>
									)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditCompany));
