import {
	<PERSON><PERSON>,
	Col,
	Dropdown,
	Icon,
	Input,
	Menu,
	message,
	Modal,
	Row,
	Spin,
	Table,
	Tooltip,
	Form,
} from "antd";
import React from "react";
import ModalImage from "react-modal-image";
import NumberFormat from "react-number-format";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import user from "../../static/images/user1.png";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';
import queryString from 'query-string';



const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let companyId;


export default class companyManagement extends React.Component {
	state = {
		apiParam: {},
		companyData: null,
		companyList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		isMainAdmin: false,
		superAdminAccessToken: "",
		consumerLoginAccessToken: "",
		integrationKey: "",
		isMenusHide: false
	};

	componentDidMount() {
		const queryParams = queryString.parse(window.location.search);
		let params = {
			search: "",
			orderBy: "created_at",
			pageNo: parseInt(queryParams.page) || "1",
			orderSequence: "DESC",
			pageSize: 25,
		};

		const userType = localStorage.getItem("userType") === "1" ? true : false;
		this.setState({ apiParam: params, isMainAdmin: userType }, () => this.fetchCompanyList());


		const superAdminLoginJson = JSON.stringify({
			email: "<EMAIL>",
			password: "x{9_Ff]S5g",
			deviceToken: "abcd",
			deviceType: 0,
		});

		axios.post(`${MOVER_STORAGE_API_URL}login`, superAdminLoginJson,
			{
				headers: {
					'Content-Type': 'application/json'
				}
			})
			.then((response) => {
				this.setState({
					loading: false, superAdminAccessToken: response.data.data.accessToken
				});
			})
			.catch((error) => {
				console.log(error);
				this.setState({ loading: false });
			});
	}


	EnableMoverInventoryIntegration = (companyId) => {
		this.setState({ loading: true, pageloading: true, isMenusHide: true });
		const data = [];
		data["data"] = { company_id: companyId };
		API.post("api/integration/createIntegrationToken", data)
			.then((response) => {
				this.setState({ loading: false, contentloader: false, pageloading: false, isMenusHide: false });
				message.success(response.message);
				this.fetchCompanyList();
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({ loading: false, contentloader: false, pageloading: false, isMenusHide: false });
			});
	}



	fetchCompanyRes = (response, accessToken) => {
		this.setState({ loading: true, pageloading: true, contentloader: true });
		const data = JSON.stringify({
			companyIdTokenMoverInventory: response.data.integration_key
		});
		axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/companies`, data,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': accessToken
				}
			})
			.then((fetchCompanyRes) => {
				let params = {
					staff_email: fetchCompanyRes.data && fetchCompanyRes.data.data && fetchCompanyRes.data.data.companyAdmin.email,
					staffIdStorage: fetchCompanyRes.data && fetchCompanyRes.data.data && fetchCompanyRes.data.data.companyAdmin.id,
					warehouseId: fetchCompanyRes.data && fetchCompanyRes.data.data && fetchCompanyRes.data.data.warehouses[0].id,
				};
				const data = [];
				data["data"] = params;
				API.post("api/admin/staff/storageId/updateByEmail", data)
					.then((storageIdUpdate) => {
						if (storageIdUpdate.status === 1) {
							this.setState({ loading: false, pageloading: false, contentloader: false, isMenusHide: false });
							message.success("Successfully integrated with MoverStorage");
							// fetchCompanyList function
							this.fetchCompanyList();
						} else {
							this.setState({ loading: false, pageloading: false, contentloader: false, isMenusHide: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false, pageloading: false, contentloader: false, isMenusHide: false });
						message.error(error.message);
					});
			})
			.catch((error) => {
				console.log("error", error);
				this.setState({ loading: false, pageloading: false, contentloader: false, isMenusHide: false });

			});
	}

	changeIntegrationKeyStatusResponse = (response, accessToken) => {
		this.setState({ loading: true, pageloading: true, contentloader: true });
		const companyId = response.data && response.data.key_company && response.data.key_company.storage_company_id

		const data = JSON.stringify({
			isIntegratedWithMoverInventory: response.data.status == "active" ? true : false
		});

		axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/companies/${companyId}`, data,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': this.state.consumerLoginAccessToken
				}
			})
			.then((companyResponse) => {
				// fetchCompanyRes function
				this.fetchCompanyRes(response, accessToken)
			})
			.catch((error) => {
				console.log("error", error);
				this.setState({ loading: false, pageloading: false, contentloader: false, isMenusHide: false });

			});
	}

	changeIntegrationKeyStatusResponseForStatusKey = (response, accessToken) => {
		this.setState({ loading: true, contentloader: true });
		const companyId = response.data && response.data.key_company && response.data.key_company.storage_company_id

		const data = JSON.stringify({
			isIntegratedWithMoverInventory: response.data.status == "active" ? true : false
		});

		axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/companies/${companyId}`, data,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': accessToken
				}
			})
			.then((companyResponse) => {
				this.setState({ loading: false, pageloading: false, contentloader: false, isMenusHide: false });
				message.success("Integration key status updated successfully.");
				// fetchCompanyList function
				this.fetchCompanyList();
			})
			.catch((error) => {
				console.log("error", error);
				this.setState({ loading: false, pageloading: false, contentloader: false, isMenusHide: false });

			});
	}



	fetchConsumerLoginAccessTokenForKey = (response) => {
		this.setState({ contentloader: true, loading: true, pageloading: true });

		const consumerLoginJson = JSON.stringify({
			companyIdTokenMoverInventory: this.state.integrationKey,
			email: "<EMAIL>",
			password: "5PLaRAqq",
			deviceToken: "abcd",
			deviceType: 0,
		});

		axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson,
			{
				headers: { 'Content-Type': 'application/json' }
			})
			.then((consumerLoginResponse) => {
				this.setState({ consumerLoginAccessToken: consumerLoginResponse.data.data.accessToken });
				//changeIntegrationKeyStatusResponse function
				this.changeIntegrationKeyStatusResponse(response, consumerLoginResponse.data.data.accessToken)
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({ contentloader: false, loading: false, pageloading: false, isMenusHide: false });
			});
	}

	fetchConsumerLoginAccessTokenForKeyStatusChange = (response) => {
		this.setState({ contentloader: true, loading: true });
		const consumerLoginJson = JSON.stringify({
			companyIdTokenMoverInventory: response.data.integration_key,
			email: "<EMAIL>",
			password: "5PLaRAqq",
			deviceToken: "abcd",
			deviceType: 0,
		});
		axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson,
			{
				headers: { 'Content-Type': 'application/json' }
			})
			.then((consumerLoginResponse) => {
				this.setState({ consumerLoginAccessToken: consumerLoginResponse.data.data.accessToken });
				//changeIntegrationKeyStatusResponse function
				this.changeIntegrationKeyStatusResponseForStatusKey(response, consumerLoginResponse.data.data.accessToken)
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({ contentloader: false, loading: false, pageloading: false, isMenusHide: false });
			});
	}

	menus = (text) => {
		return (
			<Menu>
				<Menu.Item key='1'>
					<Tooltip title='Edit'>
						<Button
							type='primary'
							className='c-btn c-round c-warning'
							icon='edit'
							onClick={() => this.edit(text.company_id)}></Button>
					</Tooltip>
				</Menu.Item>



				{((text.api_key === "" || text.api_key === undefined || text.api_key === null) && (text.status == "active")) ?
					<div style={{ marginLeft: "12px" }} >
						{
							this.state.isMainAdmin && (
								<Menu.Item key='2'>
									<Tooltip title='Create Api Key'>
										<Button
											type='primary'
											className='c-btn c-round c-success'
											icon='plus'
											onClick={() => this.createCompanyKey(text)}></Button>
									</Tooltip>
								</Menu.Item>
							)
						}
					</div>

					: ""}

				{this.state.isMainAdmin && (text.integration_key == "" || text.integration_key === undefined || text.integration_key === null) && (
					<Menu.Item key='3'>
						<Tooltip title='Delete'>
							<Button
								type='primary'
								className='c-btn c-round c-danger'
								icon='delete'
								onClick={() => this.delete(text.company_id)}></Button>
						</Tooltip>
					</Menu.Item>
				)}


				<Menu.Item key='4'>
					<Tooltip title='Password Update'>
						<Button
							type='primary'
							className='c-btn c-round c-success'
							icon='edit'
							onClick={() => this.editPassword(text.company_id)}></Button>
					</Tooltip>
				</Menu.Item>

				{((text.integration_key === "" || text.integration_key === undefined || text.integration_key === null) && (text.status == "active")) ?
					<div style={{ marginLeft: "12px" }} >
						{
							this.state.isMainAdmin && (
								<Menu.Item key='5'>
									<Tooltip title='create Integration Token For Storage'>
										<Button
											type='primary'
											className='c-btn c-round c-info'
											onClick={() => this.EnableMoverInventoryIntegration(text.company_id)}>
											<img
												width="16px"
												height="10px"
												src="data:image/png;base64,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"
											/>
										</Button>
									</Tooltip>
								</Menu.Item>
							)
						}
					</div>
					: ""}


			</Menu>
		);
	};
	fetchCompanyList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		this.setState({ loading: true });
		API.post("api/admin/company/list", data)
			.then((response) => {
				console.log("res", response)
				const pagination = { ...this.state.pagination };
				if (response) {
					if (response.status === 1) {
						pagination.total = response.data.companyList.count;
						this.setState({
							companyList: response.data.companyList.rows,
							pagination,
							loading: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							pagination,
							loading: false,
						});
					}
				}
			})
			.catch((error) => {
				//console.log("error:", error);
				message.error(error.message);
				this.setState({ pageloading: false });
			});
	};
	handleChange = (pagination, filters, sorter) => {
		let sortData = Array.isArray(sorter.field) ? sorter.field[0] : sorter.field
		scrollTo();
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		const queryParams = queryString.parse(window.location.search);
		queryParams.page = pagination.current;
		const newUrl = `${window.location.pathname}?${queryString.stringify(queryParams)}`;
		window.history.pushState(null, null, newUrl);
		let params = {
			...this.state.apiParam,
			pageNo: pagination.current,
			orderBy: sorter.field === "createdAt" ? "created_at" : sortData,
			orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
			pageSize: pagination.pageSize,
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchCompanyList();
			}
		);
	};
	addCompany() {
		this.props.history.push(`${this.props.match.url}/add`);
	}
	createCompanyKey(text) {
		const data = [];
		let params = {
			company_id: text.company_id,
			company_identity: text.company_identity
		}
		data["data"] = params;
		this.setState({ loading: true });
		API.post("api/open-api/apiKey/add", data)
			.then((response) => {
				if (response.status === 1) {
					this.fetchCompanyList();
					this.setState({ loading: false });
					message.success(response.message);
				} else {
					this.setState({ loading: false });
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({ loading: false });
			});

	}
	viewCompanyKey(id) {
		this.props.history.push(`${this.props.match.url}/viewCompanyKey/${id}`);
	}
	delete(id) {
		this.setState({ deleteModal: true });
		companyId = id;
	}
	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}

	editPassword(id) {
		this.props.history.push(`${this.props.match.url}/edit-password/${id}`);
	}


	handleCancel = () => {
		this.setState({ deleteModal: false, confirmLoading: false });
	};
	handleOk = () => {
		const id = companyId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { company_id: id };
		API.post("api/admin/company/delete", deleteData)
			.then((response) => {
				if (response) {
					this.fetchCompanyList();
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				//console.log("error:", error);
				message.error(error.message);
				this.setState({ confirmLoading: false });
			});
	};

	handleApiStatusChange = (record) => {
		this.setState({ confirmLoading: true });
		const data = [];
		data["data"] = {
			company_id: record.company_id,
		};

		API.post("api/open-api/apiKey/change-key-status", data)
			.then((response) => {
				if (response) {
					this.fetchCompanyList({
						page: this.state.pagination.current ? this.state.pagination.current : 1,
					});
					this.setState({ confirmLoading: false });

				} else {
					this.setState({ confirmLoading: false });
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({ confirmLoading: false });
				message.error(error.message);
				//console.log("error:", error);
			});
	};

	integrationKeyStatusChange = (record) => {
		this.setState({ loading: true, confirmLoading: true });
		const data = [];
		data["data"] = {
			company_id: record.company_id,
		};
		API.post("api/admin/change-integration_key_status", data)
			.then((response) => {
				// fetchConsumerLoginAccessTokenForKey function
				this.fetchConsumerLoginAccessTokenForKeyStatusChange(response)

			})
			.catch((error) => {
				this.setState({ loading: false, pageloading: false, confirmLoading: false, isMenusHide: false });
				message.error(error.message);
			});
	};




	handleStatusChange = (company_id) => {
		const statusData = [];
		statusData["data"] = { company_id: company_id };
		API.post("api/admin/company/change-company-status", statusData)
			.then((response) => {
				if (response) {
					this.fetchCompanyList({
						page: this.state.pagination.current ? this.state.pagination.current : 1,
					});
				} else {
					message.error(response.message);
				}
			})
			.catch((error) => {
				message.error(error.message);
				//console.log("error:", error);
			});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchCompanyList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					pageNo: 1,
				},
				search: e,
			},
			this.callback
		);
	};
	render() {
		const { isMainAdmin } = this.state;
		const columns = [
			{
				title: "Name",
				dataIndex: ["company_name", "photo"],
				key: "company_name",
				// minWidth: "15%",
				sorter: true,
				align: "left",
				render: (record, text) => {
					return (
						<div style={{ display: "flex", alignItems: "center" }}>
							<div
								style={{
									width: "50px",
									height: "50px",
									marginTop: "10px"
								}}>
								<ModalImage
									className='imageModal'
									small={
										text.company_logo && text.company_logo.trim() !== "" ? `${text.company_logo}` : user
									}
									large={
										text.company_logo && text.company_logo.trim() !== "" ? `${text.company_logo}` : user
									}
									hideDownload={true}
									hideZoom={true}
								/>
							</div>
							<div
								style={{
									flex: "1",
								}}>
								{text.company_name}
							</div>
						</div>
					);
				},
			},
			{
				title: "Company ID",
				dataIndex: "company_identity",
				key: "company_identity",
				// minWidth: "15%",
				sorter: true,
				align: "center",
			},
			{
				title: "Email",
				dataIndex: "email",
				key: "email",
				// minWidth: "12%",
				sorter: true,
				align: "left",
			},
			{
				title: "Api Key",
				dataIndex: "api_key",
				key: "api_key",
				// minWidth: "12%",
				align: "center",
			},
			{
				title: "Api Status",
				dataIndex: "api_status",
				key: "api_status",
				// width: "20%",
				align: "center",
				render: (text, record) => {
					return (
						<div>
							{(record.api_key !== undefined && record.api_key !== null && record.api_key !== "") ?
								<div style={{ width: "100px" }} >
									{isMainAdmin ? (
										<button
											disabled={!isMainAdmin}
											className={record.api_status === 1 ? "statusButtonActive" : "statusButtoninactive"}
											onClick={() => this.handleApiStatusChange(record)}>
											<Icon type='swap' />
											{record.api_status === 1 ? " Active" : " Inactive"}
										</button>
									) : (
										""
									)}
								</div>
								:
								""}
						</div>
					);
				},
			},


			{
				title: "Integration Key",
				dataIndex: "integration_key",
				key: "integration_key",
				// minWidth: "12%",
				align: "center",
			},

			{
				title: "Integration Key Status",
				dataIndex: "integration_key_status",
				key: "integration_key_status",
				// width: "20%",
				align: "center",
				render: (text, record) => {
					return (
						<>
							{(record.integration_key_status !== undefined && record.integration_key_status !== null && record.integration_key_status !== "") ?

								<div style={{ width: "100px" }} >
									{isMainAdmin ? (
										<button
											style={{ cursor: "not-allowed" }}
											disabled={!isMainAdmin}
											className={record.integration_key_status === "active" ? "statusButtonActive" : "statusButtoninactive"}
										// onClick={() => this.integrationKeyStatusChange(record)}
										>
											<Icon type='swap' />
											{record.integration_key_status === "active" ? " Active" : " Inactive"}
										</button>
									) : (
										""
									)}

								</div>
								: ""}
						</>
					);
				},
			},

			{
				title: "Phone",
				dataIndex: "phone",
				minWidth: "15%",
				key: "phone",
				align: "left",
				render: (record, text) => {
					return record === "undefined" || record === "" ? "" : record
				},
			},

			{
				title: "Group Name",
				dataIndex: "group_name",
				key: "group_name",
				align: "center",
			},

			{
				title: "Address",
				// dataIndex: "address1",
				// minWidth: "15%",
				key: "address1",
				align: "left",
				render: (text, record) => {
					return (
						<div>
							<div>
								<p style={{ margin: "0" }}>
									{record && record.address1 && record.address1 !== "undefined" && record.address1 !== "null"
										? record.address1
										: ""}
									&nbsp;{" "}
									{record && record.address2 && record.address2 !== "undefined" && record.address2 !== "null"
										? record.address2
										: ""}
								</p>
							</div>
							<div>
								<p style={{ margin: "0" }}>
									{record && record.city && record.city !== "undefined" && record.city !== "null"
										? record.city
										: ""}
									&nbsp;{" "}
									{record && record.state && record.state !== "undefined" && record.state !== "null"
										? record.state
										: ""}
									{record && record.zipCode && record.zipCode !== "undefined" && record.zipCode !== "null"
										? ", " + record.zipCode
										: ""}
								</p>
							</div>
							<div>
								<p style={{ margin: "0" }}>
									{record && record.country && record.country !== "undefined" && record.country !== "null"
										? record.country
										: ""}
								</p>
							</div>
						</div>
					);
				},
			},
			{
				title: "Status",
				dataIndex: "status",
				key: "status",
				// width: "20%",
				align: "center",
				render: (text, record) => {
					return (
						<div style={{ width: "100px" }} >
							{isMainAdmin ? (
								<button
									disabled={!isMainAdmin}
									className={record.status === "active" ? "statusButtonActive" : "statusButtoninactive"}
									onClick={() => this.handleStatusChange(record.company_id)}>
									<Icon type='swap' />
									{record.status === "active" ? " Active" : " Inactive"}
								</button>
							) : (
								""
							)}
						</div>
					);
				},
			},

			{
				title: "Action",
				key: "action",
				// minWidth: "12.5%",
				align: "center",
				render: (record, text) => {
					return (
						<div className='icons' style={{ textAlign: "center" }}>
							{this.state.isMenusHide ?
								"" :
								<Dropdown placement='bottomCenter' overlay={this.menus.bind(null, text)}>
									<Icon
										type='setting'
										theme='twoTone'
										twoToneColor='#3fa146'
										onClick={() => this.menus.bind(null, text)}
									/>
								</Dropdown>
							}
						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' id="scroller" style={{ height: "100%" }}>
					<Row>
						<Col sm={8}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' /> &emsp;Company Management
							</h2>
						</Col>
						<Col sm={6}>
							{this.state.search ? (
								<p
									style={{
										margin: "5px",
										textAlign: "center",
										color: "#303030",
									}}>
									Matching Records: {this.state.pagination.total}
								</p>
							) : (
								""
							)}
						</Col>
						<Col sm={10}>
							<Row>
								<Col sm={16} style={{ textAlign: "right" }}>
									<Search
										placeholder='Search Company'
										onChange={(e) => this.handleSearch(e.target.value)}
										value={this.state.search}
										style={{ width: 200 }}
									/>
								</Col>
								{isMainAdmin && (
									<Col sm={8}>
										<Button
											className='addButton'
											style={{ marginTop: "0" }}
											type='primary'
											onClick={() => this.addCompany()}>
											+ Add Company
										</Button>
									</Col>
								)}
							</Row>
						</Col>
					</Row>
				</div>
				<Spin
					spinning={this.state.pageloading}
					indicator={antIcon}
					tip={
						<div>
							<p style={{ margin: "0" }}>
							</p>
						</div>
					}
				>
					<Spin
						spinning={this.state.loading}
						indicator={antIcon}
					>
						<LayoutContent>
							<div style={{ marginTop: "-1.5rem", overflowX: "auto" }}>
								<Table
									bordered={true}
									columns={columns}
									pagination={{
										// pageSize: 10,
										total: this.state.pagination.total,
										showSizeChanger: true,
										defaultPageSize: 25,
										pageSizeOptions: ["25", "50"],
										current: this.state.apiParam.pageNo
									}}
									dataSource={this.state.companyList}
									onChange={this.handleChange}
								/>
							</div>
						</LayoutContent>
					</Spin>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to delete Company?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}
