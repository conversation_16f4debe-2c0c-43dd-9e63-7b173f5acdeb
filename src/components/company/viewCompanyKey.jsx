import {
	But<PERSON>,
	Col,
	Dropdown,
	Icon,
	Input,
	Menu,
	message,
	Modal,
	Row,
	Spin,
	Table,
	Tooltip,
} from "antd";
import React from "react";
import ModalImage from "react-modal-image";
import NumberFormat from "react-number-format";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import user from "../../static/images/user1.png";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";

const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let companyId;

export default class companyManagement extends React.Component {
	state = {
		apiParam: {},
		companyList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		isMainAdmin: false,
	};
	componentDidMount() {
		let params = {
			search: "",
			orderBy: "created_at",
			pageNo: "1",
			orderSequence: "DESC",
			pageSize: 25,
		};
		const userType = localStorage.getItem("userType") === "1" ? true : false;
		this.setState({ apiParam: params, isMainAdmin: userType }, () => this.fetchCompanyList());
	}
	menus = (text) => {
		return (
			<Menu>
				{this.state.isMainAdmin && (
					<Menu.Item key='4'>
						<Tooltip title='Delete'>
							<Button
								type='primary'
								className='c-btn c-round c-danger'
								icon='delete'
								onClick={() => this.delete(text.company_id)}></Button>
						</Tooltip>
					</Menu.Item>
				)}
			</Menu>
		);
	};
	fetchCompanyList = () => {
		const data = [];
		let params = {
			company_id: this.props.match.params.id,
		}
		data["data"] = params;
		this.setState({ pageloading: true });
		API.post("api/open-api/apiKey/list", data)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							companyList: response.data.rows,
							pageloading: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							pageloading: false,
						});
					}
				}
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({ pageloading: false });
			});
	};
	handleChange = (pagination, filters, sorter) => {
		
		let sortData = Array.isArray(sorter.field) ? sorter.field[0] : sorter.field

		scrollTo();
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		let params = {
			...this.state.apiParam,
			pageNo: pagination.current,
			orderBy: sorter.field === "createdAt" ? "created_at" : sortData,
			orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
			pageSize: pagination.pageSize,
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchCompanyList();
			}
		);
	};
	addCompany() {
		this.props.history.push(`${this.props.match.url}/add`);
	}
	createCompanyKey(id) {
		this.props.history.push(`${this.props.match.url}/addCompanyKey/${id}`);
	}
	delete(id) {
		this.setState({ deleteModal: true });
		companyId = id;
	}
	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}

	handleCancel = () => {
		this.setState({ deleteModal: false, confirmLoading: false });
	};
	handleOk = () => {
		const id = companyId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { company_id: id };
		API.post("api/open-api/apiKey/delete", deleteData)
			.then((response) => {
				if (response) {
					this.fetchCompanyList();
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({ confirmLoading: false });
			});
	};
	handleStatusChange = (record) => {
		const statusData = [];
		statusData["data"] = {
			company_id: record.company_id,
			isEnable: record.isEnable === 0 ? 1 : 0
		};
		API.post("api/open-api/apiKey/change-key-status", statusData)
			.then((response) => {
				if (response) {
					this.fetchCompanyList();
				} else {
					message.error(response.message);
				}
			})
			.catch((error) => {
				message.error(error.message);
			});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchCompanyList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					pageNo: 1,
				},
				search: e,
			},
			this.callback
		);
	};
	render() {
		const { isMainAdmin } = this.state;
		const columns = [
			{
				title: "Company ID",
				dataIndex: "company_identity",
				key: "company_identity",
				minWidth: "15%",
				sorter: true,
				align: "center",
			},

			{
				title: "Api Key",
				dataIndex: "company_key",
				key: "company_key",
				minWidth: "15%",
				sorter: true,
				align: "center",
			},

			{
				title: "Status",
				dataIndex: "status",
				width: "10%",
				key: "status",
				align: "center",
				render: (text, record) => {
					return (
						<div>
							{isMainAdmin ? (
								<button
									disabled={!isMainAdmin}
									className={record.isEnable === 1 ? "statusButtonActive" : "statusButtoninactive"}
									onClick={() => this.handleStatusChange(record)}>
									<Icon type='swap' />
									{record.isEnable === 1 ? " Active" : " Inactive"}
								</button>
							) : (
								""
							)}
						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				minWidth: "12.5%",
				align: "center",
				render: (record, text) => {
					return (
						<div className='icons' style={{ textAlign: "center" }}>
							<Dropdown placement='bottomCenter' overlay={this.menus.bind(null, text)}>
								<Icon
									type='setting'
									theme='twoTone'
									twoToneColor='#3fa146'
									onClick={() => this.menus.bind(null, text)}
								/>
							</Dropdown>
						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<Col sm={8}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' /> &emsp;Company Key Management
							</h2>
						</Col>
						<Col sm={6}>
							{this.state.search ? (
								<p
									style={{
										margin: "5px",
										textAlign: "center",
										color: "#303030",
									}}>
									Matching Records: {this.state.pagination.total}
								</p>
							) : (
								""
							)}
						</Col>
					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div style={{ marginTop: "-1.5rem", overflowX: "auto" }}>
							<Table
								bordered={true}
								columns={columns}
								pagination={{
									// pageSize: 10,
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 25,
									pageSizeOptions: ["25", "50"],
								}}
								dataSource={this.state.companyList}
								onChange={this.handleChange}
							/>
						</div>
					</LayoutContent>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to delete Company?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}
