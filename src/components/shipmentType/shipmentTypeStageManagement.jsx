import React from "react";
import {
	Col,
	Icon,
	Input,
	Modal,
	Row,
	Spin,
	Table,
	Tooltip,
	message,
	Button,
} from "antd";

import "../../static/css/userManagement.css";
import "../../static/css/common.css";

import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import Api from "../../api/api-handler";
import DateFns from "date-fns";
import appActions from "../../redux/app/actions";
import { connect } from "react-redux";

const { changeCurrent } = appActions;
const Search = Input.Search;
const antIcon = <Icon type="loading" style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let shipmentTypeStageId;

class shipmentTypeStageManagement extends React.Component {
	state = {
		apiParam: {},
		shipmentTypeStageList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		shipment_type_id: "",
		adminId: "",
		companyId: "",
		staffId: "",
		objAdmin: "",
		objStaff: "",
		objCompany: "",
		integrationKey: "",
		integrationKeyStatus: false,
		staffId: "",
		editShipmentTypeCheckbox: true

	};
	componentDidMount() {
		const id = this.props.match.params.id;
		this.setState({ shipment_type_id: id });
		this.props.changeCurrent("shipment-type");
		let params = {
			shipment_type_id: id,
			search: "",
			orderBy: "created_at",
			pageNo: "1",
			orderSequence: "DESC",
			pageSize: 25,
		};
		this.setState({ apiParam: params }, () =>
			this.fetchShipmentTypeStageList()
		);

		this.setState({
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
			editShipmentTypeCheckbox: localStorage.getItem("editShipmentTypeFlagCheck") == 1 ? true : false,

		});

		const objData = this.props.location.state


		this.setState({
			objAdmin: objData.objAdmin,
			objCompany: objData.objCompany,
			objStaff: objData.objStaff,

		})


		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							integrationKeyStatus: true,
							integrationKey: response.data.integration_key
						});
					} else {
						this.setState({
							integrationKeyStatus: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});


		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});


	}
	fetchShipmentTypeStageList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		this.setState({ pageloading: true });
		API.post("api/admin/shipment-type/view-shipment-stage-list", data).then(
			(response) => {
				const pagination = { ...this.state.pagination };
				if (response) {
					if (response.status === 1) {
						pagination.total = response.data.shipmentTypeStageList.count;
						this.setState({
							shipmentTypeStageList: response.data.shipmentTypeStageList.rows,
							pagination,
							pageloading: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							pagination,
							pageloading: false,
						});
						// message.error(res.message)
					}
				}
			}
		);
	};
	handleChange = (pagination, filters, sorter) => {
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		let params = {
			...this.state.apiParam,
			pageNo: pagination.current,
			orderBy: sorter.field === "createdAt" ? "created_at" : sorter.field,
			orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
			pageSize: pagination.pageSize,
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search
					? this.handleSearch(null, this.state.search)
					: this.fetchShipmentTypeStageList();
			}
		);
	};
	addShipmentStageType() {
		this.props.history.push(`${this.props.match.url}/add`);
	}
	delete(id) {
		this.setState({ deleteModal: true });
		shipmentTypeStageId = id;
	}

	edit(id) {
		this.props.history.push(
			`${this.props.match.url.slice(
				0,
				this.props.match.url.lastIndexOf("/")
			)}/edit/${id}`
		);
	}

	handleCancel = () => {
		this.setState({ deleteModal: false, confirmLoading: false });
	};
	handleOk = () => {
		const id = shipmentTypeStageId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { shipment_stage_id: id };
		API.post("api/admin/shipment-type/delete", deleteData).then((response) => {
			if (response) {
				this.fetchShipmentTypeStageList();
				this.setState({
					deleteModal: false,
					confirmLoading: false,
				});
				message.success(response.message);
			} else {
				this.setState({
					deleteModal: false,
					confirmLoading: false,
				});
				message.error(response.message);
			}
		});
	};
	handleStatusChange = (shipment_stage_id) => {
		const statusData = [];
		statusData["data"] = { shipment_stage_id: shipment_stage_id };
		API.post(
			"api/admin/shipment-type/change-shipment-stage-status",
			statusData
		).then((response) => {
			if (response) {
				this.fetchShipmentTypeStageList({
					page: this.state.pagination.current
						? this.state.pagination.current
						: 1,
				});
			} else {
				message.error(response.message);
			}
		});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchShipmentTypeStageList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e.target.value,
				},
				search: e.target.value,
			},
			this.callback
		);
	};
	render() {
		const columns = [
			{
				title: "Name",
				dataIndex: "name",
				key: "name",
				sorter: true,
				align: "left"
			},
			{
				title: "Order of stage",
				dataIndex: "order_of_stages",
				key: "order_of_stages",
				sorter: true,
			},
			{
				title: "Add Items To Inventory",
				dataIndex: "add_items_to_inventory",
				key: "add_items_to_inventory",
				sorter: true,
				render: (text, record) => {
					return <div>{record.add_items_to_inventory === "1" || record.add_items_to_inventory === 1 || record.add_items_to_inventory === true || record.add_items_to_inventory === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Additional Scan",
				dataIndex: "scan_require",
				key: "scan_require",
				sorter: true,
				render: (text, record) => {
					return <div>{record.scan_require === "1" || record.scan_require === 1 || record.scan_require === true || record.scan_require === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Add Items To Storage",
				dataIndex: "assign_storage_units_to_items",
				key: "assign_storage_units_to_items",
				sorter: true,
				render: (text, record) => {
					return <div>{record.assign_storage_units_to_items === "1" || record.assign_storage_units_to_items === 1 || record.assign_storage_units_to_items === true || record.assign_storage_units_to_items === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Remove Items From Storage",
				dataIndex: "unassign_storage_units_from_items",
				key: "unassign_storage_units_from_items",
				sorter: true,
				render: (text, record) => {
					return <div>{record.unassign_storage_units_from_items === "1" || record.unassign_storage_units_from_items === 1 || record.unassign_storage_units_from_items === true || record.unassign_storage_units_from_items === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Remove Items From Inventory",
				dataIndex: "remove_items_to_inventory",
				key: "remove_items_to_inventory",
				sorter: true,
				render: (text, record) => {
					return <div>{record.remove_items_to_inventory === "1" || record.remove_items_to_inventory === 1 || record.remove_items_to_inventory === true || record.remove_items_to_inventory === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Additional Scan",
				dataIndex: "remove_scan_require",
				key: "remove_scan_require",
				sorter: true,
				render: (text, record) => {
					return <div>{record.remove_scan_require === "1" || record.remove_scan_require === 1 || record.remove_scan_require === true || record.remove_scan_require === "yes" ? "Yes" : "No"}</div>;
				},
			},

			{
				title: "Enable Partial Complete Stage",
				dataIndex: "enable_partial_complete_stage",
				key: "enable_partial_complete_stage",
				sorter: true,
				render: (text, record) => {
					return <div>{record.enable_partial_complete_stage === "1" || record.enable_partial_complete_stage === 1 || record.enable_partial_complete_stage === true || record.enable_partial_complete_stage === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Carrier Signature Required",
				dataIndex: "supervisor_signature_require",
				key: "supervisor_signature_require",
				sorter: true,
				render: (text, record) => {
					return <div>{text === "1" || text === 1 ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Customer Signature Required",
				dataIndex: "customer_signature_require",
				key: "customer_signature_require",
				sorter: true,
				render: (text, record) => {
					return <div>{text === "1" || text === 1 ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Created Date",
				dataIndex: "created_at",
				key: "created_at",
				align: "center",
				sorter: true,
				render: (text, record) => {
					return <div>{DateFns.format(record.created_at, "MM/DD/YYYY")}</div>;
				},
			},
			{
				title: "Status",
				dataIndex: "status",
				key: "status",
				align: "center",
				render: (text, record) => {
					return (
						<div>
							<button
								className={
									record.status === "active"
										? "statusButtonActive"
										: "statusButtoninactive"
								}
								onClick={() =>
									this.handleStatusChange(record.shipment_stage_id)
								}
							>
								<Icon type="swap" />
								{record.status === "active" ? " Active" : " Inactive"}
							</button>

						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				align: "center",
				render: (record, text) => {
					return (
						<div className="icons">
							{this.state.editShipmentTypeCheckbox == false && this.state.staffId !== null ? (
								"-"
							) : (
								<Tooltip title="Edit">
									<Button
										type="primary"
										className="c-btn c-round c-warning"
										icon="edit"
										onClick={() => this.edit(text.shipment_stage_id)}
									></Button>
								</Tooltip>
							)}

						</div>
					);
				},
			},
		];

		const columns2 = [
			{
				title: "Name",
				dataIndex: "name",
				key: "name",
				sorter: true,
			},
			{
				title: "Order of stage",
				dataIndex: "order_of_stages",
				key: "order_of_stages",
				sorter: true,
			},
			{
				title: "Add Items To Inventory",
				dataIndex: "add_items_to_inventory",
				key: "add_items_to_inventory",
				sorter: true,
				render: (text, record) => {
					return <div>{record.add_items_to_inventory === "1" || record.add_items_to_inventory === 1 || record.add_items_to_inventory === true || record.add_items_to_inventory === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Additional Scan",
				dataIndex: "scan_require",
				key: "scan_require",
				sorter: true,
				render: (text, record) => {
					return <div>{record.scan_require === "1" || record.scan_require === 1 || record.scan_require === true || record.scan_require === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Remove Items From Inventory",
				dataIndex: "remove_items_to_inventory",
				key: "remove_items_to_inventory",
				sorter: true,
				render: (text, record) => {
					return <div>{record.remove_items_to_inventory === "1" || record.remove_items_to_inventory === 1 || record.remove_items_to_inventory === true || record.remove_items_to_inventory === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Additional Scan",
				dataIndex: "remove_scan_require",
				key: "remove_scan_require",
				sorter: true,
				render: (text, record) => {
					return <div>{record.remove_scan_require === "1" || record.remove_scan_require === 1 || record.remove_scan_require === true || record.remove_scan_require === "yes" ? "Yes" : "No"}</div>;
				},
			},

			{
				title: "Enable Partial Complete Stage",
				dataIndex: "enable_partial_complete_stage",
				key: "enable_partial_complete_stage",
				sorter: true,
				render: (text, record) => {
					return <div>{record.enable_partial_complete_stage === "1" || record.enable_partial_complete_stage === 1 || record.enable_partial_complete_stage === true || record.enable_partial_complete_stage === "yes" ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Carrier Signature Required",
				dataIndex: "supervisor_signature_require",
				key: "supervisor_signature_require",
				sorter: true,
				render: (text, record) => {
					return <div>{text === "1" || text === 1 ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Customer Signature Required",
				dataIndex: "customer_signature_require",
				key: "customer_signature_require",
				sorter: true,
				render: (text, record) => {
					return <div>{text === "1" || text === 1 ? "Yes" : "No"}</div>;
				},
			},
			{
				title: "Created Date",
				dataIndex: "created_at",
				key: "created_at",
				sorter: true,
				align: "center",
				render: (text, record) => {
					return <div>{DateFns.format(record.created_at, "MM/DD/YYYY")}</div>;
				},
			},
			{
				title: "Status",
				dataIndex: "status",
				key: "status",
				align: "center",
				render: (text, record) => {
					return (
						<div>
							<button
								className={
									record.status === "active"
										? "statusButtonActive"
										: "statusButtoninactive"
								}
								onClick={() =>
									this.handleStatusChange(record.shipment_stage_id)
								}
							>
								<Icon type="swap" />
								{record.status === "active" ? " Active" : " Inactive"}
							</button>

						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				align: "center",
				render: (record, text) => {
					return (
						<div className="icons">
							{this.state.editShipmentTypeCheckbox == false && this.state.staffId !== null ? (
								"-"
							) : (
								<Tooltip title="Edit">
									<Button
										type="primary"
										className="c-btn c-round c-warning"
										icon="edit"
										onClick={() => this.edit(text.shipment_stage_id)}
									></Button>
								</Tooltip>
							)}
						</div>
					);
				},
			},
		];

		return (
			<LayoutContentWrapper>
				<div className="top_header" style={{ height: "100%" }}>
					<Row>
						<Col sm={12}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type="user" /> &emsp;Shipment Type Stages Management
							</h2>
						</Col>
						<Col sm={12}>
							<Row>
								<Col sm={16} style={{ textAlign: "right" }}>
									<Search
										placeholder="Search shipment type stages"
										onChange={this.handleSearch}
										value={this.state.search}
										style={{ width: 200 }}
									/>
								</Col>
								<Col sm={8} style={{ textAlign: "right" }}>
									<button
										className="backButton"
										onClick={() => this.props.history.goBack()}
									>
										<i className="fa fa-chevron-left" aria-hidden="true" /> Back
									</button>
								</Col>
							</Row>
						</Col>
					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div style={{ marginTop: "-1.5rem", overflowX: "auto" }}>
							<Table
								bordered={true}
								columns={this.state.integrationKeyStatus ? columns : columns2}
								pagination={{
									// pageSize: 10,
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 25,
									pageSizeOptions: ["25", "50"],
								}}
								dataSource={this.state.shipmentTypeStageList}
								onChange={this.handleChange}
								scroll={{ x: 2000 }}
							/>
						</div>
					</LayoutContent>
				</Spin>
				{
					<Modal
						title="Are You Sure?"
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText="Yes"
						cancelText="No"
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}
					>
						<p>Are you sure you want to delete shipment type stage?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}

export default connect(null, { changeCurrent })(shipmentTypeStageManagement);
