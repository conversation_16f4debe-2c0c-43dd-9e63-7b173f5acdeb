import { Button, Col, Icon, Input, message, Modal, Row, Spin, Table, Tooltip } from "antd";
import React from "react";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import queryString from 'query-string';

const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let shipmentTypeId;

class ShipmentTypeManagement extends React.Component {
	state = {
		apiParam: {},
		shipmentTypeList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		adminId: "",
		companyId: "",
		staffId: "",
		editShipmentTypeCheckbox: true

	};
	componentDidMount() {
		const queryParams = queryString.parse(window.location.search);
		let params = {
			search: "",
			orderBy: "created_at",
			pageNo: parseInt(queryParams.page) || "1",
			orderSequence: "DESC",
			pageSize: 25,
			filter: "active",
		};
		this.setState({ apiParam: params }, () => this.fetchShipmentTypeList());

		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
			editShipmentTypeCheckbox: localStorage.getItem("editShipmentTypeFlagCheck") == 1 ? true : false,
		});
	}

	fetchShipmentTypeList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		this.setState({ pageloading: true });
		API.post("api/admin/shipment-type/list", data).then((response) => {
			const pagination = { ...this.state.pagination };
			if (response) {


				if (response.status === 1) {
					pagination.total = response.data.shipmentTypeList.count;

					this.setState({
						shipmentTypeList: response.data.shipmentTypeList.rows,
						pagination,
						pageloading: false,
					});

				} else {
					this.setState({
						usersList: response.data,
						pagination,
						pageloading: false,
					});
					// message.error(res.message)
				}
			}
		});
	};
	handleChange = (pagination, filters, sorter) => {
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		const queryParams = queryString.parse(window.location.search);
		queryParams.page = pagination.current;
		const newUrl = `${window.location.pathname}?${queryString.stringify(queryParams)}`;
		window.history.pushState(null, null, newUrl);
		let params = {
			...this.state.apiParam,
			pageNo: pagination.current,
			orderBy: sorter.field === "createdAt" ? "created_at" : sorter.field,
			orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
			pageSize: pagination.pageSize,
			filter: filters.status && filters.status[0] ? filters.status[0] : "active",
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchShipmentTypeList();
			}
		);
		scrollTo();
	};
	addShipmentType() {
		this.props.history.push(`${this.props.match.url}/add`);
	}
	delete(id) {
		this.setState({ deleteModal: true });
		shipmentTypeId = id;
	}
	view(id, obj) {
		this.props.history.push({ pathname: `${this.props.match.url}/view-stages/${id}`, state: obj });
	}
	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}

	handleCancel = () => {
		this.setState({ deleteModal: false, confirmLoading: false });
	};

	handleOk = () => {
		const id = shipmentTypeId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { shipment_type_id: id };
		API.post("api/admin/shipment-type/delete", deleteData)
			.then((response) => {
				if (response) {
					this.fetchShipmentTypeList();
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				//console.log("error:", error);
				this.setState({
					confirmLoading: false,
					deleteModal: false,
				});
			});
	};
	handleStatusChange = (shipment_type_id) => {
		const statusData = [];
		this.setState({ confirmLoading: true });
		statusData["data"] = { shipment_type_id: shipment_type_id };
		API.post("api/admin/shipment-type/status", statusData).then((response) => {
			if (response) {
				this.fetchShipmentTypeList({
					page: this.state.pagination.current ? this.state.pagination.current : 1,
				});
				this.setState({ confirmLoading: false });
				message.success(response.message);
			} else {
				this.setState({ confirmLoading: false });
				message.error(response.message);
			}
		});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchShipmentTypeList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					pageNo: 1,
				},
				search: e,
			},
			this.callback
		);
	};
	render() {


		const columns = [
			{
				title: "Name",
				dataIndex: "name",
				key: "name",
				sorter: true,
				maxWidth: "25%",
			},
			{
				title: "Number of stages",
				dataIndex: "number_of_stages",
				key: "number_of_stages",
				maxWidth: "25%",
				sorter: true,
				align: "center",
			},
			{
				title: "Status",
				dataIndex: "status",
				key: "status",
				maxWidth: "25%",
				align: "center",
				filterMultiple: false,
				filtered: true,
				defaultFilteredValue: ["active"],
				filters: [
					{ text: "Active", value: "active" },
					{ text: "Inactive", value: "inactive" },
				],

				render: (text, record) => {
					return (
						<div>

							<button
								className={record.status === "active" ? "statusButtonActive" : "statusButtoninactive"}
								onClick={() => this.handleStatusChange(record.shipment_type_id)}>
								<Icon type='swap' />
								{record.status === "active" ? " Active" : " Inactive"}
							</button>

						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				maxWidth: "25%",
				align: "center",
				render: (record, text) => {


					const obj = {
						objAdmin: record.admin_id,
						objStaff: record.staff_id,
						objCompany: record.company_id,
					}

					return (
						<div
							className='icons'
							style={{
								display: "flex",
								justifyContent: "center",
							}}>
							<Tooltip title='View'>
								<Button
									type='primary'
									className='c-btn c-info c-round'
									icon='eye'
									onClick={() => this.view(text.shipment_type_id, obj)}></Button>
							</Tooltip>
							{this.state.editShipmentTypeCheckbox == false && this.state.staffId !== null ? (
								""
							) : (
								<Tooltip title='Edit'>
									<Button
										type='primary'
										className='c-btn c-round c-warning'
										icon='edit'
										onClick={() => this.edit(text.shipment_type_id)}
									></Button>
								</Tooltip>
							)
							}
							{this.state.editShipmentTypeCheckbox == false && this.state.staffId !== null ? (
								""
							) : (
								<Tooltip title='Delete'>
									<Button
										type='primary'
										className='c-btn c-round c-danger'
										icon='delete'
										onClick={() => this.delete(text.shipment_type_id)}></Button>
								</Tooltip>

							)
							}
						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<Col sm={7}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' /> &emsp;Shipment Type Management
							</h2>
						</Col>
						<Col sm={5}>
							{this.state.search ? (
								<p
									style={{
										margin: "5px",
										textAlign: "right",
										color: "#303030",
									}}>
									Matching Records: {this.state.pagination.total}
								</p>
							) : (
								""
							)}
						</Col>
						<Col sm={12}>
							<Row>
								<Col sm={16} style={{ textAlign: "right" }}>
									<Search
										placeholder='Search shipment type'
										onChange={(e) => this.handleSearch(e.target.value)}
										value={this.state.search}
										style={{ width: 200 }}
									/>
								</Col>
								{
									<Col sm={8}>
										<Button
											className='addButton'
											style={{ marginTop: "0" }}
											type='primary'
											onClick={() => this.addShipmentType()}>
											+ Add Shipment Type
										</Button>
									</Col>
								}
							</Row>
						</Col>
					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div style={{ marginTop: "-1.5rem", overflowX: "auto" }}>
							<Table
								bordered={true}
								columns={columns}
								pagination={{
									// pageSize: 10,
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 25,
									pageSizeOptions: ["25", "50"],
									current: this.state.apiParam.pageNo
								}}
								dataSource={this.state.shipmentTypeList}
								onChange={this.handleChange}
							/>
						</div>
					</LayoutContent>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to delete shipment type?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}



export default ShipmentTypeManagement;
