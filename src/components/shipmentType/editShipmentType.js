import "../../static/css/add.css";

import { Button, Form, Icon, Input, Spin, message, Checkbox, Modal, Tooltip } from "antd";
import Api from "../../api/api-handler";

import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import React from "react";
import appActions from "../../redux/app/actions";
import { connect } from "react-redux";

const { changeCurrent } = appActions;
const { TextArea } = Input;


const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});

class EditShipmentType extends React.Component {
	state = {
		shipmentTypeData: null,
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		integrationKeyStatus: false,
		integrationKey: "",
		trueCheckBox: false,
		trueCheckBoxStageArray: [0],
		pdf_time_stamp_checked: false,

		supervisorSignatureOriginFlag: false,
		supervisorSignatureOriginId: null,
		supervisorSignatureOriginStageId: null,
		supervisorSignatureOriginNewId: null,
		changeModelSupervisorSignatureAtOrigin: false,

		supervisorSignatureDestinationFlag: false,
		supervisorSignatureDestinationId: null,
		supervisorSignatureDestinationStageId: null,
		supervisorSignatureDestinationNewId: null,
		changeModelSupervisorSignatureAtDestination: false,

		customerSignatureOriginFlag: false,
		customerSignatureOriginId: null,
		customerSignatureOriginStageId: null,
		customerSignatureOriginNewId: null,
		changeModelCustomerSignatureAtOrigin: false,

		customerSignatureDestinationFlag: false,
		customerSignatureDestinationId: null,
		customerSignatureDestinationStageId: null,
		customerSignatureDestinationNewId: null,
		changeModelCustomerSignatureAtDestination: false,


		stageFields: [
			// {
			//   optionName: "",
			//   optionValue: "",
			// },
		],

		selectedScanIntoStorageIndex: null,
		isMakeExceptions: [],
		isMakeEnablePartialComplete: [],
		isMakeDefaultManualLabelVisible: [],
		isMakeAddScanVisible: [],
		isMakeRemoveScanVisible: [],
		isRemoveItemFromInvnetoryChecked: false

	};
	componentDidMount() {
		this.setState({
			pdf_time_stamp_checked: localStorage.getItem("pdf_time_stamp_checked")
				&& localStorage.getItem("pdf_time_stamp_checked") == 1 ? true
				: false
		});

		const id = this.props.match.params.id;
		this.props.changeCurrent("shipment-type");
		this.setState({ contentloader: true });


		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							integrationKeyStatus: true,
							integrationKey: response.data.integration_key
						});
					} else {
						this.setState({
							integrationKeyStatus: false,
							integrationKey: response.data.integration_key
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		const data = [];
		data["data"] = { shipment_type_id: id };
		API.post("api/admin/shipment-type/view-shipment-type", data)
			.then((response) => {
				if (response) {
					let stageFields = [];
					for (let index = 0; index < response.data.number_of_stages; index++) {
						if (response.data.shipment_stage[index].is_add_exceptions) {
							const updatedIsMakeExceptions = [...this.state.isMakeExceptions];
							updatedIsMakeExceptions[index] = true;
							this.setState({ isMakeExceptions: updatedIsMakeExceptions });
						}
						if (response.data.shipment_stage[index].add_items_to_inventory) {
							const updatedisMakeScanVisible = [...this.state.isMakeAddScanVisible];
							updatedisMakeScanVisible[index + 1] = true;

							const updatedisMakeEnablePartialComplete = [...this.state.isMakeDefaultManualLabelVisible];
							updatedisMakeEnablePartialComplete[index] = true;

							this.setState({
								isMakeDefaultManualLabelVisible: updatedisMakeEnablePartialComplete,
								isMakeAddScanVisible: updatedisMakeScanVisible
							});
						}
						if (
							response.data.shipment_stage[index].remove_items_to_inventory ||
							response.data.shipment_stage[index].unassign_storage_units_from_items
						) {
							const updatedisMakeEnablePartialComplete = [...this.state.isMakeEnablePartialComplete];
							updatedisMakeEnablePartialComplete[index] = true;
							this.setState({ isMakeEnablePartialComplete: updatedisMakeEnablePartialComplete });
						}

						if (
							response.data.shipment_stage[index].remove_items_to_inventory
						) {
							const updatedisMakeScanVisible = [...this.state.isMakeRemoveScanVisible];
							updatedisMakeScanVisible[index + 1] = true;
							this.setState({ isMakeRemoveScanVisible: updatedisMakeScanVisible });
						}

						stageFields.push({
							shipment_stage_id: response.data.shipment_stage[index].shipment_stage_id,
							name: response.data.shipment_stage[index].name,
							allow_default_manual_label: response.data.shipment_stage[index].allow_default_manual_label,
							add_items_to_inventory: response.data.shipment_stage[index].add_items_to_inventory,
							assign_storage_units_to_items: response.data.shipment_stage[index].assign_storage_units_to_items,
							unassign_storage_units_from_items: response.data.shipment_stage[index].unassign_storage_units_from_items,
							remove_items_to_inventory: response.data.shipment_stage[index].remove_items_to_inventory,
							enable_partial_complete_stage: response.data.shipment_stage[index].enable_partial_complete_stage,
							order_of_stages: response.data.shipment_stage[index].order_of_stages,
							scan_require: response.data.shipment_stage[index].scan_require,
							remove_scan_require: response.data.shipment_stage[index].remove_scan_require,
							scan_into_storage: response.data.shipment_stage[index].scan_into_storage,
							scan_out_of_storage: response.data.shipment_stage[index].scan_out_of_storage,
							is_add_exceptions: response.data.shipment_stage[index].is_add_exceptions,
							show_no_exceptions: response.data.shipment_stage[index].show_no_exceptions,
							PDF_time_require: response.data.shipment_stage[index].PDF_time_require,
							is_add_item: response.data.shipment_stage[index].is_add_item,
							supervisor_signature_require: response.data.shipment_stage[index].supervisor_signature_require,
							why_supervisor_signature_require_note: response.data.shipment_stage[index].why_supervisor_signature_require_note,
							customer_signature_require: response.data.shipment_stage[index].customer_signature_require,
							why_customer_signature_require_note: response.data.shipment_stage[index].why_customer_signature_require_note,
							customer_signature_require_at_origin_to_all_pages: response.data.shipment_stage[index].customer_signature_require_at_origin_to_all_pages,
							customer_signature_require_at_destination_to_all_pages: response.data.shipment_stage[index].customer_signature_require_at_destination_to_all_pages,
							supervisor_signature_require_at_origin_to_all_pages: response.data.shipment_stage[index].supervisor_signature_require_at_origin_to_all_pages,
							supervisor_signature_require_at_destination_to_all_pages: response.data.shipment_stage[index].supervisor_signature_require_at_destination_to_all_pages,
						});
					}


					response.data.shipment_stage.forEach((newData, id) => {
						if (newData.supervisor_signature_require_at_origin_to_all_pages === 1) {
							this.setState({
								supervisorSignatureOriginId: `stages[${id}].supervisor_signature_require_at_origin_to_all_pages`,
								supervisorSignatureOriginStageId: id,
								supervisorSignatureOriginFlag: true
							})
						}
						if (newData.supervisor_signature_require_at_destination_to_all_pages === 1) {
							this.setState({
								supervisorSignatureDestinationId: `stages[${id}].supervisor_signature_require_at_destination_to_all_pages`,
								supervisorSignatureDestinationStageId: id,
								supervisorSignatureDestinationFlag: true
							})
						}
						if (newData.customer_signature_require_at_origin_to_all_pages === 1) {
							this.setState({
								customerSignatureOriginId: `stages[${id}].customer_signature_require_at_origin_to_all_pages`,
								customerSignatureOriginStageId: id,
								customerSignatureOriginFlag: true
							})
						}
						if (newData.customer_signature_require_at_destination_to_all_pages === 1) {
							this.setState({
								customerSignatureDestinationId: `stages[${id}].customer_signature_require_at_destination_to_all_pages`,
								customerSignatureDestinationStageId: id,
								customerSignatureDestinationFlag: true
							})
						}
					});

					let findFirstScanIntoStorageIndex = null;
					if (response.data.shipment_stage && response.data.shipment_stage.length) findFirstScanIntoStorageIndex = response.data.shipment_stage.findIndex((value) => value.scan_into_storage === 1);

					this.setState(
						{
							shipmentTypeData: response.data && response.data,
							contentloader: false,
							selectedScanIntoStorageIndex: (findFirstScanIntoStorageIndex && findFirstScanIntoStorageIndex !== -1 || findFirstScanIntoStorageIndex === 0) ? findFirstScanIntoStorageIndex : null
						},
						this.setState({ stageFields }),
						() => this.props.form.setFieldsValue({})
					);
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((err) => {
				message.error(err);
				this.setState({ contentloader: false });
				this.props.history.goBack();
			});
	}

	addStageField = (e) => {
		let stageFields = [];
		for (let index = 0; index < e.target.value; index++) {
			stageFields.push("");
		}
		this.setState({ stageFields });
	};

	removeStageField = (i) => {
		let stageFields = [...this.state.stageFields];
		stageFields.splice(i, 1);
		this.setState({ stageFields });
	};

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};

	checkBoxHandler = (event, i, fieldName) => {
		const { checked } = event.target;
		if (fieldName === 'remove_scan_require' && checked) {
			this.props.form.setFieldsValue({ [`stages[${i}].scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_scan_require`]: true });
			this.props.form.setFieldsValue({ [`stages[${i}].add_items_to_inventory`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].assign_storage_units_to_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].unassign_storage_units_from_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_items_to_inventory`]: false });

			const updateMakeDefaultManualLabelVisible = [...this.state.isMakeDefaultManualLabelVisible];
			updateMakeDefaultManualLabelVisible[i] = false;

			const updateMakeScanVisible = [...this.state.isMakeAddScanVisible];
			updateMakeScanVisible[i + 1] = false;

			const updateMakeRemoveScanVisible = [...this.state.isMakeRemoveScanVisible];
			updateMakeRemoveScanVisible[i + 1] = false;

			const updatedisMakeEnablePartialComplete = [...this.state.isMakeEnablePartialComplete];
			updatedisMakeEnablePartialComplete[i] = false;
			this.setState({
				isMakeEnablePartialComplete: updatedisMakeEnablePartialComplete,
				isMakeDefaultManualLabelVisible: updateMakeDefaultManualLabelVisible,
				isMakeAddScanVisible: updateMakeScanVisible,
				isMakeRemoveScanVisible: updateMakeRemoveScanVisible,
			});
		}

		else if (fieldName === 'scan_require' && checked) {
			this.props.form.setFieldsValue({ [`stages[${i}].scan_require`]: true });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].add_items_to_inventory`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].assign_storage_units_to_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].unassign_storage_units_from_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_items_to_inventory`]: false });

			const updateMakeDefaultManualLabelVisible = [...this.state.isMakeDefaultManualLabelVisible];
			updateMakeDefaultManualLabelVisible[i] = false;

			const updateMakeScanVisible = [...this.state.isMakeAddScanVisible];
			updateMakeScanVisible[i + 1] = false;

			const updateMakeRemoveScanVisible = [...this.state.isMakeRemoveScanVisible];
			updateMakeRemoveScanVisible[i + 1] = false;

			const updatedisMakeEnablePartialComplete = [...this.state.isMakeEnablePartialComplete];
			updatedisMakeEnablePartialComplete[i] = false;
			this.setState({
				isMakeEnablePartialComplete: updatedisMakeEnablePartialComplete,
				isMakeDefaultManualLabelVisible: updateMakeDefaultManualLabelVisible,
				isMakeAddScanVisible: updateMakeScanVisible,
				isMakeRemoveScanVisible: updateMakeRemoveScanVisible,
			});
		}
		else if (fieldName === 'add_items_to_inventory' && checked) {
			this.props.form.setFieldsValue({ [`stages[${i}].scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].add_items_to_inventory`]: true });
			this.props.form.setFieldsValue({ [`stages[${i}].assign_storage_units_to_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].unassign_storage_units_from_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_items_to_inventory`]: false });

			const updateMakeDefaultManualLabelVisible = [...this.state.isMakeDefaultManualLabelVisible];
			updateMakeDefaultManualLabelVisible[i] = true;

			const updateMakeScanVisible = [...this.state.isMakeAddScanVisible];
			updateMakeScanVisible[i + 1] = true;

			const updateMakeRemoveScanVisible = [...this.state.isMakeRemoveScanVisible];
			updateMakeRemoveScanVisible[i + 1] = false;

			const updatedisMakeEnablePartialComplete = [...this.state.isMakeEnablePartialComplete];
			updatedisMakeEnablePartialComplete[i] = false;
			this.setState({
				isMakeEnablePartialComplete: updatedisMakeEnablePartialComplete,
				isMakeDefaultManualLabelVisible: updateMakeDefaultManualLabelVisible,
				isMakeAddScanVisible: updateMakeScanVisible,
				isMakeRemoveScanVisible: updateMakeRemoveScanVisible,
			});
		}

		else if (fieldName === 'assign_storage_units_to_items' && checked) {
			this.props.form.setFieldsValue({ [`stages[${i}].scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].add_items_to_inventory`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].assign_storage_units_to_items`]: true });
			this.props.form.setFieldsValue({ [`stages[${i}].unassign_storage_units_from_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_items_to_inventory`]: false });

			const updateMakeDefaultManualLabelVisible = [...this.state.isMakeDefaultManualLabelVisible];
			updateMakeDefaultManualLabelVisible[i] = false;

			const updateMakeScanVisible = [...this.state.isMakeAddScanVisible];
			updateMakeScanVisible[i + 1] = false;

			const updateMakeRemoveScanVisible = [...this.state.isMakeRemoveScanVisible];
			updateMakeRemoveScanVisible[i + 1] = false;

			const updatedisMakeEnablePartialComplete = [...this.state.isMakeEnablePartialComplete];
			updatedisMakeEnablePartialComplete[i] = false;
			this.setState({
				isMakeEnablePartialComplete: updatedisMakeEnablePartialComplete,
				isMakeDefaultManualLabelVisible: updateMakeDefaultManualLabelVisible,
				isMakeAddScanVisible: updateMakeScanVisible,
				isMakeRemoveScanVisible: updateMakeRemoveScanVisible,
			});

		}
		else if (fieldName === 'unassign_storage_units_from_items' && checked) {
			this.props.form.setFieldsValue({ [`stages[${i}].scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].add_items_to_inventory`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].assign_storage_units_to_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].unassign_storage_units_from_items`]: true });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_items_to_inventory`]: false });

			const updateMakeDefaultManualLabelVisible = [...this.state.isMakeDefaultManualLabelVisible];
			updateMakeDefaultManualLabelVisible[i] = false;

			const updateMakeScanVisible = [...this.state.isMakeAddScanVisible];
			updateMakeScanVisible[i + 1] = false;

			const updateMakeRemoveScanVisible = [...this.state.isMakeRemoveScanVisible];
			updateMakeRemoveScanVisible[i + 1] = false;

			const updatedisMakeEnablePartialComplete = [...this.state.isMakeEnablePartialComplete];
			updatedisMakeEnablePartialComplete[i] = checked;
			this.setState({
				isMakeEnablePartialComplete: updatedisMakeEnablePartialComplete,
				isMakeDefaultManualLabelVisible: updateMakeDefaultManualLabelVisible,
				isMakeAddScanVisible: updateMakeScanVisible,
				isMakeRemoveScanVisible: updateMakeRemoveScanVisible,
				isRemoveItemFromInvnetoryChecked: false
			});
		}
		else if (fieldName === 'remove_items_to_inventory' && checked) {
			this.props.form.setFieldsValue({ [`stages[${i}].scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].add_items_to_inventory`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].assign_storage_units_to_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].unassign_storage_units_from_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_items_to_inventory`]: true });

			const updateMakeDefaultManualLabelVisible = [...this.state.isMakeDefaultManualLabelVisible];
			updateMakeDefaultManualLabelVisible[i] = false;

			const updateMakeScanVisible = [...this.state.isMakeAddScanVisible];
			updateMakeScanVisible[i + 1] = false;

			const updateMakeRemoveScanVisible = [...this.state.isMakeRemoveScanVisible];
			updateMakeRemoveScanVisible[i + 1] = true;

			const updatedisMakeEnablePartialComplete = [...this.state.isMakeEnablePartialComplete];
			updatedisMakeEnablePartialComplete[i] = checked;
			this.setState({
				isMakeEnablePartialComplete: updatedisMakeEnablePartialComplete,
				isMakeDefaultManualLabelVisible: updateMakeDefaultManualLabelVisible,
				isMakeAddScanVisible: updateMakeScanVisible,
				isMakeRemoveScanVisible: updateMakeRemoveScanVisible,
				isRemoveItemFromInvnetoryChecked: true
			});
		}
		else {
			this.props.form.setFieldsValue({ [`stages[${i}].scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_scan_require`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].add_items_to_inventory`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].assign_storage_units_to_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].unassign_storage_units_from_items`]: false });
			this.props.form.setFieldsValue({ [`stages[${i}].remove_items_to_inventory`]: false });

			const updateMakeDefaultManualLabelVisible = [...this.state.isMakeDefaultManualLabelVisible];
			updateMakeDefaultManualLabelVisible[i] = false;

			const updateMakeScanVisible = [...this.state.isMakeAddScanVisible];
			updateMakeScanVisible[i + 1] = false;

			const updateMakeRemoveScanVisible = [...this.state.isMakeRemoveScanVisible];
			updateMakeRemoveScanVisible[i + 1] = false;

			const updatedisMakeEnablePartialComplete = [...this.state.isMakeEnablePartialComplete];
			updatedisMakeEnablePartialComplete[i] = false;
			this.setState({
				isMakeEnablePartialComplete: updatedisMakeEnablePartialComplete,
				isMakeDefaultManualLabelVisible: updateMakeDefaultManualLabelVisible,
				isMakeAddScanVisible: updateMakeScanVisible,
				isMakeRemoveScanVisible: updateMakeRemoveScanVisible,
			});
		}
	}

	handleUpload = ({ fileList }) => {
		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};
	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {

			values.stages = values.stages.map(stages => {
				stages.show_no_exceptions = stages && stages.is_add_exceptions ? stages.show_no_exceptions : false
				stages.scan_into_storage = stages.scan_into_storage ? stages.scan_into_storage : 0
				stages.scan_out_of_storage = stages.scan_out_of_storage ? stages.scan_out_of_storage : 0
				return stages;
			})

			let params = {
				shipment_type_id: this.props.match.params.id,
				name: values.name,
				number_of_stages: values.number_of_stages,
				is_pickup_date_mandatory: values.is_pickup_date_mandatory,
				is_make_user_mandatory: values.is_make_user_mandatory,
				stages: values.stages,
			};
			if (!err) {
				const data = [];

				data["data"] = params;
				this.setState({ loading: true });

				API.post("api/admin/shipment-type/edit", data).then((response) => {
					if (response) {
						this.setState({ loading: false });
						message.success(response.message);
						this.props.history.goBack();
					} else {
						this.setState({ loading: false });
						message.error(response.message);
					}
				});
			}
		});
	};

	isMakeExceptionsHandler = (e, index) => {
		const updatedIsMakeExceptions = [...this.state.isMakeExceptions];
		updatedIsMakeExceptions[index] = e.target.checked;
		this.setState({ isMakeExceptions: updatedIsMakeExceptions });
	};


	onChangeSupervisorSignatureOrigin = (event, id) => {
		if (event.target.checked) {
			if (this.state.supervisorSignatureOriginFlag) {
				this.setState({
					supervisorSignatureOriginNewId: event.target.id,
					changeModelSupervisorSignatureAtOrigin: true,
				})
			}
			else {
				this.setState({
					supervisorSignatureOriginFlag: true,
					supervisorSignatureOriginId: event.target.id,
					supervisorSignatureOriginStageId: id
				})
				for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
					if (`stages[${i}].supervisor_signature_require_at_origin_to_all_pages` === event.target.id) {
						this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_origin_to_all_pages`]: true });
					} else {
						this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_origin_to_all_pages`]: false });
					}
				}
			}
		}
		else {
			this.setState({
				supervisorSignatureOriginFlag: false,
				supervisorSignatureOriginId: null,
				supervisorSignatureOriginStageId: null
			})
		}
	}

	handleOkSupervisorSignatureAtOrigin = () => {
		for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
			if (`stages[${i}].supervisor_signature_require_at_origin_to_all_pages` === this.state.supervisorSignatureOriginNewId) {
				this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_origin_to_all_pages`]: true });
			} else {
				this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_origin_to_all_pages`]: false })
			}
		}
		this.setState({
			supervisorSignatureOriginId: this.state.supervisorSignatureOriginNewId,
			changeModelSupervisorSignatureAtOrigin: false,
		})
	}

	handleCancelSupervisorSignatureAtOrigin = () => {
		for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
			if (`stages[${i}].supervisor_signature_require_at_origin_to_all_pages` === this.state.supervisorSignatureOriginNewId) {
				this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_origin_to_all_pages`]: false });
			}
		}
		this.setState({
			changeModelSupervisorSignatureAtOrigin: false,
			supervisorSignatureOriginNewId: null,
		});
	};


	onChangeSupervisorSignatureDestination = (event, id) => {
		if (event.target.checked) {
			if (this.state.supervisorSignatureDestinationFlag) {
				this.setState({
					supervisorSignatureDestinationNewId: event.target.id,
					changeModelSupervisorSignatureAtDestination: true,
					supervisorSignatureDestinationStageId: id
				})
			}
			else {
				this.setState({
					supervisorSignatureDestinationFlag: true,
					supervisorSignatureDestinationId: event.target.id,
					supervisorSignatureDestinationStageId: id
				})
				for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
					if (`stages[${i}].supervisor_signature_require_at_destination_to_all_pages` === event.target.id) {
						this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_destination_to_all_pages`]: true });
					} else {
						this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_destination_to_all_pages`]: false });
					}
				}
			}
		}
		else {
			this.setState({
				supervisorSignatureDestinationFlag: false,
				supervisorSignatureDestinationId: null,
				supervisorSignatureDestinationStageId: null
			})
		}
	}

	handleOkSupervisorSignatureAtDestination = () => {

		for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
			if (`stages[${i}].supervisor_signature_require_at_destination_to_all_pages` === this.state.supervisorSignatureDestinationNewId) {
				this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_destination_to_all_pages`]: true });
			} else {
				this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_destination_to_all_pages`]: false });
			}
		}
		this.setState({
			supervisorSignatureDestinationId: this.state.supervisorSignatureDestinationNewId,
			changeModelSupervisorSignatureAtDestination: false,
		})
	}

	handleCancelSupervisorSignatureAtDestination = () => {
		for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
			if (`stages[${i}].supervisor_signature_require_at_destination_to_all_pages` === this.state.supervisorSignatureDestinationNewId) {
				this.props.form.setFieldsValue({ [`stages[${i}].supervisor_signature_require_at_destination_to_all_pages`]: false });
			}
		}
		this.setState({ changeModelSupervisorSignatureAtDestination: false, supervisorSignatureDestinationNewId: null });
	};



	onChangeCustomerSignatureOrigin = (event, id) => {
		if (event.target.checked) {
			if (this.state.customerSignatureOriginFlag) {
				this.setState({
					customerSignatureOriginNewId: event.target.id,
					changeModelCustomerSignatureAtOrigin: true,
					customerSignatureOriginStageId: id
				})
			}
			else {
				this.setState({
					customerSignatureOriginFlag: true,
					customerSignatureOriginId: event.target.id,
					customerSignatureOriginStageId: id

				})
				for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
					if (`stages[${i}].customer_signature_require_at_origin_to_all_pages` === event.target.id) {
						this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_origin_to_all_pages`]: true });

					} else {
						this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_origin_to_all_pages`]: false });

					}
				}
			}
		}
		else {
			this.setState({
				customerSignatureOriginFlag: false,
				customerSignatureOriginId: null,
				customerSignatureOriginStageId: null
			})
		}

	}


	handleOkCustomerSignatureAtOrigin = () => {
		for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
			if (`stages[${i}].customer_signature_require_at_origin_to_all_pages` === this.state.customerSignatureOriginNewId) {
				this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_origin_to_all_pages`]: true });

			} else {
				this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_origin_to_all_pages`]: false });
			}
		}
		this.setState({
			customerSignatureOriginId: this.state.customerSignatureOriginNewId,
			changeModelCustomerSignatureAtOrigin: false,
		})
	}

	handleCancelCustomerSignatureAtOrigin = () => {
		for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
			if (`stages[${i}].customer_signature_require_at_origin_to_all_pages` === this.state.customerSignatureOriginNewId) {
				this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_origin_to_all_pages`]: false });
			}
		}
		this.setState({ changeModelCustomerSignatureAtOrigin: false, customerSignatureOriginNewId: null });
	};

	onChangeCustomerSignatureDestination = (event, id) => {
		if (event.target.checked) {
			if (this.state.customerSignatureDestinationFlag) {
				this.setState({
					customerSignatureDestinationNewId: event.target.id,
					changeModelCustomerSignatureAtDestination: true,
					customerSignatureDestinationStageId: id
				})
			}
			else {
				this.setState({
					customerSignatureDestinationFlag: true,
					customerSignatureDestinationId: event.target.id,
					customerSignatureDestinationStageId: id

				})
				for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
					if (`stages[${i}].customer_signature_require_at_destination_to_all_pages` === event.target.id) {
						this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_destination_to_all_pages`]: true });

					} else {
						this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_destination_to_all_pages`]: false });

					}
				}
			}
		}
		else {
			this.setState({
				customerSignatureDestinationFlag: false,
				customerSignatureDestinationId: null,
				customerSignatureDestinationStageId: null
			})
		}

	}


	handleOkCustomerSignatureAtDestination = () => {

		for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
			if (`stages[${i}].customer_signature_require_at_destination_to_all_pages` === this.state.customerSignatureDestinationNewId) {
				this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_destination_to_all_pages`]: true });

			} else {
				this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_destination_to_all_pages`]: false });

			}
		}
		this.setState({
			customerSignatureDestinationId: this.state.customerSignatureDestinationNewId,
			changeModelCustomerSignatureAtDestination: false,
		})
	}

	handleCancelCustomerSignatureAtDestination = () => {
		for (let i = 0; i <= this.state.stageFields.length - 1; i++) {
			if (`stages[${i}].customer_signature_require_at_destination_to_all_pages` === this.state.customerSignatureDestinationNewId) {
				this.props.form.setFieldsValue({ [`stages[${i}].customer_signature_require_at_destination_to_all_pages`]: false });
			}
		}
		this.setState({ changeModelCustomerSignatureAtDestination: false, customerSignatureDestinationNewId: null });
	};



	render() {
		const { getFieldDecorator } = this.props.form;
		const { shipmentTypeData } = this.state;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Edit Shipment Type
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Name'>
									{getFieldDecorator("name", {
										initialValue: shipmentTypeData && shipmentTypeData.name,
										placeholder: "Shipment Type Name",
										rules: [
											{
												required: true,
												message: "Please input shipment type name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Shipment Type Name' />)}
								</Form.Item>

								<Form.Item label='Number of stages'>
									{getFieldDecorator("number_of_stages", {
										initialValue: shipmentTypeData && shipmentTypeData.number_of_stages,
										placeholder: "Number of stages",
										rules: [
											{
												required: true,
												message: "Please input number of stages!",
											},
										],
									})(
										<Input placeholder='Number of stages' onChange={this.addStageField} disabled='disabled' />
									)}
								</Form.Item>


								<Form.Item label='Make Pickup Date Mandatory'>
									{getFieldDecorator(`is_pickup_date_mandatory`, {
										valuePropName: "checked",
										initialValue: shipmentTypeData && shipmentTypeData.is_pickup_date_mandatory,
									})(
										<Checkbox>Make Pickup Date Mandatory</Checkbox>)}
								</Form.Item>

								<Form.Item label='Make User Mandatory'>
									{getFieldDecorator(`is_make_user_mandatory`, {
										valuePropName: "checked",
										initialValue: shipmentTypeData && shipmentTypeData.is_make_user_mandatory,
									})(
										<Checkbox>Make User Mandatory</Checkbox>)}
								</Form.Item>

								<div>
									{this.state.stageFields.map((el, i) => (
										<div>
											<Form.Item label='Stage Number'>
												<h2>-----Stage {i + 1}-----</h2>
											</Form.Item>
											<Form.Item>
												{getFieldDecorator(`stages[${i}].shipment_stage_id`, {
													initialValue: shipmentTypeData && el.shipment_stage_id,
													placeholder: "Stage Id",
												})(<Input placeholder='Stage Name' type='hidden' />)}
											</Form.Item>
											<Form.Item label='Stage Name'>
												{getFieldDecorator(`stages[${i}].name`, {
													initialValue: shipmentTypeData && el.name,
													placeholder: "Stage Name",
													rules: [
														{
															required: true,
															message: "Please input stage name!",
															whitespace: true,
														},
													],
												})(<Input placeholder='Stage Name' />)}
											</Form.Item>
											<Form.Item label='Stage Order'>
												{getFieldDecorator(`stages[${i}].order_of_stages`, {
													initialValue: shipmentTypeData && el.order_of_stages,
													placeholder: "Stage Order",
													rules: [
														{
															required: true,
															message: "Please input stage order!",
														},
													],
												})(<Input disabled placeholder='Stage Order' />)}
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}>
												<h3>Stage Activities</h3>
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(`stages[${i}].add_items_to_inventory`, {
													valuePropName: "checked",
													initialValue: shipmentTypeData && el.add_items_to_inventory,
												})(
													<Checkbox
														onChange={(e) => { this.checkBoxHandler(e, i, 'add_items_to_inventory') }}
													>
														<Tooltip
															title="This option is generally used for the first scan when creating a digital inventory or adding additional items to the inventory later.">
															Add Items To Inventory &nbsp;
															<Icon type="info-circle" />
														</Tooltip>
													</Checkbox>)}
											</Form.Item>

											{this.state.isMakeAddScanVisible[i] && (
												<Form.Item
													label=" "
													colon={false}
												>
													{getFieldDecorator(`stages[${i}].scan_require`, {
														valuePropName: "checked",
														initialValue: shipmentTypeData && el.scan_require,
													})(
														<Checkbox
															onChange={(e) => { this.checkBoxHandler(e, i, 'scan_require') }}
														>
															<Tooltip
																title="This option is generally used if an additional scan is required, such as when loading items into the truck.  The additional scan required option is only available after adding or removing items from inventory, not for storage.">
																Additional Scan &nbsp;
																<Icon type="info-circle" />
															</Tooltip>
														</Checkbox>)}
												</Form.Item>
											)
											}

											{this.state.isMakeDefaultManualLabelVisible[i] && (
												<Form.Item
													label=" "
													colon={false}
												>
													{getFieldDecorator(`stages[${i}].allow_default_manual_label`, {
														valuePropName: "checked",
														initialValue: shipmentTypeData && el.allow_default_manual_label,
													})(
														<Checkbox>Default Manual Label Mode</Checkbox>)}
												</Form.Item>
											)}

											{
												this.state.integrationKeyStatus && i > 0 ? (
													<Form.Item
														label=" "
														colon={false}
													>
														{getFieldDecorator(`stages[${i}].assign_storage_units_to_items`, {
															valuePropName: 'checked',
															initialValue: shipmentTypeData && el.assign_storage_units_to_items,
														})(
															<Checkbox
																onChange={(e) => { this.checkBoxHandler(e, i, 'assign_storage_units_to_items') }}
															>
																<Tooltip
																	title="This option is generally used during 'Storage In' to scan items into your warehouse/storage facilities’ racks, shelves, bays, vaults, etc., or to a staging area.">
																	Add Items To Storage &nbsp;
																	<Icon type="info-circle" />
																</Tooltip>
															</Checkbox>
														)}
													</Form.Item>
												) : (
													''
												)
											}

											{
												this.state.integrationKeyStatus && i > 0 ? (
													<Form.Item
														label=" "
														colon={false}
													>
														{getFieldDecorator(`stages[${i}].unassign_storage_units_from_items`, {
															valuePropName: 'checked',
															initialValue: shipmentTypeData && el.unassign_storage_units_from_items,
														})(
															<Checkbox
																onChange={(e) => { this.checkBoxHandler(e, i, 'unassign_storage_units_from_items') }}>
																<Tooltip
																	title="This option is generally used to scan items out of your warehouse or storage facility during Storage-Out.">
																	Remove Items From Storage&nbsp;
																	<Icon type="info-circle" />
																</Tooltip>
															</Checkbox>
														)}
													</Form.Item>
												) : (
													''
												)
											}

											{
												i > 0 ? (
													<Form.Item
														label=" "
														colon={false}
													>
														{getFieldDecorator(`stages[${i}].remove_items_to_inventory`, {
															valuePropName: 'checked',
															initialValue: shipmentTypeData && el.remove_items_to_inventory,
														})(
															<Checkbox
																onChange={(e) => {
																	this.checkBoxHandler(e, i, 'remove_items_to_inventory');
																}}
															>
																<Tooltip
																	title="This option is generally used to remove items from inventory, such as during delivery.">
																	Remove Items From Inventory&nbsp;
																	<Icon type="info-circle" />
																</Tooltip>
															</Checkbox>
														)}
													</Form.Item>
												) : (
													''
												)
											}

											{this.state.isMakeRemoveScanVisible[i] && (
												<Form.Item
													label=" "
													colon={false}
												>
													{getFieldDecorator(`stages[${i}].remove_scan_require`, {
														valuePropName: "checked",
														initialValue: shipmentTypeData && el.remove_scan_require,
													})(
														<Checkbox
															onChange={(e) => { this.checkBoxHandler(e, i, 'remove_scan_require') }}
														>
															<Tooltip
																title="This option is generally used if an additional scan is required, such as when unloading items from the truck. The additional scan required option is only available after adding or removing items from inventory, not for storage.">
																Additional Scan&nbsp;
																<Icon type="info-circle" />
															</Tooltip>
														</Checkbox>)}
												</Form.Item>
											)
											}


											<Form.Item
												label=" "
												colon={false}>
												<h3>Additional Options</h3>
											</Form.Item>

											{this.state.isMakeEnablePartialComplete[i] && (

												<Form.Item
													label=" "
													colon={false}
												>
													{getFieldDecorator(`stages[${i}].enable_partial_complete_stage`, {
														valuePropName: "checked",
														initialValue: shipmentTypeData && el.enable_partial_complete_stage,
													})(
														<Checkbox >

															<Tooltip
																title={this.state.isRemoveItemFromInvnetoryChecked ?
																	"This option allows partial deliveries, such as when the customer requests delivery of only a portion of the inventory. If you select this option your crew will be able to complete a stage without scanning out all of the items either from inventory or storage. If this check box is not selected, your crew must scan every item out of inventory." : "This option allows partial checkouts from storage, such as when the customer requests delivery of only a portion of the items in storage. If you select this option your crew will be able to complete a stage without scanning out all of the items either from inventory or storage. If this check box is not selected, your crew must scan every item out of inventory. The Enable Partial Complete Stage option is only available when removing items from storage or inventory."}>
																Enable Partial Complete Stage&nbsp;
																<Icon type="info-circle" />
															</Tooltip>

														</Checkbox>)}
												</Form.Item>
											)}

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(`stages[${i}].is_add_exceptions`, {
													valuePropName: "checked",
													initialValue: shipmentTypeData && el.is_add_exceptions,
												})(
													<Checkbox onChange={e => { this.isMakeExceptionsHandler(e, i) }} >Make Exceptions Mandatory</Checkbox>)}

											</Form.Item>

											{this.state.isMakeExceptions[i] && (
												<Form.Item
													label=" "
													colon={false}
												>
													{getFieldDecorator(`stages[${i}].show_no_exceptions`, {
														valuePropName: "checked",
														initialValue: shipmentTypeData && el.show_no_exceptions,
													})(
														<Checkbox>Show No Exceptions</Checkbox>)}
												</Form.Item>
											)}


											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(`stages[${i}].PDF_time_require`, {
													valuePropName: "checked",
													initialValue: shipmentTypeData && el.PDF_time_require,
												})(
													<Checkbox>Include Signature Time In PDF</Checkbox>)}
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(`stages[${i}].supervisor_signature_require`, {
													valuePropName: "checked",
													initialValue: shipmentTypeData && el.supervisor_signature_require,
												})(<Checkbox>Carrier Signature Required</Checkbox>)}
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(
													`stages[${i}].supervisor_signature_require_at_origin_to_all_pages`,
													{
														valuePropName: "checked",
														initialValue: `stages[${i}].supervisor_signature_require_at_origin_to_all_pages` === this.state.supervisorSignatureOriginId ? true : false,
													}
												)(<Checkbox
													name="checkSupervisorSignatureAtOrigin"
													checked={`stages[${i}].supervisor_signature_require_at_origin_to_all_pages` === this.state.supervisorSignatureOriginId ? true : false}
													onChange={e => { this.onChangeSupervisorSignatureOrigin(e, i) }}
												>Apply this signature at origin to all pages of the inventory</Checkbox>)}
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(
													`stages[${i}].supervisor_signature_require_at_destination_to_all_pages`,
													{
														valuePropName: "checked",
														initialValue: `stages[${i}].supervisor_signature_require_at_destination_to_all_pages` === this.state.supervisorSignatureDestinationId ? true : false
													}
												)(<Checkbox
													name="checkSupervisorSignatureAtDestination"
													checked={`stages[${i}].supervisor_signature_require_at_destination_to_all_pages` === this.state.supervisorSignatureDestinationId ? true : false}
													onChange={e => { this.onChangeSupervisorSignatureDestination(e, i) }}
												>Apply this signature at destination to all pages of the inventory</Checkbox>)}
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(`stages[${i}].why_supervisor_signature_require_note`,
													{
														initialValue: shipmentTypeData && el.why_supervisor_signature_require_note
													}
												)(< TextArea rows={5} placeholder='Carrier Signature Note' maxLength={1000} />)}
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(`stages[${i}].customer_signature_require`, {
													valuePropName: "checked",
													initialValue: shipmentTypeData && el.customer_signature_require,
												})(<Checkbox>Customer Signature Required</Checkbox>)}
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(
													`stages[${i}].customer_signature_require_at_origin_to_all_pages`,
													{
														valuePropName: "checked",
														initialValue: `stages[${i}].customer_signature_require_at_origin_to_all_pages` === this.state.customerSignatureOriginId ? true : false,
													}
												)(<Checkbox
													name="checkCustomerSignatureAtOrigin"
													checked={`stages[${i}].customer_signature_require_at_origin_to_all_pages` === this.state.customerSignatureOriginId ? true : false}
													onChange={e => { this.onChangeCustomerSignatureOrigin(e, i) }}
												>Apply this signature at origin to all pages of the inventory</Checkbox>)}
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(
													`stages[${i}].customer_signature_require_at_destination_to_all_pages`,
													{
														valuePropName: "checked",
														initialValue: `stages[${i}].customer_signature_require_at_destination_to_all_pages` === this.state.customerSignatureDestinationId ? true : false,
													}
												)(<Checkbox
													name="checkCustomerSignatureAtDestination"
													checked={`stages[${i}].customer_signature_require_at_destination_to_all_pages` === this.state.customerSignatureDestinationId ? true : false}
													onChange={e => { this.onChangeCustomerSignatureDestination(e, i) }}
												>Apply this signature at destination to all pages of the inventory</Checkbox>)}
											</Form.Item>

											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(`stages[${i}].why_customer_signature_require_note`,
													{
														initialValue: shipmentTypeData && el.why_customer_signature_require_note
													}
												)(< TextArea rows={5} placeholder='Customer Signature Note' maxLength={1000} />)}
											</Form.Item>
											{/* {i > 0 ? (
                        <Form.Item {...tailFormItemLayout}>
                          <Button
                            type="danger"
                            onClick={() => this.removeStageField(i)}
                          >
                            - Remove
                          </Button>
                        </Form.Item>
                      ) : (
                        ""
                      )} */}
										</div>
									))}
									{/* <Form.Item {...tailFormItemLayout}>
                    <Button onClick={this.addOptionField}>+ Add More</Button>
                  </Form.Item> */}
								</div>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.changeModelSupervisorSignatureAtOrigin}
						onOk={this.handleOkSupervisorSignatureAtOrigin}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						onCancel={this.handleCancelSupervisorSignatureAtOrigin}>
						<p>A Carrier Signature has already been selected to apply at Origin, would you like to use this signature instead?</p>
					</Modal>
				}

				{
					<Modal
						title='Are You Sure?'
						visible={this.state.changeModelSupervisorSignatureAtDestination}
						onOk={this.handleOkSupervisorSignatureAtDestination}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						onCancel={this.handleCancelSupervisorSignatureAtDestination}>
						<p>A Carrier Signature has already been selected to apply at Destination, would you like to use this signature instead?</p>
					</Modal>
				}

				{
					<Modal
						title='Are You Sure?'
						visible={this.state.changeModelCustomerSignatureAtOrigin}
						onOk={this.handleOkCustomerSignatureAtOrigin}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						onCancel={this.handleCancelCustomerSignatureAtOrigin}>
						<p>A Customer Signature has already been selected to apply at Origin, would you like to use this signature instead?</p>
					</Modal>
				}

				{
					<Modal
						title='Are You Sure?'
						visible={this.state.changeModelCustomerSignatureAtDestination}
						onOk={this.handleOkCustomerSignatureAtDestination}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						onCancel={this.handleCancelCustomerSignatureAtDestination}>
						<p>A Customer Signature has already been selected to apply at Destination, would you like to use this signature instead?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditShipmentType));
