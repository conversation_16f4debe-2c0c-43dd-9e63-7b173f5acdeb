import { But<PERSON>, Col, Icon, Input, message, Modal, Row, Spin, Table, Tooltip, Checkbox } from "antd";
import React from "react";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import queryString from 'query-string';

const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let item_suggestion_id;

export default class roomListManagement extends React.Component {
	state = {
		apiParam: {},
		roomList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		adminId: "",
		companyId: "",
		staffId: "",
		batchAction: [],
		selectedRowKeys: [],
		isCheckedAllQrCodes: false,
		filterStatus: "active"
	};
	componentDidMount() {
		const queryParams = queryString.parse(window.location.search);
		let params = {
			search: "",
			order_by_fields: "",
			page_no: parseInt(queryParams.page) || "1",
			order_sequence: "",
			page_size: 25,
			filter: "active",
		};
		this.setState({ apiParam: params }, () => this.fetchItemList());

		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});
	}
	fetchItemList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		const {
			search,
			page_no,
			order_sequence,
			page_size,
			order_by_fields,
			filter,
		} = this.state.apiParam;
		this.setState({ pageloading: true });
		API.get(
			`api/admin/item_suggestion/list?order_by_fields=${order_by_fields}&order_sequence=${order_sequence}&page_no=${page_no}&search=${search}&page_size=${page_size}&filter=${filter}`
		)
			.then((response) => {
				const pagination = { ...this.state.pagination };
				if (response) {
					if (response.status === 1) {
						pagination.total = response.data.count;

						this.setState({
							roomList: response.data.rows,
							pagination,
							pageloading: false,
						});

					} else {
						this.setState({
							usersList: response.data,
							pagination,
							pageloading: false,
						});
						// message.error(res.message)
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	};
	handleChange = (pagination, filters, sorter) => {
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		const queryParams = queryString.parse(window.location.search);
		queryParams.page = pagination.current;
		const newUrl = `${window.location.pathname}?${queryString.stringify(queryParams)}`;
		window.history.pushState(null, null, newUrl);
		let params = {
			...this.state.apiParam,
			page_no: pagination.current,
			order_by_fields: sorter.column && sorter.field ? sorter.field : "",
			order_sequence: sorter.order === "ascend" ? "ASC" : "DESC",
			page_size: pagination.pageSize,
			filter: filters.status && filters.status[0] ? filters.status[0] : "active",
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
				filterStatus: filters.status && filters.status[0] ? filters.status[0] : "active",
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchItemList();
			}
		);
		scrollTo();
	};
	addRoom() {
		this.props.history.push(`${this.props.match.url}/add`);
	}
	delete(id) {
		this.setState({ deleteModal: true });
		item_suggestion_id = id;
	}

	batchDelete() {
		let params = {
			itemList: this.state.batchAction
		};
		const data = [];
		data["data"] = params;
		this.setState({ confirmLoading: true });
		API.post("api/admin/item/batch-delete-item-list", data)
			.then((response) => {
				if (response) {
					this.fetchItemList();
					this.setState({
						confirmLoading: false,
						batchAction: [],
						selectedRowKeys: []
					});
					message.success(response.message);
				}
				else {
					this.setState({
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
	}

	batchStatusChange(flag) {
		let params = {
			itemList: this.state.batchAction,
			isActiveFlag: flag
		};
		const data = [];
		data["data"] = params;
		this.setState({ confirmLoading: true });
		API.post("api/admin/item/batch-item-list-status-change", data)
			.then((response) => {
				if (response) {
					this.fetchItemList();
					this.setState({
						confirmLoading: false,
						batchAction: [],
						selectedRowKeys: []
					});
					message.success(response.message);
				}
				else {
					this.setState({
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
	}

	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}

	handleCancel = () => {
		this.setState({
			deleteModal: false,
			confirmLoading: false,
		});
	};
	handleOk = () => {
		const id = item_suggestion_id;
		this.setState({ confirmLoading: true });
		API.delete(`api/admin/item_suggestion/${id}`)
			.then((response) => {
				if (response) {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
						selectedRowKeys: [],
						batchAction: [],
					});
					this.fetchItemList();
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({
					confirmLoading: false,
					deleteModal: false,
				});
			});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchItemList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					page_no: 1,
				},
				search: e,
			},
			this.callback
		);
	};
	handleStatusChange = (id) => {
		const statusData = [];
		statusData["data"] = { item_id: id };
		API.post("api/admin/item_suggestion/change-item-status", statusData).then((response) => {
			if (response) {
				this.setState({
					selectedRowKeys: [],
					batchAction: [],
				});
				this.fetchItemList({
					page: this.state.pagination.current ? this.state.pagination.current : 1,
				});
				message.success(response.message);
			} else {
				message.error(response.message);
			}
		});
	};


	checkBoxHandler = (event, id) => {
		if (event.target.checked) {
			this.setState(prevState => ({
				batchAction: [...prevState.batchAction, id]
			}))
		}
		else {
			this.setState({
				batchAction: this.state.batchAction.filter((data) => {
					return data !== id
				})
			})
		}
	}


	onSelectChange = (newSelectedRowKeys, selectedRows) => {
		const selectedIds = selectedRows.map(row => row.item_suggestion_id);
		this.setState({
			selectedRowKeys: newSelectedRowKeys,
			batchAction: selectedIds,
		});
	};


	render() {
		const { selectedRowKeys } = this.state;
		const columns = [
			// {
			// 	title: "Check",
			// 	key: "check",
			// 	align: "center",
			// 	width: "10%",
			// 	render: (record, text) => {
			// 		return (
			// 			<div className='icons'>
			// 				<Checkbox onChange={e => { this.checkBoxHandler(e, record.item_suggestion_id) }} />
			// 			</div>
			// 		);
			// 	},
			// },
			{
				title: "Name",
				dataIndex: "name",
				key: "name",
				width: "30%",
				sorter: true,
			},
			{
				title: "Volume",
				dataIndex: "volume",
				key: "volume",
				align: "center",
				width: "18%",
				sorter: true,
			},
			{
				title: "Weight",
				dataIndex: "weight",
				key: "weight",
				align: "center",
				width: "18%",
				sorter: true,
			},
			{
				title: "Status",
				dataIndex: "status",
				key: "status",
				width: "10%",
				align: "center",
				filterMultiple: false,
				filtered: true,
				defaultFilteredValue: ["active"],
				filters: [
					{ text: "Active", value: "active" },
					{ text: "Inactive", value: "inactive" },
				],
				render: (text, record) => {
					return (
						<div>
							<button
								className={record.status === "active" ? "statusButtonActive" : "statusButtoninactive"}
								onClick={() => this.handleStatusChange(record.item_suggestion_id)}>
								<Icon type='swap' />
								{record.status === "active" ? " Active" : " Inactive"}
							</button>
						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				align: "center",
				render: (record, text) => {
					return (
						<div className='icons'>
							<Tooltip title='Edit'>
								<Button
									type='primary'
									className='c-btn c-round c-warning'
									icon='edit'
									onClick={() => this.edit(text.item_suggestion_id)}></Button>
							</Tooltip>
							<Tooltip title='Delete'>
								<Button
									type='primary'
									className='c-btn c-round c-danger'
									icon='delete'
									onClick={() => this.delete(text.item_suggestion_id)}></Button>
							</Tooltip>

						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<Col sm={8}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' /> &emsp;Item List Management
							</h2>
						</Col>
						<Col sm={6}>
							{this.state.search ? (
								<p
									style={{
										margin: "5px",
										textAlign: "center",
										color: "#303030",
									}}>
									Matching Records: {this.state.pagination.total}
								</p>
							) : (
								""
							)}
						</Col>
						{this.state.batchAction.length > 1 ?
							<Col sm={10}>
								<Row>
									{this.state.filterStatus == "inactive" ?
										<Col sm={16}>
											<Button
												className='addButton'
												style={{ marginTop: "0" }}
												type='primary'
												onClick={() => this.batchStatusChange(true)}>
												Batch Active
											</Button>
										</Col>
										: ""}

									{this.state.filterStatus == "active" ?
										<Col sm={16}>
											<Button
												className='addButton'
												style={{ marginTop: "0" }}
												type='primary'
												onClick={() => this.batchStatusChange(false)}>
												Batch Inactive
											</Button>
										</Col>
										: ""}

									<Col sm={8}>
										<Button
											className='addButton'
											style={{ marginTop: "0" }}
											type='danger'
											onClick={() => this.batchDelete()}>
											Batch Delete
										</Button>
									</Col>

								</Row>
							</Col>
							:

							<Col sm={10}>
								<Row>
									<Col sm={16} style={{ textAlign: "right" }}>
										<Search
											placeholder='Search item name'
											onChange={(e) => this.handleSearch(e.target.value)}
											value={this.state.search}
											style={{ width: 200 }}
										/>
									</Col>
									{
										<Col sm={8}>
											<Button
												className='addButton'
												style={{ marginTop: "0" }}
												type='primary'
												onClick={() => this.addRoom()}>
												+ Add Item
											</Button>
										</Col>
									}
								</Row>
							</Col>

						}
					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div style={{ marginTop: "-1.5rem" }}>
							<Table
								rowSelection={{
									selectedRowKeys,
									onChange: this.onSelectChange,
								}}
								bordered={true}
								columns={columns}
								pagination={{
									// pageSize: 10,
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 25,
									pageSizeOptions: ["25", "50"],
									current: this.state.apiParam.page_no
								}}
								dataSource={this.state.roomList}
								onChange={this.handleChange}
							/>
						</div>
					</LayoutContent>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to delete this item?</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}
