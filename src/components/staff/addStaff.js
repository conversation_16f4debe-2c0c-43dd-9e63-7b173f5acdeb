import React from "react";
import { connect } from "react-redux";

import { Button, Form, Icon, Input, Spin, message, Tag, Upload, Select, Checkbox } from "antd";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import Api from "../../api/api-handler";
import NumberFormat from "react-number-format";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';

const { TextArea } = Input;
const { Option } = Select;

const { changeCurrent } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;

class AddStaff extends React.Component {
	state = {
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		companyData: null,
		companyName: this.props.company,
		companyID: this.props.companyID,
		editShipmentTypeCheckbox: false,
		isAllowWorkerToViewItem: false
	};

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};

	handleUpload = ({ fileList }) => {
		this.setState({ fileList });
	};

	componentDidMount() {
		this.setState({ contentloader: true, loading: true });
		const data = [];
		API.post("api/admin/staff/view-company-list", data)
			.then((response) => {
				if (response) {
					this.setState({
						companyData: response.data && response.data,
						contentloader: false,
						loading: false
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false, loading: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false });
			});
	}

	handleSubmit = (e) => {
		e.preventDefault();
		let formData = new FormData();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];
				const tag = [];
				if (values.tags) {
					values.tags.forEach((item) => {
						tag.push(item.key);
					});
				}
				formData.append("company_id", (values.company.key !== undefined) ? values.company.key : "");
				formData.append("first_name", (values.first_name !== undefined) ? values.first_name : "");
				formData.append("last_name", (values.last_name !== undefined) ? values.last_name : "");
				formData.append("phone", (values.phone !== undefined) ? values.phone : "");
				formData.append("country_code", (values.country_code !== undefined) ? values.country_code : "");
				formData.append("email", (values.email !== undefined) ? values.email : "");
				formData.append("roles", (values.roles !== undefined) ? values.roles : "");
				formData.append("notes", (values.notes !== undefined) ? values.notes : "");
				formData.append("is_admin_can_edit_shipment_type", (values.roles == "ADMIN") ? values.is_admin_can_edit_shipment_type : false);
				formData.append("is_allow_view_item", (values.roles == "ADMIN") ? values.is_allow_view_item : false);
				formData.append(
					"photo",
					values.profile && values.profile !== "" && this.state.fileList.length
						? this.state.fileList[0].originFileObj
						: ""
				);
				formData.append("password", values.first_name.replace(/\s/g, "") + "@123");
				formData.append("tag", JSON.stringify(tag));
				this.setState({ loading: true, contentloader: true });

				data["data"] = formData;
				API.post("api/admin/staff/add", data)
					.then((response) => {
						if (response.status === 1) {
							this.setState({ loading: false, contentloader: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false, contentloader: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false, contentloader: false });
					});
			}
		});
	};

	normFile = (e) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e && e.fileList;
	};

	OnChangeSelectUser = (value, option) => {
		if (value == "ADMIN") {
			this.setState({
				isAllowWorkerToViewItem: false,
				editShipmentTypeCheckbox: true
			})
		}
		else {
			this.setState({
				isAllowWorkerToViewItem: true,
				editShipmentTypeCheckbox: false
			})
		}
	}
	render() {
		const { getFieldDecorator } = this.props.form;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		const countryCodeSelector = getFieldDecorator("country_code", {
			initialValue: "1",
		})(
			<Select disabled style={{ width: 70 }}>
				<Option value='1'>+1</Option>
			</Select>
		);
		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add User
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent
					style={{
						margin: "0 20px",
						height: "93%",
						overflowY: "auto",
					}}>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Company'>
									{getFieldDecorator("company", {
										initialValue: this.state.companyName
											? {
												key: this.state.companyID,
												label: this.state.companyName,
											}
											: [],
										rules: [
											{
												required: true,
												message: "Please select company",
											},
										],
									})(
										<Select
											size={"default"}
											labelInValue
											disabled={this.state.companyName}
											placeholder='Select Company'
											style={{ width: "100%" }}>
											{!this.state.companyName && this.state.companyData
												? this.state.companyData.map((e) => (
													<Option key={e.company_id} value={e.company_id}>
														{e.company_name}
													</Option>
												))
												:
												""}
										</Select>
									)}
								</Form.Item>

								<Form.Item label='Role'>
									{getFieldDecorator("roles", {
										rules: [{ required: true, message: "Please select your role!" }],
									})(
										<Select placeholder='Select role' onChange={this.OnChangeSelectUser}>
											<Option value='ADMIN'>Admin</Option>
											<Option value='WORKER'>Worker</Option>
										</Select>
									)}
								</Form.Item>

								{this.state.editShipmentTypeCheckbox ?
									<Form.Item label='Edit Access for Shipment Type'>
										{getFieldDecorator(`is_admin_can_edit_shipment_type`, {
											valuePropName: "checked",
										})(
											<Checkbox>Edit Access for Shipment Type</Checkbox>)}
									</Form.Item>
									: ""
								}

								{this.state.isAllowWorkerToViewItem ?
									<Form.Item label='Access for View Items'>
										{getFieldDecorator(`is_allow_view_item`, {
											valuePropName: "checked",
										})(
											<Checkbox>Access for View Items</Checkbox>)}
									</Form.Item>
									: ""
								}

								<Form.Item label='First Name'>
									{getFieldDecorator("first_name", {
										placeholder: "First Name",
										rules: [
											{
												required: true,
												message: "Please input first name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='First Name' />)}
								</Form.Item>

								<Form.Item label='Last Name'>
									{getFieldDecorator("last_name", {
										placeholder: "Last Name",
										rules: [
											{
												required: true,
												message: "Please input last name!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Last Name' />)}
								</Form.Item>

								<Form.Item label='Email'>
									{getFieldDecorator("email", {
										rules: [
											{
												type: "email",
												message: "The input is not valid Email!",
											},
											{
												required: true,
												message: "Please input your Email!",
											},
										],
									})(<Input placeholder='Email' />)}
								</Form.Item>

								<Form.Item label='Phone Number'>
									{getFieldDecorator("phone")(
										<Input
											style={{ width: "100%" }}
											placeholder='Phone Number'
											customInput={Input}
											maxLength={20}
										/>
									)}
								</Form.Item>

								<Form.Item label='User Notes'>
									{getFieldDecorator("notes", {})(<TextArea rows={4} placeholder='User Notes' />)}
								</Form.Item>

								<Form.Item label='Profile Image'>
									{getFieldDecorator("profile", {
										valuePropName: "fileList",
										getValueFromEvent: this.normFile,
									})(
										<Upload
											listType='picture-card'
											multiple={false}
											onPreview={this.handlePreview}
											onChange={this.handleUpload}
											showUploadList={{
												showRemoveIcon: true,
												showPreviewIcon: false,
											}}
											beforeUpload={() => false} // return false so that antd doesn't upload the picture right away
										>
											{this.state.fileList && this.state.fileList.length < 1 && (
												<Button>
													<Icon type='upload' /> Click to Upload
												</Button>
											)}
										</Upload>
									)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Add User
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

const mapStateToProps = (state) => {
	return {
		company: state.Auth.company,
		companyID: Number(state.Auth.companyID),
	};
};

export default Form.create()(connect(mapStateToProps, { changeCurrent })(AddStaff));
