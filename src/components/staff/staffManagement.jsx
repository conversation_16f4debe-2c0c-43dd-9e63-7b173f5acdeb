import {
	But<PERSON>,
	Col,
	Dropdown,
	Icon,
	Input,
	Menu,
	message,
	Modal,
	Row,
	Spin,
	Table,
	Tooltip,
} from "antd";
import React from "react";
import ModalImage from "react-modal-image";
import NumberFormat from "react-number-format";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import user from "../../static/images/user1.png";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import queryString from 'query-string';
import { CSVLink } from "react-csv";

const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let staffId;

export default class staffManagement extends React.Component {
	state = {
		apiParam: {},
		staffList: [],
		pagination: {},
		countAdmin: [],
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		deleteConfirmModal: false,
		statusModal: false,
		loading: false,
		confirmLoading: false,
		isAdmin: false,
		staffId: "",
		apiKeyStatus: false,
		lastAdminArray: [],
		exportCSVUsers: [],

	};
	componentDidMount() {
		const queryParams = queryString.parse(window.location.search);
		let params = {
			search: "",
			orderBy: "created_at",
			pageNo: parseInt(queryParams.page) || "1",
			orderSequence: "DESC",
			pageSize: 25,
			filter: "active",
		};
		const userType = localStorage.getItem("userType") === "1" ? true : false;
		const staffId = localStorage.getItem("staffId");

		this.setState({ apiParam: params, isAdmin: userType, staffId: staffId }, () =>
			this.fetchStaffList(),
			this.handleExport()
		);

		API.get(`api/open-api/apiKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						if (response.data.isEnable === 1) {
							this.setState({
								apiKeyStatus: true,
							});
						}
						else {
							this.setState({
								apiKeyStatus: false,
							});
						}

					} else {
						console.log(response.message);
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

	}
	fetchStaffList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		this.setState({ pageloading: true });
		API.post("api/admin/staff/list", data)
			.then((response) => {
				const pagination = { ...this.state.pagination };
				if (response) {

					if (response.status === 1) {
						pagination.total = response.data.staffList.count;
						response.data.staffList.rows.map((data, i) => {
							if (data.roles === "ADMIN")
								this.setState({
									lastAdminArray: [...this.state.lastAdminArray, data.staff_id]
								});
						})
						this.setState({
							staffList: response.data.staffList.rows,
							pagination,
							pageloading: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							pagination,
							pageloading: false,
						});
						// message.error(res.message)
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	};

	handleExport = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		this.setState({ pageloading: true });
		API.post("api/admin/staff/listAllUsers", data)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							exportCSVUsers: response.data.staffList,
							pageloading: false
						});
					} else {
						this.setState({
							exportCSVUsers: [],
							pageloading: false
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	};



	handleChange = (pagination, filters, sorter) => {
		scrollTo();
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		const queryParams = queryString.parse(window.location.search);
		queryParams.page = pagination.current;
		const newUrl = `${window.location.pathname}?${queryString.stringify(queryParams)}`;
		window.history.pushState(null, null, newUrl);
		let params = {
			...this.state.apiParam,
			pageNo: pagination.current,
			orderBy:
				sorter.field === "createdAt"
					? "created_at"
					: sorter.field === "company.company_name"
						? "company_name"
						: sorter.field,
			orderTable: sorter.field === "company.company_name" ? "staff_company" : "staff",
			orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
			pageSize: pagination.pageSize,
			filter: filters.status && filters.status[0] ? filters.status[0] : "active",
		};
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchStaffList();
			}
		);
	};

	addStaff() {
		this.props.history.push(`${this.props.match.url}/add`);
	}

	delete(id) {
		this.setState({ deleteModal: true });
		staffId = id;
	}

	deleteConfirm(id) {
		this.setState({ deleteConfirmModal: true });
		staffId = id;
	}




	ValidToDelete(staffId) {
		const id = staffId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { staff_id: id };
		API.post("api/admin/staff/ValidToDelete", deleteData)
			.then((response) => {
				console.log("response", response)
				if (response.status == 1) {
					this.setState({
						confirmLoading: false,
					});
					this.deleteConfirm(staffId);
				} else {
					this.setState({
						confirmLoading: false,
					});
					this.delete(staffId);
				}
			})
			.catch((error) => {
				this.setState({
					confirmLoading: false,
					deleteModal: false,
				});
			});
	};

	status(id) {
		this.setState({ statusModal: true });
		staffId = id;
	}
	edit(id, isSingleAdmin, chackAdminLast) {
		this.props.history.push({
			pathname: `${this.props.match.url}/edit/${id}`,
			state: { isSingleAdmin: isSingleAdmin, chackAdminLast: chackAdminLast },
		});
	}
	handleCancel = () => {
		this.setState({
			deleteModal: false,
			statusModal: false,
			confirmLoading: false,
		});
	};
	handleCancelConfirm = () => {
		this.setState({
			deleteConfirmModal: false,
			statusModal: false,
			confirmLoading: false,
		});
	}
	menus = (text) => {
		let chackAdminLast = text.staff_id !== this.state.lastAdminArray[0] ? false : true
		return (
			<Menu>

				<Menu.Item key='1'>
					<Tooltip title='Edit'>
						<Button
							type='primary'
							className='c-btn c-round c-warning'
							icon='edit'
							onClick={() => this.edit(text.staff_id, text.isSingleAdmin, chackAdminLast)}></Button>
					</Tooltip>
				</Menu.Item>

				{(text.isSingleAdmin === "false" || text.roles === "WORKER") &&
					text.staff_id !== parseInt(this.state.staffId) ? (

					<Menu.Item key='2'>

						<Tooltip title='Delete'>
							<Button
								type='primary'
								className='c-btn c-round c-danger'
								icon='delete'
								onClick={() => this.ValidToDelete(text.staff_id)}></Button>
						</Tooltip>
					</Menu.Item>

				) : (
					""
				)}
			</Menu>
		);
	};
	handleOk = () => {
		const id = staffId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { staff_id: id };
		API.post("api/admin/staff/delete", deleteData)
			.then((response) => {
				if (response) {
					this.fetchStaffList();
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({
					confirmLoading: false,
					deleteModal: false,
				});
			});
	};

	handleOKdeleteConfirmModal = () => {
		const id = staffId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { staff_id: id };
		API.post("api/admin/staff/delete", deleteData)
			.then((response) => {
				if (response) {
					this.fetchStaffList();
					this.setState({
						deleteModal: false,
						deleteConfirmModal: false,
						confirmLoading: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						deleteConfirmModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({
					confirmLoading: false,
					deleteModal: false,
					deleteConfirmModal: false,
				});
			});
	};

	handleStatusChange = () => {
		const id = staffId;
		this.setState({ confirmLoading: true });
		const statusData = [];
		statusData["data"] = { staff_id: id };
		API.post("api/admin/staff/change-staff-status", statusData)
			.then((response) => {
				if (response) {
					this.setState({
						statusModal: false,
						confirmLoading: false,
					});
					message.success(response.message);
					this.fetchStaffList({
						page: this.state.pagination.current ? this.state.pagination.current : 1,
					});
				} else {
					this.setState({
						statusModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch(() => {
				this.setState({
					statusModal: false,
					confirmLoading: false,
				});
			});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchStaffList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
					pageNo: 1,
				},
				search: e,
			},
			this.callback
		);
	};
	render() {
		const { apiKeyStatus } = this.state;
		const headersUserCSV = [
			{ label: "FirstName", key: "first_name" },
			{ label: "LastName", key: "last_name" },
			{ label: "Email", key: "email" },
			{ label: "Company", key: "staff_company.company_name" },
			{ label: "CreatedDate", key: "created_at" },
			{ label: "Roles", key: "roles" },
		];

		const columns = [
			{
				title: "Name",
				dataIndex: "first_name",
				key: "first_name",
				sorter: true,
				minWidth: "14%",
				align: "left",
				render: (record, text) => {

					return (
						<div
							style={{
								display: "flex",
								alignItems: "center",
							}}>
							<div
								style={{
									width: "50px",
									height: "50px",
									marginTop: "10px"
								}}>
								<ModalImage
									className='imageModal'
									small={
										text.staff_profile && text.staff_profile.trim() !== "" ? `${text.staff_profile}` : user
									}
									large={
										text.staff_profile && text.staff_profile.trim() !== "" ? `${text.staff_profile}` : user
									}
									hideDownload={true}
									hideZoom={true}
								/>
							</div>
							<div
								style={{
									flex: "1",
								}}>
								{text.first_name + " " + text.last_name}
							</div>
						</div>
					);
				},
			},
			{
				title: "Company Name",
				dataIndex: "company.company_name",
				key: "company_name",
				sorter: true,
				minWidth: "16%",
				align: "center",
				render: (record, text) => {
					return text.staff_company.company_name;
				},
			},
			{
				title: "Phone",
				dataIndex: "phone",
				key: "phone",
				minWidth: "20%",
				align: "left",
				// return record === "undefined" || !record ? "" : record;
				render: (record, text) => {
					return record === "undefined" || record === "" ? "" : record
				},
			},
			{
				title: "Role",
				dataIndex: "roles",
				key: "roles",
				minWidth: "20%",
				align: "center",
				render: (record, text) => {
					return record;
				},
			},
			{
				title: "Email",
				dataIndex: "email",
				key: "email",
				sorter: true,
				minWidth: "20%",
				align: "left",
			},
			{
				title: (data) => (
					<div>
						<p style={{ margin: "0" }}>Status</p>
						<p style={{ margin: "0", textTransform: "capitalize" }}>
							({data.filters && data.filters.status ? data.filters.status[0] : ""})
						</p>
					</div>
				),
				dataIndex: "status",
				key: "status",
				width: "10%",
				align: "center",
				filterMultiple: false,
				filtered: true,
				defaultFilteredValue: ["active"],
				filters: [
					{ text: "Active", value: "active" },
					{ text: "Inactive", value: "inactive" },
				],
				render: (text, record) => {
					return (
						<div>
							{(record.roles === "WORKER") &&
								record.staff_id !== parseInt(this.state.staffId) ?
								(
									<button
										className={record.status === "active" ? "statusButtonActive" : "statusButtoninactive"}
										onClick={() => this.status(record.staff_id)}>
										<Icon type='swap' />
										{record.status === "active" ? " Active" : " Inactive"}
									</button>
								) : (
									<strong>
										{record.staff_id === parseInt(this.state.staffId)
											? "Can't change role & status of loggged in user"
											:
											(
												<strong>
													{record.staff_id !== this.state.lastAdminArray[0]
														? (
															<button
																className={record.status === "active" ? "statusButtonActive" : "statusButtoninactive"}
																onClick={() => this.status(record.staff_id)}>
																<Icon type='swap' />
																{record.status === "active" ? " Active" : " Inactive"}
															</button>
														)
														:
														"Can't edit role & status of only admin"
													}
												</strong>
											)
										}
									</strong>
								)}
						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				minWidth: "10%",
				align: "center",
				render: (record, text) => {
					return (
						<div className='icons' style={{ textAlign: "center" }}>
							<Dropdown placement='bottomCenter' overlay={this.menus.bind(null, text)}>
								<Icon
									type='setting'
									theme='twoTone'
									twoToneColor='#3fa146'
									onClick={() => this.menus.bind(null, text)}
								/>
							</Dropdown>
						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<Col sm={8}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' />
								&emsp;Users Management
							</h2>

						</Col>
						<Col sm={6}>
							{this.state.search ? (
								<p
									style={{
										margin: "5px",
										textAlign: "center",
										color: "#303030",
									}}>
									Matching Records {this.state.pagination.total}
								</p>
							) : (
								""
							)}
						</Col>
						<Col sm={10}>
							<Row>
								<Col
									sm={this.state.isAdmin ? 8 : 16}
									style={{
										textAlign: "right",
									}}>
									<Search
										placeholder='Search Users'
										onChange={(e) => this.handleSearch(e.target.value)} handleExport
										value={this.state.search}
										style={{ width: 200 }}
									/>
								</Col>
								{this.state.isAdmin && (
									<Col sm={8}>
										<CSVLink
											data={this.state.exportCSVUsers}
											headers={headersUserCSV}
											filename={"Users.csv"}
											target="_blank"
											style={{ marginLeft: "50px" }}
										>
											<Button type='primary' icon='printer'>
												Users CSV Export
											</Button>
										</CSVLink>
									</Col>)}
								<Col sm={8}>
									<Button
										className='addButton'
										style={{
											marginTop: "0",
										}}
										type='primary'
										onClick={() => this.addStaff()}>
										+ Add User
									</Button>
								</Col>

							</Row>
						</Col>
					</Row >
				</div >
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div
							style={{
								marginTop: "-1.5rem",
								overflowX: "auto",
							}}>
							<Table
								bordered={true}
								columns={columns}
								pagination={{
									// pageSize: 10,
									total: this.state.pagination.total,
									showSizeChanger: true,
									defaultPageSize: 25,
									pageSizeOptions: ["25", "50"],
									current: this.state.apiParam.pageNo

								}}
								dataSource={this.state.staffList}
								onChange={this.handleChange}
							// scroll={{ x: 1500 }}
							/>
						</div>
					</LayoutContent>
				</Spin>

				<Modal
					title='Are You Sure?'
					visible={this.state.deleteModal || this.state.statusModal}
					onOk={this.state.deleteModal ? this.handleOk : this.handleStatusChange}
					okText='Yes'
					cancelText='No'
					centered
					maskClosable={false}
					confirmLoading={this.state.confirmLoading}
					onCancel={this.handleCancel}>
					<p>
						{this.state.deleteModal
							? "Shipments assigned to this User, Are you sure you want to delete User ?"
							: "Are you sure you want to change status of this User?"}
					</p>
				</Modal>

				<Modal
					title='Are You Sure?'
					visible={this.state.deleteConfirmModal}
					onOk={this.handleOKdeleteConfirmModal}
					okText='Yes'
					cancelText='No'
					centered
					maskClosable={false}
					confirmLoading={this.state.confirmLoading}
					onCancel={this.handleCancelConfirm}>
					<p>
						Are you sure you want to delete this User ?.
					</p>
				</Modal>
			</LayoutContentWrapper >
		);
	}
}
