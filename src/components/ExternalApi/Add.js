import { Button, Form, Icon, Input, message, Select, Spin } from "antd";
import React, { useState } from "react";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";

const { changeCurrent } = appActions;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const { Option } = Select;
const API = new Api({});

const Add = ({ history, form }) => {
	const { getFieldDecorator } = form;
	// const access = [
	// 	{
	// 		id: 1,
	// 		type: "CMS",
	// 	},
	// 	{
	// 		id: 2,
	// 		type: "API",
	// 	},
	// ];
	const status = [
		{
			id: 1,
			type: "Active",
		},
		{
			id: 2,
			type: "Inactive",
		},
	];
	const [loading, setLoading] = useState(false);
	const handleSubmit = (e) => {
		e.preventDefault();
		const data = [];
		let formData = new FormData();

		setLoading(true);
		form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				formData.append("email", values.email);
				formData.append("password", values.password);
				formData.append("from", values.from);
				formData.append("status", values.status.label);
				formData.append("type", "API");
				data["data"] = formData;

				API.post("api/external_api/add_user", data)
					.then((response) => {
						if (response) {
							setLoading(false);
							if (response.status === 1) {
								message.success(response.message);
								history.push("/external-api-user");
							} else {
								message.error(response.message);
							}
						}
					})
					.catch((e) => {
						setLoading(false);
					});
			} else {
				//console.log(err);
			}
		});
	};
	const compareToFirstPassword = (rule, value, callback) => {
		if (value && value !== form.getFieldValue("password")) {
			callback("Two passwords that you enter doesn't match!");
		} else {
			callback();
		}
	};
	const formItemLayout = {
		labelCol: {
			xs: {
				span: 24,
			},
			sm: {
				span: 5,
			},
		},
		wrapperCol: {
			xs: { span: 24 },
			sm: {
				span: 12,
				offset: 1,
			},
			md: {
				span: 12,
				offset: 1,
			},
			lg: {
				span: 12,
				offset: 1,
			},
			xl: {
				span: 10,
				offset: 1,
			},
		},
	};
	const tailFormItemLayout = {
		wrapperCol: {
			xs: {
				span: 24,
				offset: 6,
			},
			sm: {
				span: 16,
				offset: 6,
			},
			xl: {
				offset: 6,
			},
		},
	};
	return (
		<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
			<div className='add_header'>
				<h2 style={{ marginBottom: "0" }}>
					<i class='fas fa-plus'></i>&emsp;Add Api User
				</h2>
				<button className='backButton' onClick={() => history.goBack()}>
					<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
				</button>
			</div>
			<LayoutContent
				style={{
					margin: "0 20px",
					height: "93%",
					overflowY: "auto",
				}}>
				<div>
					<Spin spinning={false} indicator={antIcon}>
						<Form {...formItemLayout} onSubmit={handleSubmit}>
							<Form.Item label='Email'>
								{getFieldDecorator("email", {
									rules: [
										{
											required: true,
											message: "Please enter your Email!",
										},
										{
											pattern: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
											message: "Please valid email address!",
										},
									],
								})(<Input placeholder='<EMAIL>' />)}
							</Form.Item>

							<Form.Item label='Password'>
								{getFieldDecorator("password", {
									rules: [
										{
											required: true,
											message: "Please enter your password!",
										},
									],
								})(<Input.Password placeholder='*************' />)}
							</Form.Item>
							<Form.Item label='Confirm Password'>
								{getFieldDecorator("confirmPassword", {
									rules: [
										{
											required: true,
											message: "Please confirm your password!",
										},
										{
											validator: compareToFirstPassword,
										},
									],
								})(<Input.Password placeholder='*************' />)}
							</Form.Item>
							<Form.Item label='From'>
								{getFieldDecorator("from", {
									rules: [
										{
											required: true,
											message: "Please give name of access cms!",
										},
									],
								})(<Input placeholder='movegistics' />)}
							</Form.Item>
							<Form.Item label='Status'>
								{getFieldDecorator("status", {
									rules: [
										{
											required: true,
											message: "Please add status of api access!",
										},
									],
								})(
									<Select labelInValue style={{ width: "100%" }} placeholder='Please give api access status'>
										{status.map((item) => (
											<Option value={item.id} key={item.id}>
												{item.type}
											</Option>
										))}
									</Select>
								)}
							</Form.Item>
							{/* <Form.Item label='Access Type'>
								{getFieldDecorator("access", {
									rules: [
										{
											required: true,
											message: "Please select access type!",
										},
									],
								})(
									<Select
										labelInValue
										style={{ width: "100%" }}
										placeholder='Please select access type'>
										{access.map((item) => (
											<Option value={item.id} key={item.id}>
												{item.type}
											</Option>
										))}
									</Select>
								)}
							</Form.Item> */}
							<Form.Item {...tailFormItemLayout}>
								<Button className='submitButton' loading={loading} type='primary' htmlType='submit'>
									Add Api User
								</Button>
							</Form.Item>
						</Form>
					</Spin>
				</div>
			</LayoutContent>
		</LayoutContentWrapper>
	);
};

export default Form.create()(connect(null, { changeCurrent })(Add));
