import { Button, Col, Form, Icon, Input, message, Modal, Row, Spin, Table } from "antd";
import moment from "moment";
import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { changeCurrent } = appActions;

const List = ({ history, match, form }) => {
	const { getFieldDecorator } = form;
	const [password, setPassword] = useState("");
	const [email, setEmail] = useState("");
	const [loading, setLoading] = useState(true);
	const [spinning, setSpinning] = useState(false);
	const [modalOpen, setModalOpen] = useState(false);
	const [reload, setReload] = useState(false);
	const [data, setData] = useState([]);
	useEffect(() => {
		API.get("api/external_api/list")
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						setData(response.data);
						setLoading(false);
					} else {
						setLoading(false);
						message.error(response.message);
					}
				} else {
					setLoading(false);
				}
			})
			.catch((e) => {
				setLoading(false);
			});
	}, [loading]);
	useEffect(() => {
		if (reload) {
			handleReloadToken();
		}
	}, [reload]);
	const handleReloadToken = () => {
		setSpinning(true);
		const data = [];
		let formData = new FormData();
		formData.append("email", email);
		formData.append("password", password);
		data["data"] = formData;
		if (reload) {
			API.post("api/external_api/reload_token", data)
				.then((response) => {
					setReload(false);
					setSpinning(false);
					if (response) {
						if (response.status === 1) {
							message.success(response.message);
							setLoading(true);
						} else {
							message.error(response.message);
						}
					}
				})
				.catch((e) => {
					setSpinning(false);
					setReload(false);
				});
		} else {
			setReload(false);
			// setSpinning(false);
			// setModalOpen(false);
		}
	};
	const handleSubmit = (e) => {
		e.preventDefault();
		form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				setPassword(values.password);
				setModalOpen(false);
				setReload(true);
			} else {
				setModalOpen(true);
			}
		});
	};
	const handleCancel = () => {
		setModalOpen(false);
		setSpinning(false);
		message.warning("API token canceled!");
	};
	const columns = [
		{
			title: "Email",
			dataIndex: "email",
			key: "email",
			width: 200,
		},
		{
			title: "From",
			dataIndex: "from",
			key: "from",
			align: "center",
			width: 100,
		},
		{
			title: "Access",
			dataIndex: "type",
			key: "type",
			align: "center",
			width: 100,
		},
		{
			title: "API Key",
			dataIndex: "apiKey",
			key: "apiKey",
			width: 250,
		},
		{
			title: "Status",
			dataIndex: "status",
			align: "center",
			key: "status",
			width: 100,
			render: (text, record) => (
				<div style={{ textTransform: "uppercase", fontWeight: "600" }}>
					<p style={text === "Active" ? { color: "green" } : { color: "red" }}>{text}</p>
				</div>
			),
		},
		{
			title: "Expire",
			key: "expire",
			align: "center",
			width: 150,
			render: (text, record) => {
				// return DateFns.format(record.expire, "MM/DD/YYYY HH:MM");
				return moment(record.expire).format("MM/DD/YYYY hh:mm");
			},
		},
		{
			title: "Reload Token",
			key: "action",
			align: "center",
			width: 100,
			render: (text, record) => {
				return record.status === "Active" ? (
					<div
						onClick={() => {
							setSpinning(true);
							setEmail(record.email);
							setModalOpen(true);
							setPassword("");
						}}>
						<Icon
							type='reload'
							style={{ cursor: "pointer", fontSize: "20px" }}
							spin={record.email === email && spinning}
						/>
					</div>
				) : (
					""
				);
			},
		},
	];
	return (
		<LayoutContentWrapper>
			<div className='top_header' style={{ height: "100%" }}>
				<Row>
					<Col sm={12}>
						<h2 style={{ marginBottom: "10px" }}>
							<Icon type='user' /> &emsp;Api Users
						</h2>
					</Col>
					<Col sm={12}>
						<Row>
							<Col
								sm={16}
								style={{
									textAlign: "right",
								}}></Col>

							<Col sm={8}>
								{/* {data.length < 1 ? (
									<Button
										className='addButton'
										style={{
											marginTop: "0",
										}}
										type='primary'
										onClick={() => history.push(`${match.url}/add`)}>
										+ Add Api User
									</Button>
								) : (
									""
								)} */}
							</Col>
						</Row>
					</Col>
				</Row>
			</div>
			<Spin spinning={loading} indicator={antIcon}>
				<LayoutContent>
					<div style={{ marginTop: "-1.5rem", overflowX: "auto" }}>
						<Table
							bordered={true}
							columns={columns}
							dataSource={data}
							pagination={false}
						// onChange={this.handleChange}
						/>
					</div>
					{modalOpen && (
						<Modal
							title='Enter Password'
							visible={modalOpen}
							// onOk={handleOk}
							footer={null}
							onCancel={handleCancel}>
							<Form {...formItemLayout} onSubmit={handleSubmit}>
								<Form.Item label='Password'>
									{getFieldDecorator("password", {
										rules: [
											{
												required: true,
												message: "Please enter your password!",
											},
										],
									})(<Input.Password placeholder='*************' />)}
								</Form.Item>
								<Form.Item {...tailFormItemLayout}>
									<Button className='submitButton' type='primary' htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Modal>
					)}
				</LayoutContent>
			</Spin>
		</LayoutContentWrapper>
	);
};

export default Form.create()(connect(null, { changeCurrent })(List));
const formItemLayout = {
	labelCol: {
		xs: {
			span: 24,
		},
		sm: {
			span: 5,
		},
	},
	wrapperCol: {
		xs: { span: 24 },
		sm: {
			span: 12,
			offset: 1,
		},
		md: {
			span: 18,
			offset: 1,
		},
		lg: {
			span: 18,
			offset: 1,
		},
		xl: {
			span: 18,
			offset: 1,
		},
	},
};
const tailFormItemLayout = {
	wrapperCol: {
		xs: {
			span: 24,
			offset: 6,
		},
		sm: {
			span: 16,
			offset: 6,
		},
		xl: {
			offset: 6,
		},
	},
};
