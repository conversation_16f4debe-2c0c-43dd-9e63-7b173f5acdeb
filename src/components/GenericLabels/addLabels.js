import React from "react";
import { connect } from "react-redux";
import { Button, Form, Icon, InputNumber, message, Spin } from "antd";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { toast } from "react-toastify";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import Api from "../../api/api-handler";
import "./qrstyle.css";
const { changeCurrent, currentQrJob } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddQrCodeLabel extends React.Component {
	state = {
		loading: false,
		contentloader: true,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		notificationTime: 0,
		lastLabel: 0
	};
	componentDidMount() {
		API.get("api/admin/last/genericLabel")
			.then((response) => {
				if (response.status === 1) {
					this.setState({
						lastLabel: response.data,
						loading: false
					});
				} else {
					this.setState({ loading: false });
				}
			})
			.catch(() => {
				this.setState({ loading: false });
			});
		setTimeout(() => {
			this.setState({ loading: false });
		}, 120000);
	}

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {
		this.setState({ fileList });
	};
	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {
			let params = {
				type: 1,
				quantity: values.quantity,
				fromNumber: values.fromNumber
			};
			if (!err) {
				const data = [];
				data["data"] = params;
				this.setState({ loading: true });
				const resolveAfterPromise = new Promise((resolve, reject) => {
					API.post("api/admin/generic_label/generate", data)
						.then((response) => {
							if (response.status === 1) {
								this.setState({ loading: false });
								this.props.history.goBack();
								resolve();
							} else {
								this.setState({ loading: false });
								reject();
							}
						})
						.catch(() => {
							this.setState({ loading: false });
							reject();
						});
				});
				// toast.promise(resolveAfterPromise, {
				// 	pending: "Please wait, Your QR Codes are being created. Please avoid any qr related action like delete!",
				// 	success: "Your labels have been generated for company",
				// 	error: "Error while generating qr codes!",
				// });
			}
		});
	};

	normFile = (e) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e && e.fileList;
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add QR Code
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent style={{ margin: "0 20px", height: "93%", overflowY: "auto" }}>
					<div className='print-qr'>
						<Spin
							spinning={this.state.loading}
							indicator={antIcon}
							tip={
								<div>
									<p style={{ margin: "0" }}>
										Please wait, Your QR Codes are being created, this could take several minutes depending on
										the number of labels. You may continue with other work and you will be notified when your
										labels have been generated.
									</p>
								</div>
							}>
							{/* <Spin spinning={this.state.contentloader} indicator={antIcon}> */}
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Quantity'>
									{getFieldDecorator("quantity", {
										placeholder: "Quantity",
										rules: [

											{
												required: true,
												message: "Please input quantity!",
											},

											{
												min: 1,
												type: "number",
												message: "Quantity must be numeric & greater than 0!",
											},

											{
												max: 5000,
												type: "number",
												message: "Quantity must be numeric & less than or equal to 5000!"
											}
										],
									})(<InputNumber placeholder='Quantity' style={{ width: "100%" }} />)}
								</Form.Item>

								<Form.Item label='From sequence number'>
									{getFieldDecorator("fromNumber", {
										placeholder: "From sequence number",
										initialValue: this.state.lastLabel,
										rules: [
											{
												required: true,
												message: "Please input from sequence number!",
											},
										],
									})(<InputNumber placeholder='From sequence number' style={{ width: "100%" }} />)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Add Qr Code
									</Button>
								</Form.Item>
							</Form>
							{/* </Spin> */}
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

const mapStateToProps = (state) => {
	return { jobId: state.App.currentQrJob };
};

export default Form.create()(connect(mapStateToProps, { changeCurrent, currentQrJob })(AddQrCodeLabel));
