import { Button, Col, Icon, Input, Form, message, Select, Modal, Row, Spin, Table, Tooltip, Checkbox, notification } from "antd";
import React from "react";
import { saveAs } from "file-saver";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import user from "../../static/images/user1.png";
import appActions from "../../redux/app/actions";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import QRCode from "qrcode.react";
import { Helmet } from "react-helmet";
import ReactToPrint from "react-to-print";
import { ComponentToPrint } from "./ComponentToPrint";


const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let qrCodeId;

const { currentQrJob } = appActions;
const { Option } = Select;

class labelManagement extends React.Component {
	componentRef = null;
	state = {
		apiParam: {},
		labelImages: "",
		qrCodeList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		showModal: false,
		confirmLoading: false,
		items: [],
		QRModal: false,
		QRValue: "",
		currentQrJobId: null,
		companyCheckId: null,
		batchAction: [],
		batchActionImages: [],
		printerList: [],
		printerList2: [],
		printerName: "",
		previewButton: false,
		printButton: false,
		selectedRowKeys: [],
		isCheckedAllQrCodes: false,
	};

	componentDidMount() {
		let printers = window.dymo.label.framework.getPrinters()
		this.setState({
			companyCheckId: (localStorage.getItem("companyID") !== "" || localStorage.getItem("companyID") !== null || localStorage.getItem("companyID") !== undefined) ?
				localStorage.getItem("companyID")
				: null,
			printerList: printers
		})

		let params = {
			search: "",
			page_no: 1,
			page_size: 25,
		};
		this.setState({ apiParam: params, currentQrJobId: this.props.app.currentQrJob }, () =>
			this.fetchQrCodeList()
		);
	}

	fetchQrCodeList = () => {
		const id = this.props.match.params.id;
		const data = [];
		data["data"] = this.state.apiParam;
		const { search, page_no, page_size } = this.state.apiParam;
		this.setState({ pageloading: true });
		API.get(
			`api/admin/generic_label/list/${id}?page_no=${page_no}&search=${search}&page_size=${page_size}`,
			data
		)
			.then((response) => {
				const pagination = { ...this.state.pagination };
				if (response) {
					if (response.status === 1) {
						pagination.total = response.data.count;
						this.setState({
							qrCodeList: response.data.rows,
							pagination,
							pageloading: false,
						});
					} else {
						this.setState({
							qrCodeList: response.data.rows,
							pagination,
							pageloading: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	};

	onSelectChange = (newSelectedRowKeys, selectedRows) => {
		const selectedIds = selectedRows.map(row => row);

		this.setState({
			selectedRowKeys: newSelectedRowKeys,
			batchAction: selectedIds,
		});

		if (newSelectedRowKeys.length === this.state.qrCodeList.length) {
			this.setState({
				isCheckedAllQrCodes: true
			})
		}
		else {
			this.setState({
				isCheckedAllQrCodes: false
			})
		}
	};

	handleChange = (pagination, filters, sorter) => {
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		let params = {
			...this.state.apiParam,
			page_no: pagination.current,
			page_size: pagination.pageSize,
		};

		this.setState({
			pagination: pager,
			apiParam: params,
		});
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchQrCodeList();
			}
		);
		scrollTo();
	};

	addQrCode() {
		this.props.history.push({
			pathname: `${this.props.match.url}/add`,
		});
	}

	printSingalQrGeneric(id, qrId) {
		let params = {
			place: "Generic Label",
			title_flag: true,
			qr_code_label_flag: true,
			sequenced_label_flag: true,
			qrId: qrId,
			responseType: "blob",
		};

		const data = [];

		data["data"] = params;
		this.setState({ loading: true });
		API.post(`api/admin/generic_label/${this.state.companyCheckId}/print`, data)
			.then((response) => {
				const pdfBlob = new Blob([response], {
					type: "application/pdf",
				});
				saveAs(pdfBlob, "genericLabel.pdf");
				if (response) {
					this.setState({ loading: false });
					notification.success({
						message: "Qr Pdf",
						description: "Your Pdf for QR code labels is generated successfully.",
						duration: 4.5,
					});
				} else {
					this.setState({ loading: false });
					message.error("Error while creating pdf.");
				}
			})
			.catch((error) => {
				this.setState({ loading: false });
			});

	}

	printQrCode(id) {
		let params = {
			place: "Generic Label",
			title_flag: true,
			qr_code_label_flag: true,
			sequenced_label_flag: true,
			responseType: "blob",
		};

		const data = [];

		data["data"] = params;
		this.setState({ loading: true });
		API.post(`api/admin/generic_label/${this.state.companyCheckId}/print/${id}`, data)
			.then((response) => {
				const pdfBlob = new Blob([response], {
					type: "application/pdf",
				});
				saveAs(pdfBlob, "genericLabel.pdf");
				if (response) {
					this.setState({ loading: false });
					notification.success({
						message: "Qr Pdf",
						description: "Your Pdf for QR code labels is generated successfully.",
						duration: 4.5,
					});
				} else {
					this.setState({ loading: false });
					message.error("Error while creating pdf.");
				}
			})
			.catch((error) => {
				this.setState({ loading: false });
			});

	}
	delete(id) {
		this.setState({ deleteModal: true });
		qrCodeId = id;
	}

	handleQrCancel = () => {
		this.setState({
			QRModal: !this.state.QRModal,
		});
	};

	printDymoLabels = () => {
		if (this.state.isCheckedAllQrCodes) {
			const id = this.props.match.params.id;
			this.setState({ pageloading: true });
			API.get(
				`api/admin/generic_label/list/${id}?page_no=${1}&search=${""}&page_size=${this.state.pagination.total}`,
			)
				.then((response) => {
					if (response) {
						if (response.status === 1) {
							this.props.history.push({
								pathname: `${this.props.match.url}/dymo-print`,
								state: {
									name: response.data.rows
								},
							});
						} else {
							this.setState({
								qrCodeList: response.data.rows,
								pageloading: false,
							});

						}
					}
				})
				.catch((error) => {
					this.setState({ pageloading: false });
				});
		}
		else {
			this.props.history.push({
				pathname: `${this.props.match.url}/dymo-print`,
				state: {
					name: this.state.batchAction
				},
			});
		}
	};

	handleOk = () => {
		const id = qrCodeId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { qr_code_id: id };
		API.delete("api/admin/generic_label/" + id, deleteData)
			.then((response) => {
				if (response) {
					this.fetchQrCodeList();
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({
					confirmLoading: false,
					deleteModal: false,
				});
			});
	};

	handleCancel = () => {
		this.setState({
			deleteModal: false,
			confirmLoading: false,
		});
	};

	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchQrCodeList, 1000);
	};

	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e.target.value,
				},
				search: e.target.value,
			},
			this.callback
		);
	};

	setComponentRef = (ref) => {
		this.componentRef = ref;
	};

	reactToPrintContent = () => {
		return this.componentRef;
	};

	reactToPrintTrigger = () => {
		return <Button
			className="addButton"
			style={{ marginTop: "0" }}
			disabled={this.state.batchAction.length > 0 ? false : true}

			type="primary"
		>
			Print Labels with Dymo/Zebra
		</Button>
	};

	render() {
		const { selectedRowKeys } = this.state;

		const columns = [
			{
				title: "Label Number",
				dataIndex: "label_number",
				key: "label_number",
			},
			{
				title: "Qr Code",
				dataIndex: "random_number",
				key: "random_number",
				align: "center",
				render: (text) => (
					<p style={{ margin: 0, letterSpacing: "1.25px", fontWeight: "500" }}>{text}</p>
				),
			},

			{
				title: "Qr Image",
				dataIndex: "qr_image",
				key: "qr_image",
				align: "center",
				render: (record, text) => {
					return (
						<div
							style={{
								display: "flex",
								justifyContent: "center",
								alignItems: "center",
							}}>
							{text.qr_image && text.qr_image.trim() !== "" ? (
								<QRCode
									onClick={() => this.setState({ QRModal: true, QRValue: text.random_number.toString() })}
									value={text.random_number.toString()}
									size={40}
								/>
							) : (
								user
							)}
						</div>
					);
				},
			},
			{
				title: "Item",
				key: "item",
				align: "center",
				render: (record, text) => {
					return (
						<div>
							{text.item_id ? (
								<Link style={{ letterSpacing: "0.5px" }} to={`/job/view-inventory/${text.item_id}`}>
									VIEW
								</Link>
							) : (
								" - "
							)}
						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				align: "center",
				render: (record, text) => {
					return (
						<div style={{ display: "flex", flexDirection: "row", justifyContent: "space-around", padding: "0px 0px" }}>

							<div className='icons'>
								{!text.item_id ? (
									<Tooltip title='Delete'>
										<Button
											type='primary'
											className={!this.state.currentQrJobId && "c-btn c-round c-danger"}
											icon='delete'
											disabled={this.state.currentQrJobId}
											onClick={() => this.delete(text.qr_code_id)}></Button>
									</Tooltip>
								) : (
									" - "
								)}
							</div>
							<div style={!text.item_id ? { marginRight: "10px" } : {}} className='icons'>
								<Tooltip title='Print'>
									<Button
										type='primary'
										className={"c-btn c-round c-primary"}
										icon='printer'
										onClick={() => this.printSingalQrGeneric(this.props.match.params.id, text.qr_code_id)}></Button>
								</Tooltip>
							</div>

						</div>

					);
				},
			},
		];
		return (
			<LayoutContentWrapper>

				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<h3
							style={{
								display: "flex",
								textTransform: "capitalize",
								marginLeft: "5px",
								padding: "10px 0",
							}}>
						</h3>
						<Col sm={8}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' /> &emsp;Generic Label Managements
							</h2>
						</Col>
						<Col sm={16}>
							<Row>
								<Col sm={8} style={{ textAlign: "right" }}>
									<Search
										placeholder='Search Label/Qr'
										onChange={this.handleSearch}
										value={this.state.search}
										style={{ width: 200 }}
										disabled={this.state.currentQrJobId}
									/>
								</Col>
								<Col sm={12}>
									<Button
										className='addButton'
										style={{ marginTop: "0" }}
										type='primary'
										disabled={this.state.qrCodeList.length > 0 ? false : true}
										onClick={() => this.printQrCode(this.props.match.params.id)}>
										Print A4 Labels
									</Button>

									{/* <ReactToPrint
										content={this.reactToPrintContent}
										documentTitle="MoverInventory"
										removeAfterPrint
										trigger={this.reactToPrintTrigger}
									/> */}

									< Button
										className='addButton'
										style={{ marginTop: "0" }}
										type='primary'
										disabled={this.state.batchAction.length > 0 ? false : true}
										onClick={() => this.printDymoLabels()}>
										Print Dymo Labels
									</Button>
								</Col>

								<Col sm={4} style={{ textAlign: "right" }}>
									<button className='backButton' onClick={() => this.props.history.goBack()}>
										<i className='fa fa-chevron-left' aria-hidden='true' /> Back
									</button>
								</Col>
							</Row>
						</Col>
					</Row>
				</div >
				<Spin
					spinning={this.state.loading}
					indicator={antIcon}
					tip={
						<div>
							<p style={{ margin: "0" }}>
								Please wait, pdf labels are getting created. This process may take sometime, so you can
								click on the back button and continue with other work. You will be notified once Labels
								are generated.
							</p>
						</div>
					} />


				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div style={{ marginTop: "-1.5rem" }}>
							{this.state.qrCodeList.length > 0 ? (
								<Table
									rowSelection={{
										selectedRowKeys,
										onChange: this.onSelectChange,
									}}
									bordered={true}
									columns={columns}
									pagination={{
										total: this.state.pagination.total,
										showSizeChanger: true,
										defaultPageSize: 25,
										pageSizeOptions: ["25", "50"],
									}}
									dataSource={this.state.qrCodeList}
									onChange={this.handleChange}
								/>
							) : (
								<h4
									style={{
										fontSize: "30px",
										textAlign: "center",
										color: "#999",
										marginTop: "1rem",
									}}>
									No QR available.
								</h4>
							)}
						</div>



					</LayoutContent>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to delete Qr Code?</p>
					</Modal>
				}
				{
					<Modal
						bodyStyle={{ textAlign: "center" }}
						title={null}
						visible={this.state.QRModal}
						cancelText='Close'
						centered
						maskClosable={false}
						onOk={this.handleQrCancel}
						onCancel={this.handleQrCancel}>
						<QRCode value={this.state.QRValue} size={150} />
					</Modal>
				}

				<div style={{ display: "none" }}>
					<ComponentToPrint ref={this.setComponentRef} text={this.state} />
				</div>
			</LayoutContentWrapper >

		);
	}
}

export default connect(
	(state) => ({
		app: state.App,
		height: state.App.height,
	}),
	{ currentQrJob }
)(labelManagement);
