import {
    <PERSON><PERSON>,
    Col,
    Dropdown,
    Icon,
    Input,
    Menu,
    message,
    Modal,
    Row,
    Spin,
    Table,
    Tag,
    Tooltip,
} from "antd";
import React from "react";
import ModalImage from "react-modal-image";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import user from "../../static/images/user1.png";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import queryString from 'query-string';


const { changeCurrent } = appActions;

const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let customerId;

class batchLabelManagement extends React.Component {
    state = {
        apiParam: {},
        batchList: [],
        pagination: {},
        visible: false,
        viewPageData: "",
        pageloading: false,
        search: null,
        deleteModal: false,
        confirmModal: false,
        loading: false,
        confirmLoading: false,
        adminId: "",
        companyId: "",
        staffId: "",
        apiKeyStatus: false
    };
    componentDidMount() {
        const queryParams = queryString.parse(window.location.search);
        let params = {
            search: "",
            orderBy: "created_at",
            pageNo: parseInt(queryParams.page) || "1",
            orderSequence: "DESC",
            pageSize: 10,
            filter: "active",
        };
        const apiKeyStatusValid = localStorage.getItem("apiKeyStatus") === "1" || localStorage.getItem("apiKeyStatus") === 1 ? true : false;
        this.setState({ apiParam: params, apiKeyStatus: apiKeyStatusValid }, () => this.fetchCustomerList());


        this.setState({
            adminId:
                ((localStorage.getItem("adminid") !== ""))
                    ? localStorage.getItem("adminid")
                    : null,
            companyId:
                ((localStorage.getItem("companyID") !== ""))
                    ? localStorage.getItem("companyID")
                    : null,
            staffId:
                ((localStorage.getItem("staffId") !== ""))
                    ? localStorage.getItem("staffId")
                    : null,
        });
    }
    fetchCustomerList = () => {
        const data = [];
        data["data"] = this.state.apiParam;
        this.setState({ pageloading: true });

        API.get(
            `api/admin/batch/list?search=${this.state.apiParam.search}&pageNo=${this.state.apiParam.pageNo}&pageSize=${this.state.apiParam.pageSize}&orderSequence=${this.state.apiParam.orderSequence}&orderBy=${this.state.apiParam.orderBy}&filter=${this.state.apiParam.filter}`
        )
            .then((response) => {
                const pagination = { ...this.state.pagination };
                if (response) {
                    if (response.status === 1) {
                        pagination.total = response.data.count;
                        this.setState({
                            batchList: response.data.rows,
                            pagination,
                            pageloading: false,
                        });
                    } else {
                        this.setState({
                            usersList: response.data,
                            pagination,
                            pageloading: false,
                        });
                        // message.error(res.message)
                    }
                }
            })
            .catch((error) => {
                this.setState({ pageloading: false });
            });
    };
    menus = (text) => {
        return (
            <Menu>
                <Menu.Item key='1'>
                    <Tooltip title='View'>
                        <Button
                            type='primary'
                            className='c-btn c-round c-success'
                            icon='eye'
                            onClick={() => this.view(text.batch_id)}></Button>
                    </Tooltip>
                </Menu.Item>
                <Menu.Item key='2'>
                    <Tooltip title='Delete'>
                        <Button
                            type='primary'
                            className='c-btn c-round c-danger'
                            icon='delete'
                            onClick={() => this.delete(text.batch_id)}></Button>
                    </Tooltip>
                </Menu.Item>
            </Menu>
        );
    };
    handleChange = (pagination, filters, sorter) => {
        const pager = { ...this.state.pagination };
        pager.current = pagination.current;
        const queryParams = queryString.parse(window.location.search);
        queryParams.page = pagination.current;
        const newUrl = `${window.location.pathname}?${queryString.stringify(queryParams)}`;
        window.history.pushState(null, null, newUrl);
        let params = {
            ...this.state.apiParam,
            pageNo: pagination.current,
            orderBy: sorter.field && sorter.column ? sorter.field : "created_at",
            orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
            pageSize: pagination.pageSize,
            filter: filters.status && filters.status[0] ? filters.status[0] : "active",
        };
        this.setState(
            {
                pagination: pager,
                apiParam: params,
            },
            () => {
                this.state.search ? this.handleSearch(this.state.search) : this.fetchCustomerList();
            }
        );
        scrollTo();
    };
    view(id) {
        this.props.history.push(`${this.props.match.url}/view/${id}`);
    }

    addQrCode() {
        this.props.history.push({
            pathname: `${this.props.match.url}/add`,
        });
    }

    confirmModal(id) {
        this.setState({ confirmModal: true });
        customerId = id;
    }
    createShipment(data) {
        this.props.changeCurrent("job");
        this.props.history.push({
            pathname: "/job/add",
            state: { customerDirect: data },
        });
    }

    handleCancel = () => {
        this.setState({
            deleteModal: false,
            confirmLoading: false,
            confirmModal: false,
        });
    };
    delete = (batchId) => {
        this.setState({ confirmLoading: true });
        API.post(`api/admin/batch/delete/${batchId}`)
            .then((response) => {
                if (response) {
                    this.fetchCustomerList();
                    this.setState({
                        confirmLoading: false,
                    });
                    message.success(response.message);
                } else {
                    this.setState({
                        confirmLoading: false,
                    });
                    message.error(response.message);
                }
            })
            .catch((error) => {
                this.setState({ confirmLoading: false });
            });
    };
    handleStatusChange = () => {
        const id = customerId;
        const statusData = [];
        statusData["data"] = { customer_id: id };
        API.post("api/admin/customer/change-customer-status", statusData)
            .then((response) => {
                if (response) {
                    this.setState({
                        confirmModal: false,
                        confirmLoading: false,
                    });
                    this.fetchCustomerList({
                        page: this.state.pagination.current ? this.state.pagination.current : 1,
                    });
                    message.success(response.message);
                } else {
                    this.setState({
                        confirmModal: false,
                        confirmLoading: false,
                    });
                    message.error(response.message);
                }
            })
            .catch(() => {
                this.setState({
                    confirmModal: false,
                    confirmLoading: false,
                });
            });
    };
    callback = () => {
        if (timeoutVar) {
            clearTimeout(timeoutVar);
            timeoutVar = null;
        }
        timeoutVar = setTimeout(this.fetchCustomerList, 1000);
    };
    handleSearch = (e) => {
        this.setState(
            {
                apiParam: {
                    ...this.state.apiParam,
                    search: e,
                    pageNo: 1,
                },
                search: e,
            },
            this.callback
        );
    };

    toDate = (date) => {
        let s = new Date(date).toLocaleTimeString([], {
            day: "numeric",
            month: "short",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
        });
        return s;
    };
    render() {
        const { apiKeyStatus } = this.state;
        const columns = [

            {
                title: "Batch Name",
                dataIndex: "batch_number",
                key: "batch_number",
                width: "15%",
                // sorter:true,
                align: "center",
                render: (record, text) => {
                    return (
                        <div>
                            Batch-{record}
                        </div>
                    )
                },
            },

            {
                title: "Total Qr Count",
                dataIndex: "count",
                key: "count",
                width: "16%",
                align: "center",
            },

            {
                title: "Used Qr count",
                dataIndex: "usedQrCount",
                key: "usedQrCount",
                width: "16%",
                align: "center",
            },


            {
                title: "Date",
                dataIndex: "date",
                key: "date",
                width: "16%",
                align: "center",
                render: (record, text) => {
                    return (
                        <div>
                            {this.toDate(text.created_at)}
                        </div>
                    )
                },
            },

            {
                title: "Action",
                key: "action",
                align: "center",
                render: (record, text) => {
                    return (
                        <div className='icons' style={{ textAlign: "center" }}>
                            <Dropdown placement='bottomCenter' overlay={this.menus.bind(null, text)}>
                                <Icon
                                    type='setting'
                                    theme='twoTone'
                                    twoToneColor='#3fa146'
                                    onClick={() => this.menus.bind(null, text)}
                                />
                            </Dropdown>
                        </div>
                    );
                },
            },
        ];
        return (
            <LayoutContentWrapper>
                <div className='top_header' style={{ height: "100%" }}>
                    <Row>
                        <h3
                            style={{
                                display: "flex",
                                textTransform: "capitalize",
                                marginLeft: "5px",
                                padding: "10px 0",
                            }}>
                        </h3>
                        <Col sm={8}>
                            <h2 style={{ marginBottom: "0" }}>
                                <Icon type='setting' /> &emsp;Batch Label Managements
                            </h2>
                        </Col>
                        <Col sm={16}>
                            <Row>
                                <Col sm={8} style={{ textAlign: "right" }}>
                                    <Search
                                        placeholder='Search Label/Qr'
                                        onChange={this.handleSearch}
                                        value={this.state.search}
                                        style={{ width: 200 }}
                                        disabled={this.state.currentQrJobId}
                                    />
                                </Col>
                                <Col sm={12}>
                                    <Button
                                        className='addButton'
                                        style={{ marginTop: "0" }}
                                        type='primary'
                                        disabled={this.state.currentQrJobId}
                                        onClick={() => this.addQrCode()}>
                                        + Add QR Codes
                                    </Button>
                                </Col>

                                <Col sm={4} style={{ textAlign: "right" }}>
                                    <button className='backButton' onClick={() => this.props.history.goBack()}>
                                        <i className='fa fa-chevron-left' aria-hidden='true' /> Back
                                    </button>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                </div>
                <Spin spinning={this.state.pageloading} indicator={antIcon}>
                    <LayoutContent>
                        <div style={{ marginTop: "-1.5rem", overflowX: "auto" }}>
                            <Table
                                bordered={true}
                                columns={columns}
                                pagination={{
                                    total: this.state.pagination.total,
                                    showSizeChanger: true,
                                    defaultPageSize: 10,
                                    pageSizeOptions: ["10"],
                                    current:this.state.apiParam.pageNo
                                }}
                                key={this.state.batchList}
                                dataSource={this.state.batchList}
                                onChange={this.handleChange}
                            />
                        </div>
                    </LayoutContent>
                </Spin>
                {
                    <>
                        <Modal
                            title='Are You Sure?'
                            visible={this.state.deleteModal}
                            onOk={this.handleOk}
                            okText='Yes'
                            cancelText='No'
                            centered
                            maskClosable={false}
                            confirmLoading={this.state.confirmLoading}
                            onCancel={this.handleCancel}>
                            <p>Are you sure you want to delete Customer?</p>
                        </Modal>
                        <Modal
                            title='Are You Sure?'
                            visible={this.state.confirmModal}
                            onOk={this.handleStatusChange}
                            okText='Yes'
                            cancelText='No'
                            centered
                            maskClosable={false}
                            confirmLoading={this.state.confirmLoading}
                            onCancel={this.handleCancel}>
                            <p>Are you sure you want to change status of this Customer?</p>
                        </Modal>
                    </>
                }
            </LayoutContentWrapper>
        );
    }
}
export default connect(null, { changeCurrent })(batchLabelManagement);
