import {
  <PERSON><PERSON>,
  Col,
  Form,
  Icon,
  message,
  Row,
  Spin,
  Select,
} from "antd";
import React from "react";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import Sample_Qr from "../../static/images/sample_qr.png";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import "./qrstyle.css";
import ReactToPrint from "react-to-print";
import { ComponentToPrint } from "./ComponentToPrint";


const { changeCurrent } = appActions;
const { Option } = Select;
const API = new Api({});
const antIcon = <Icon type="loading" style={{ fontSize: 24 }} spin />;

class printDymoQrCodeQrCode extends React.Component {
  componentRef = null;

  state = {
    isDone: false,
    loading: false,
    saving: false,
    contentloader: false,
    color: "#000",
    jobItemLength: 0,
    preSetting: {},
    initialTitle: "",
    printerName: "",
    labelImages: "",
    batchAction: [],
    batchActionImages: [],
    sourceList: [{ name: `Ten Labels/Page (8.5 x 11)`, id: 1 }, { name: `Label Printer (4" x 1.5")`, id: 2 }, { name: `Label Printer (1 1/8" x 3 1/2")`, id: 3 }],
    dymoLabelPrinterId: 1,
  };

  componentDidMount() {
    this.setState({
      batchAction: this.props && this.props.location && this.props.location.state && this.props.location.state.name,
      contentloader: false,
      loading: false,
    });

  }

  DymohandleChange = (e) => {
    this.setState({ dymoLabelPrinterId: e.id });
  };

  padLeadingZeros = (num, size) => {
    let s = num + "";
    while (s.length < size) s = "0" + s;
    return s;
  };


  setComponentRef = (ref) => {
    this.componentRef = ref;
  };

  pageStyle = "@page{ size: 3.25in 2in }"

  reactToPrintContent = () => {
    return this.componentRef;
  };

  reactToPrintTrigger = () => {
    return <Button
      className="submitButton"
      type="primary"
    >
      Print
    </Button>

  };

  render() {
    const { shipmentData, jobItemLength } = this.state;
    const initialLableNumber = this.padLeadingZeros(jobItemLength, 4);

    const formItemLayout = {
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 5,
        },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: {
          span: 24,
        },
        md: {
          span: 24,
        },
        lg: {
          span: 24,
        },
        xl: {
          span: 24,
        },
      },
    };
    const tailFormItemLayout = {
      wrapperCol: {
        xs: {
          span: 24,
          offset: 6,
        },
        sm: {
          span: 16,
          offset: 6,
        },
        xl: {
          offset: 6,
        },
      },
    };

    return (
      <LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
        <div className="add_header">
          <h2 style={{ marginBottom: "0", marginLeft: "10px" }}>
            <i className="fas fa-qrcode"></i> Print Dymo/Zebra QR Code
          </h2>
          <button
            className="backButton"
            onClick={() => this.props.history.goBack()}
          >
            <i className="fa fa-chevron-left" aria-hidden="true"></i> Back
          </button>
        </div>
        <LayoutContent
          style={{
            margin: "0 20px",
            height: "93%",
            overflowY: "auto",
          }}
        >
          <div className="print-qr">

            <Spin spinning={this.state.contentloader} indicator={antIcon}>
              <Row>
                <Col sm={12} style={{ position: "relative" }}>
                  <Form {...formItemLayout} onSubmit={this.printDymoQrCode}>
                    <Row style={{ marginBottom: "10px", marginTop: "-30px" }} >
                      <Form.Item style={{ padding: "0px", margin: "0px" }} label='Paper/Sticker :'>
                        <Select
                          size={"default"}
                          labelInValue
                          placeholder="Ten Labels/Page (8.5 x 11)"
                          style={{ width: "70%" }}
                        >

                          {this.state.sourceList.map((e) => (
                            <Option
                              key={e.id}
                              value={e.id}
                              onClick={this.DymohandleChange.bind(null, e)}
                            >
                              {e.name}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Row>


                    <Form.Item
                      {...tailFormItemLayout}
                      style={{
                        marginLeft: "0",
                        marginRight: "0",
                        transform: "translatex(-25%)",
                      }}
                    >
                      <ReactToPrint
                        content={this.reactToPrintContent}
                        documentTitle="MoverInventory"
                        removeAfterPrint
                        trigger={this.reactToPrintTrigger}
                      />
                    </Form.Item>
                  </Form>
                </Col>
                <Col sm={{ span: 10, offset: 1 }}>

                  <div className="isoLogoWrapper">
                    <div
                      className="QR-image"
                      style={{
                        display: "flex",
                        alignItems: "center",
                      }}
                    >
                      <div style={{ display: "flex", flexDirection: "column" }}>
                        <b>
                          <h2 style={{ margin: "0px 0px 0px 30px" }}>
                            012345
                          </h2>
                        </b>
                        <img
                          src={Sample_Qr}
                          style={{ marginTop: "-5px" }}
                          width="135px"
                          alt="Mover Inventory"
                        />
                        <div style={{ margin: " -15px 0px 0px 18px" }}>
                          <h2>
                            <b> ABCD1234 </b>
                          </h2>
                        </div>
                      </div>
                    </div>
                  </div>
                </Col>
              </Row>
            </Spin>
          </div>
        </LayoutContent>
        <div style={{ display: "none" }}>
          <ComponentToPrint ref={this.setComponentRef} text={this.state} />
        </div>
      </LayoutContentWrapper>
    );
  }
}

export default Form.create()(connect(null, { changeCurrent })(printDymoQrCodeQrCode));
