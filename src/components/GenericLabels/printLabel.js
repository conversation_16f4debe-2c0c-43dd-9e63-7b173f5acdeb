import { Button, Checkbox, Col, Form, Icon, Input, message, Row, Spin, notification } from "antd";
import { saveAs } from "file-saver";
import React from "react";
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
//import { SketchPicker } from "react-color";
import Sample_Qr from "../../static/images/sample_qr.png";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import "./qrstyle.css";

// alert('hi');
const { changeCurrent } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddQrCode extends React.Component {
	state = {
		isDone: false,
		loading: false,
		saving: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		displayColorPicker: false,
		color: "#000",
		displayTitle: true,
		job: true,
		company: true,
		contact: true,
		fromAdd: true,
		toAdd: true,
		labelNumber: true,
		qrCode: true,
		qrTitle: "",
		shipmentData: null,
		jobItemLength: 0,
		preSetting: {},
		initialTitle: "",
	};

	componentDidMount() {
		const id = this.props.match.params.id;
		this.props.changeCurrent("job");
		this.setState({ contentloader: true });
		API.get(`api/admin/qr_code/${id}/get_setting`)
			.then(async (res) => {
				await this.setState({
					preSetting: res.data,
					displayTitle: res.data.title_flag === "yes" ? true : false,
					company: res.data.company_name_flag === "yes" ? true : false,
					contact: res.data.company_contact_flag === "yes" ? true : false,
					fromAdd: res.data.from_address_flag === "yes" ? true : false,
					toAdd: res.data.to_address_flag === "yes" ? true : false,
					qrCode: res.data.qr_code_label_flag === "yes" ? true : false,
					labelNumber: res.data.sequenced_label_flag === "yes" ? true : false,
					job: res.data.job_number_flag === "yes" ? true : false,
					initialTitle:
						(res.data && res.data.place !== null) || res.data.place !== "" ? res.data.place : "",
				});
				this.setState({ contentloader: false });
			})
			.catch((err) => {
				this.setState({ contentloader: false });
			});
		// const data1 = [];
		API.get(`api/admin/shipment/${id}`)
			.then(async (response) => {
				if (response) {
					await this.setState({
						shipmentData: response.data && response.data,
						qrTitle:
							this.state.initialTitle !== "" ? this.state.initialTitle : response.data.shipment_name,
						jobItemLength: response.data.job_items.length,
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
				this.setState({ contentloader: false });
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
		setTimeout(() => {
			this.setState({ loading: false });
		}, 120000);
	}

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {

		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};
	handleSubmit = (e) => {
		e.preventDefault();

		this.props.form.validateFieldsAndScroll((err, values) => {
			let params = {
				place: values.place,
				title_flag: values.display_title,
				company_name_flag: values.display_company_name,
				company_contact_flag: values.display_company_no,
				from_address_flag: values.display_from_add,
				to_address_flag: values.display_to_add,
				job_number_flag: values.display_job_number,
				qr_code_label_flag: values.display_qr_code,
				sequenced_label_flag: values.display_label_no,
				responseType: "blob",
			};
			if (!err) {
				const data = [];

				data["data"] = params;
				this.setState({ loading: true });
				API.post("api/admin/qr_code/" + this.props.match.params.id + "/print/", data)
					.then((response) => {
						const pdfBlob = new Blob([response], {
							type: "application/pdf",
						});
						saveAs(pdfBlob, "qrcode.pdf");
						if (response) {
							this.setState({ loading: false });
							// message.success("Pdf generated successfully.");
							notification.success({
								message: "Qr Pdf",
								description: "Your Pdf for QR code labels is generated successfully.",
								duration: 0,
							});
						} else {
							this.setState({ loading: false });
							message.error("Error while creating pdf.");
						}
					})
					.catch((error) => {
						this.setState({ loading: false });
					});
			}
		});
	};
	handleSaveSettings = (e) => {
		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {
			let params = {
				place: values.place,
				title_flag: values.display_title ? "yes" : "no",
				company_name_flag: values.display_company_name ? "yes" : "no",
				company_contact_flag: values.display_company_no ? "yes" : "no",
				from_address_flag: values.display_from_add ? "yes" : "no",
				to_address_flag: values.display_to_add ? "yes" : "no",
				job_number_flag: values.display_job_number ? "yes" : "no",
				qr_code_label_flag: values.display_qr_code ? "yes" : "no",
				sequenced_label_flag: values.display_label_no ? "yes" : "no",
			};
			if (!err) {
				const data = [];

				data["data"] = params;
				this.setState({ saving: true });
				// /qr_code/:jobId/save_setting
				API.post("api/admin/qr_code/" + this.props.match.params.id + "/save_setting", data)
					.then((response) => {
						if (response) {
							this.setState({ saving: false });
							message.success(response.message);
						} else {
							this.setState({ saving: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ saving: false });
					});
			} else {
				//console.log(err);
			}
		});
	};
	normFile = (e) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e && e.fileList;
	};

	handleClick = () => {
		this.setState({ displayColorPicker: !this.state.displayColorPicker });
	};

	handleClose = () => {
		this.setState({ displayColorPicker: false });
	};

	handleChange = (color) => {
		this.setState({ color: color.hex });
	};

	handleQRTitleChange = (e) => {
		this.setState({ qrTitle: e.target.value });
	};

	handleQRLableChange = (label) => {
		if (label === "title") {
			this.setState({ displayTitle: !this.state.displayTitle });
		} else if (label === "job") {
			this.setState({ job: !this.state.job });
		} else if (label === "company") {
			this.setState({ company: !this.state.company });
		} else if (label === "contact") {
			this.setState({ contact: !this.state.contact });
		} else if (label === "from") {
			this.setState({ fromAdd: !this.state.fromAdd });
		} else if (label === "to") {
			this.setState({ toAdd: !this.state.toAdd });
		} else if (label === "label") {
			this.setState({ labelNumber: !this.state.labelNumber });
		} else if (label === "qr") {
			this.setState({ qrCode: !this.state.qrCode });
		}
	};

	padLeadingZeros = (num, size) => {
		let s = num + "";
		while (s.length < size) s = "0" + s;
		return s;
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const { shipmentData, jobItemLength, preSetting } = this.state;
		const initialLableNumber = this.padLeadingZeros(jobItemLength, 4);

		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 24,
				},
				md: {
					span: 24,
				},
				lg: {
					span: 24,
				},
				xl: {
					span: 24,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0", marginLeft: "10px" }}>
						<i className='fas fa-qrcode'></i> Print QR Code
					</h2>
					{/* <p>{!this.state.isDone && "Last "  }</p> */}
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent
					style={{
						margin: "0 20px",
						height: "93%",
						overflowY: "auto",
					}}>
					<div className='print-qr'>
						<Spin
							spinning={this.state.loading}
							indicator={antIcon}
							tip={
								<div>
									<p style={{ margin: "0" }}>
										Please wait, pdf labels are getting created. This process may take sometime, so you can
										click on the back button and continue with other work. You will be notified once Labels
										are generated.
									</p>
								</div>
							}>
							<Spin spinning={this.state.contentloader} indicator={antIcon}>
								<Row>
									<Col sm={12} style={{ position: "relative" }}>
										<Form {...formItemLayout} onSubmit={this.handleSubmit}>
											{/* <Form.Item label="">
                      { <div>Select Color</div> }
                      <div style={styles.swatch} onClick={this.handleClick}>
                        <div style={styles.color} />
                      </div>
                      <span
                        style={{ marginLeft: "10px", marginBottom: "20px" }}
                      >
                        Select Color
                      </span>
                      {this.state.displayColorPicker ? (
                        <div style={styles.popover}>
                          <div
                            style={styles.cover}
                            onClick={this.handleClose}
                          />
                          <SketchPicker
                            color={this.state.color}
                            onChange={this.handleChange}
                          />
                        </div>
                      ) : null}
                    </Form.Item> */}
											<Row>
												<Col sm={12}>
													<Form.Item>
														{getFieldDecorator("place", {
															initialValue:
																preSetting && preSetting.place
																	? preSetting.place
																	: shipmentData && shipmentData.shipment_name,
															rules: [
																{
																	required: true,
																	message: "Please input name!",
																},
															],
														})(
															<Input
																onChange={(e) => this.handleQRTitleChange(e)}
																maxLength={90}
																placeholder='Title'
															/>
														)}
													</Form.Item>
												</Col>
											</Row>
											<Row>
												<Col sm={12}>
													<Form.Item label=''>
														{getFieldDecorator("display_title", {
															valuePropName: "checked",
															initialValue: this.state.displayTitle,
														})(
															<Checkbox onChange={() => this.handleQRLableChange("title")}>Display title</Checkbox>
														)}
													</Form.Item>
												</Col>
												<Col sm={12}>
													<Form.Item label=''>
														{getFieldDecorator("display_job_number", {
															valuePropName: "checked",
															initialValue: this.state.job,
														})(
															<Checkbox onChange={() => this.handleQRLableChange("job")}>
																Display Job Number
															</Checkbox>
														)}
													</Form.Item>
												</Col>
											</Row>
											<Row>
												<Col sm={12}>
													<Form.Item label=''>
														{getFieldDecorator("display_company_name", {
															valuePropName: "checked",
															initialValue: this.state.company,
														})(
															<Checkbox onChange={() => this.handleQRLableChange("company")}>
																Display Company Name
															</Checkbox>
														)}
													</Form.Item>
												</Col>
												<Col sm={12}>
													<Form.Item label=''>
														{getFieldDecorator("display_company_no", {
															valuePropName: "checked",
															initialValue: this.state.contact,
														})(
															<Checkbox onChange={() => this.handleQRLableChange("contact")}>
																Display Company Phone
															</Checkbox>
														)}
													</Form.Item>
												</Col>
											</Row>
											<Row>
												<Col sm={12}>
													<Form.Item label=''>
														{getFieldDecorator("display_from_add", {
															valuePropName: "checked",
															initialValue: this.state.fromAdd,
														})(
															<Checkbox onChange={() => this.handleQRLableChange("from")}>
																Display Origin Address
															</Checkbox>
														)}
													</Form.Item>
												</Col>
												<Col sm={12}>
													<Form.Item label=''>
														{getFieldDecorator("display_to_add", {
															valuePropName: "checked",
															initialValue: this.state.toAdd,
														})(
															<Checkbox onChange={() => this.handleQRLableChange("to")}>
																Display Destination Address
															</Checkbox>
														)}
													</Form.Item>
												</Col>
											</Row>
											<Row>
												<Col sm={12}>
													<Form.Item label=''>
														{getFieldDecorator("display_label_no", {
															valuePropName: "checked",
															initialValue: this.state.labelNumber,
														})(
															<Checkbox onChange={() => this.handleQRLableChange("label")}>
																Display Label Number
															</Checkbox>
														)}
													</Form.Item>
												</Col>
												<Col sm={12}>
													<Form.Item label=''>
														{getFieldDecorator("display_qr_code", {
															valuePropName: "checked",
															initialValue: this.state.qrCode,
														})(
															<Checkbox onChange={() => this.handleQRLableChange("qr")}>Display QR Code</Checkbox>
														)}
													</Form.Item>
												</Col>
											</Row>

											<Form.Item
												{...tailFormItemLayout}
												style={{
													marginLeft: "0",
													marginRight: "0",
													transform: "translatex(-25%)",
												}}>
												<Button
													className='submitButton'
													loading={this.state.loading}
													type='primary'
													htmlType='submit'>
													Print
												</Button>
											</Form.Item>
										</Form>
										<Form
											style={{
												position: "absolute",
												bottom: "0",
												right: "0",
												transform: "translatex(-100%)",
											}}
											onSubmit={this.handleSaveSettings}>
											<Form.Item {...tailFormItemLayout}>
												<Button
													className='submitButton'
													loading={this.state.saving}
													type='primary'
													htmlType='submit'>
													Save Settings
												</Button>
											</Form.Item>
										</Form>
									</Col>
									<Col sm={{ span: 10, offset: 1 }}>
										{this.state.displayTitle && (
											<div
												className='QR-title'
												style={{
													marginLeft: "15px",
													fontSize: "20px",
													fontWeight: "bold",
												}}>
												{this.state.qrTitle}
											</div>
										)}
										<div className='isoLogoWrapper'>
											<div
												className='QR-image'
												style={{
													display: "flex",
													alignItems: "center",
												}}>
												<div style={{ display: "flex", flexDirection: "column" }}>
													<img src={Sample_Qr} width='135px' alt='Mover Inventory' />
													<div style={{ marginLeft: "15px" }}>
														<>
															<b
																style={
																	!this.state.labelNumber ? { visibility: "hidden" } : { visibility: "visible" }
																}>
																{initialLableNumber ? initialLableNumber : "0000"}
															</b>
															&nbsp;
															<span>{this.state.qrCode && "78QE8901"}</span>
														</>
													</div>
												</div>
												<div>
													{this.state.job && (
														<div className='QR-job-no'>Job No : {shipmentData && shipmentData.job_number}</div>
													)}
													{this.state.company && (
														<div className='QR-company-name'>
															{shipmentData && shipmentData.job_company && shipmentData.job_company.company_name}
														</div>
													)}
													{this.state.contact && (
														<div className='QR-company-contact'>
															<NumberFormat
																value={shipmentData && shipmentData.job_company && shipmentData.job_company.phone}
																displayType={"text"}
																format='(###) ###-####'
															/>
														</div>
													)}
													{this.state.fromAdd && (
														<div className='QR-from-add'>
															<p>
																<strong>From: </strong>
																{shipmentData &&
																shipmentData.pickup_address &&
																shipmentData.pickup_address !== "undefined" &&
																shipmentData.pickup_address !== "null"
																	? shipmentData.pickup_address
																	: ""}
																{shipmentData &&
																shipmentData.pickup_address2 &&
																shipmentData.pickup_address2 !== "undefined" &&
																shipmentData.pickup_address2 !== "null"
																	? " " + shipmentData.pickup_address2
																	: ""}
															</p>
															<p>
																{shipmentData &&
																shipmentData.pickup_city &&
																shipmentData.pickup_city !== "undefined" &&
																shipmentData.pickup_city !== "null"
																	? shipmentData.pickup_city
																	: ""}
																{shipmentData &&
																shipmentData.pickup_state &&
																shipmentData.pickup_state !== "undefined" &&
																shipmentData.pickup_state !== "null"
																	? " " + shipmentData.pickup_state
																	: ""}
																{shipmentData &&
																shipmentData.pickup_zipcode &&
																shipmentData.pickup_zipcode !== "undefined" &&
																shipmentData.pickup_zipcode !== "null"
																	? ", " + shipmentData.pickup_zipcode
																	: ""}
															</p>
															<p>
																{shipmentData &&
																shipmentData.pickup_country &&
																shipmentData.pickup_country !== "undefined" &&
																shipmentData.pickup_country !== "null"
																	? shipmentData.pickup_country
																	: ""}
															</p>
														</div>
													)}
													{this.state.toAdd && (
														<div className='QR-to-add'>
															<p>
																<strong>To: </strong>
																{shipmentData &&
																shipmentData.delivery_address &&
																shipmentData.delivery_address !== "undefined" &&
																shipmentData.delivery_address !== "null"
																	? shipmentData.delivery_address
																	: ""}
																{shipmentData &&
																shipmentData.delivery_address2 &&
																shipmentData.delivery_address2 !== "undefined" &&
																shipmentData.delivery_address2 !== "null"
																	? " " + shipmentData.delivery_address2
																	: ""}
															</p>
															<p>
																{shipmentData &&
																shipmentData.delivery_city &&
																shipmentData.delivery_city !== "undefined" &&
																shipmentData.delivery_city !== "null"
																	? shipmentData.delivery_city
																	: ""}
																{shipmentData &&
																shipmentData.delivery_state &&
																shipmentData.delivery_state !== "undefined" &&
																shipmentData.delivery_state !== "null"
																	? " " + shipmentData.delivery_state
																	: ""}
																{shipmentData &&
																shipmentData.delivery_zipcode &&
																shipmentData.delivery_zipcode !== "undefined" &&
																shipmentData.delivery_zipcode !== "null"
																	? ", " + shipmentData.delivery_zipcode
																	: ""}
															</p>
															<p>
																{shipmentData &&
																shipmentData.delivery_country &&
																shipmentData.delivery_country !== "undefined" &&
																shipmentData.delivery_country !== "null"
																	? shipmentData.delivery_country
																	: ""}
															</p>
														</div>
													)}
												</div>
											</div>
											<div
												className='QR-details'
												style={{
													marginLeft: "15px",
													marginTop: "12px",
													width: "60%",
												}}>
												{/* {this.state.fromAdd && (
													<div className="QR-from-add">
														<p>
															<strong>From: </strong>
															{shipmentData &&
																shipmentData.pickup_address &&
																shipmentData.pickup_address !== "undefined" &&
																shipmentData.pickup_address !== "null"
																? shipmentData.pickup_address
																: ""}
															{shipmentData &&
																shipmentData.pickup_address2 &&
																shipmentData.pickup_address2 !== "undefined" &&
																shipmentData.pickup_address2 !== "null"
																? " " + shipmentData.pickup_address2
																: ""}
														</p>
														<p>
															{shipmentData &&
																shipmentData.pickup_city &&
																shipmentData.pickup_city !== "undefined" &&
																shipmentData.pickup_city !== "null"
																? shipmentData.pickup_city
																: ""}
															{shipmentData &&
																shipmentData.pickup_state &&
																shipmentData.pickup_state !== "undefined" &&
																shipmentData.pickup_state !== "null"
																? " " + shipmentData.pickup_state
																: ""}
															{shipmentData &&
																shipmentData.pickup_zipcode &&
																shipmentData.pickup_zipcode !== "undefined" &&
																shipmentData.pickup_zipcode !== "null"
																? ", " + shipmentData.pickup_zipcode
																: ""}
														</p>
														<p>
															{shipmentData &&
																shipmentData.pickup_country &&
																shipmentData.pickup_country !== "undefined" &&
																shipmentData.pickup_country !== "null"
																? shipmentData.pickup_country
																: ""}
														</p>
													</div>
												)}
												{this.state.toAdd && (
													<div className="QR-to-add">
														<p>
															<strong>To: </strong>
															{shipmentData &&
																shipmentData.delivery_address &&
																shipmentData.delivery_address !== "undefined" &&
																shipmentData.delivery_address !== "null"
																? shipmentData.delivery_address
																: ""}
															{shipmentData &&
																shipmentData.delivery_address2 &&
																shipmentData.delivery_address2 !== "undefined" &&
																shipmentData.delivery_address2 !== "null"
																? " " + shipmentData.delivery_address2
																: ""}
														</p>
														<p>
															{shipmentData &&
																shipmentData.delivery_city &&
																shipmentData.delivery_city !== "undefined" &&
																shipmentData.delivery_city !== "null"
																? shipmentData.delivery_city
																: ""}
															{shipmentData &&
																shipmentData.delivery_state &&
																shipmentData.delivery_state !== "undefined" &&
																shipmentData.delivery_state !== "null"
																? " " + shipmentData.delivery_state
																: ""}
															{shipmentData &&
																shipmentData.delivery_zipcode &&
																shipmentData.delivery_zipcode !== "undefined" &&
																shipmentData.delivery_zipcode !== "null"
																? ", " + shipmentData.delivery_zipcode
																: ""}
														</p>
														<p>
															{shipmentData &&
																shipmentData.delivery_country &&
																shipmentData.delivery_country !== "undefined" &&
																shipmentData.delivery_country !== "null"
																? shipmentData.delivery_country
																: ""}
														</p>
													</div>
												)} */}
											</div>
										</div>
									</Col>
								</Row>
							</Spin>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(AddQrCode));
