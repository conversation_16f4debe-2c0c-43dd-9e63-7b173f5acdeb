import * as React from "react";
import { Col } from "antd";

import "./qrstyle.css";


export class ComponentToPrint extends React.PureComponent {


  componentDidMount() {
    console.log("this.props", this.props)
  }

  render() {
    const { text } = this.props;

    return (
      <div>
        {text && text.dymoLabelPrinterId == 1 ?
          <div style={{ padding: "0px 40px" }}>
            {text && text.batchAction && text.batchAction.length > 0 ?
              text.batchAction.map((data, i) => (
                <Col className="aliginItem" style={{ pageBreakInside: "avoid", paddingTop: "3px" }} sm={{ span: 12 }} >
                  <div style={{ alignItems: "center", textAlign: "center" }}>
                    <div>
                      <h2 style={{ fontSize: "2px" }} >
                        {" "}
                        <b> {data.label_number} </b>
                      </h2>
                    </div>

                    <img
                      src={data.qr_image}
                      style={{ marginTop: "-3px" }}
                      width="20px"
                      height="20px"
                      alt="Mover Inventory"
                    />

                    <div>
                      <h2 style={{ fontSize: "2px" }} >
                        {" "}
                        <b> {data.random_number} </b>
                      </h2>
                    </div>
                  </div>
                </Col>
              ))
              : ""
            }
          </div>
          :
          text && text.dymoLabelPrinterId == 2 ?
            <div >
              {text && text.batchAction && text.batchAction.length > 0 ?
                text.batchAction.map((data, i) => (
                  <div className="aliginItem" style={{ pageBreakInside: "avoid", paddingTop: "10px" }}>
                    <div className="QR-image" style={{ display: "flex", marginTop: "0px", justifyContent: "center" }}>
                      <div style={{ alignItems: "center", textAlign: "center" }}>
                        <div>
                          <h2 style={{ fontSize: "8px" }}>
                            <b>{data.label_number}</b>
                          </h2>
                        </div>
                        <img
                          src={data.qr_image}
                          style={{ marginTop: "-2px" }}
                          width="55px"
                          height="45px"
                          alt="Mover Inventory"
                        />
                        <div>
                          <h2 style={{ fontSize: "10px" }}>
                            <b>{data.random_number}</b>
                          </h2>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
                : ""
              }
            </div>
            :
            <div>
              {text && text.batchAction && text.batchAction.length > 0 ?
                text.batchAction.map((data, i) => (
                  <div className="aliginItem" style={{ pageBreakInside: "avoid", paddingTop: "10px" }}>
                    <div className="QR-image" style={{ display: "flex", marginTop: "0px", justifyContent: "center" }}>
                      <div style={{ alignItems: "center", textAlign: "center" }}>
                        <div>
                          <h2 style={{ fontSize: "10px" }}>
                            <b>{data.label_number}</b>
                          </h2>
                        </div>
                        <img
                          src={data.qr_image}
                          style={{ marginTop: "-2px" }}
                          width="60px"
                          height="50px"
                          alt="Mover Inventory"
                        />
                        <div>
                          <h2 style={{ fontSize: "10px" }}>
                            <b>{data.random_number}</b>
                          </h2>
                        </div>
                      </div>
                    </div>
                  </div>
                )) : ""}
            </div>
        }
      </div>
    );
  }
}

export const FunctionalComponentToPrint = React.forwardRef((props, ref) => {
  // eslint-disable-line max-len
  return <ComponentToPrint ref={ref} text={props.text} />;
});
