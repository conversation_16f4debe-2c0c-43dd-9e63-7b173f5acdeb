import "../../static/css/add.css";

import { Form, Icon, Input, Spin, message, Checkbox, Button } from "antd";
import Api from "../../api/api-handler";

import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import React from "react";
import appActions from "../../redux/app/actions";
import { connect } from "react-redux";

const { changeCurrent } = appActions;
const { TextArea } = Input;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});

class ViewShipmentTypeStage extends React.Component {
    state = {
        shipmentTypeStageData: null,
        loading: false,
        contentloader: false,
        previewVisible: false,
        previewImage: "",
        fileList: [],
        integrationKeyStatus: false,
        integrationKey: "",
        warehouseId: "",
        storageShipmentJobId: "",
        trueCheckBox: false,

        shipmentTypeDetails: [],
        isScanIntoStorageEditable: true,
        isScanOutOfStorageEditable: true,
        updateOtherStageDetails: [],
        pdf_time_stamp_checked: false,
        isMakeExceptions: false,
        isMakeEnablePartialComplete: false,
        isSupervisorSignatureAtOrigin: true,
        isSupervisorSignatureAtDestination: true,
        isCustomerSignatureAtOrigin: true,
        isCustomerSignatureAtDestination: true,
        isMakeDefaultManualLabelVisible: false,
        shipment_job_id: null,
        allowEditEnablePartialCompleteStage: false,
        isMakeAddScanVisible:false,
        isMakeRemoveScanVisible:false
    };

    componentDidMount() {
        if (this.props && this.props.location && this.props.location.state && this.props.location.state.inventoryData) {
            const warehouseId = this.props.location.state.inventoryData.warehouseId;
            const storageShipmentJobId = this.props.location.state.inventoryData.storage_shipment_job_id;
            const ShipmentJobId = this.props.location.state.inventoryData.job_id;
            this.setState({
                allowEditEnablePartialCompleteStage: this.props.location.state.isCurrentStage,
                shipment_job_id: ShipmentJobId,
                warehouseId: warehouseId,
                storageShipmentJobId: storageShipmentJobId,
                pdf_time_stamp_checked: localStorage.getItem("pdf_time_stamp_checked")
                    && localStorage.getItem("pdf_time_stamp_checked") == 1 ? true
                    : false
            })
        }

        const id = this.props.match.params.id;
        this.setState({ contentloader: true, loading: true });

        const data = [];
        data["data"] = { local_shipment_stage_id: id };

        API.post("api/admin/shipment-type-for-shipment/view-shipment-stage", data).then((response) => {
            if (response) {
                if (response.data.is_add_exceptions === 1 || response.data.is_add_exceptions === true) {
                    this.setState({ isMakeExceptions: true });
                }
                if (response.data.add_items_to_inventory === 1 || response.data.add_items_to_inventory === true) {
                    this.setState({ isMakeDefaultManualLabelVisible: true });
                }
                if (
                    response.data.unassign_storage_units_from_items === 1 ||
                    response.data.unassign_storage_units_from_items === true ||
                    response.data.remove_items_to_inventory === 1 ||
                    response.data.remove_items_to_inventory === true
                ) {
                    this.setState({ isMakeEnablePartialComplete: true });
                }
                this.setState(
                    {
                        shipmentTypeStageData: response.data && response.data,
                        contentloader: false,
                        loading: false,
                        shipmentTypeDetails: response.shipmentTypeDetail && response.shipmentTypeDetail,
                    },
                    () => this.props.form.setFieldsValue({})
                );

				if (response.data.order_of_stages > 1) {
					const check = response.shipmentTypeDetail[response.data.order_of_stages - 2]
					if (check.add_items_to_inventory === 1 || check.add_items_to_inventory === true) {
						this.setState({
							isMakeAddScanVisible: true
						})
					}
					if (check.remove_items_to_inventory === 1 || check.remove_items_to_inventory === true) {
						this.setState({
							isMakeRemoveScanVisible: true
						})
					}
				}
            } else {
                message.error(response.message);
                this.setState({ contentloader: false, loading: false });
            }
        });

        API.get(`api/admin/integrationKey/fetch`)
            .then((response) => {
                if (response) {
                    if (response.data.status === "active") {
                        this.setState({
                            integrationKeyStatus: true,
                            integrationKey: response.data.integration_key
                        });
                    } else {
                        this.setState({
                            integrationKeyStatus: false,
                        });
                    }
                }
            })
            .catch((error) => {
                this.setState({ contentloader: false });
            });

    }

    handleSubmit = (e) => {
        e.preventDefault();
        this.props.form.validateFieldsAndScroll((err, values) => {

            if (this.state.isNextStageAddAdditionalScan && !values.add_items_to_inventory) {
                this.setState({
                    additionalScanModel: true
                })
            }
            else if (this.state.isNextStageRemoveAdditionalScan && !values.remove_items_to_inventory) {
                this.setState({
                    additionalScanModel: true
                })
            }
            else {
                let params = {
                    local_shipment_stage_id: this.props.match.params.id,
                    name: values.name,
                    order_of_stages: values.order_of_stages,
                    scan_require: values.scan_require,
                    remove_scan_require: values.remove_scan_require,
                    scan_into_storage: values.scan_into_storage,
                    scan_out_of_storage: values.scan_out_of_storage,
                    is_add_exceptions: values.is_add_exceptions,
                    allow_default_manual_label: values.add_items_to_inventory ? values.allow_default_manual_label : false,
                    add_items_to_inventory: values.add_items_to_inventory,
                    assign_storage_units_to_items: values.assign_storage_units_to_items,
                    unassign_storage_units_from_items: values.unassign_storage_units_from_items,
                    remove_items_to_inventory: values.remove_items_to_inventory,
                    enable_partial_complete_stage: values.remove_items_to_inventory || values.unassign_storage_units_from_items ? values.enable_partial_complete_stage : false,
                    show_no_exceptions: values.is_add_exceptions ? values.show_no_exceptions : false,
                    PDF_time_require: values.PDF_time_require,
                    is_add_item: values.scan_into_storage == true ? true : values.is_add_item,
                    supervisor_signature_require: values.supervisor_signature_require,
                    customer_signature_require: values.customer_signature_require,
                    why_supervisor_signature_require_note: values.why_supervisor_signature_require_note,
                    why_customer_signature_require_note: values.why_customer_signature_require_note,
                    customer_signature_require_at_origin_to_all_pages: values.customer_signature_require_at_origin_to_all_pages,
                    customer_signature_require_at_destination_to_all_pages: values.customer_signature_require_at_destination_to_all_pages,
                    supervisor_signature_require_at_origin_to_all_pages: values.supervisor_signature_require_at_origin_to_all_pages,
                    supervisor_signature_require_at_destination_to_all_pages: values.supervisor_signature_require_at_destination_to_all_pages,
                    shipment_job_id: this.state.shipment_job_id,
                    nextAdditionalStageId: values.assign_storage_units_to_items || values.unassign_storage_units_from_items ? this.state.nextAdditionalStageId : null,
                };

                if (!err) {
                    const data = [];

                    data["data"] = params;

                    if (this.state.updateOtherStageDetails.length) {
                        data["data"]['updateOtherStageDetails'] = this.state.updateOtherStageDetails;
                    }

                    this.setState({ loading: true });

                    API.post("api/admin/shipment-type-for-shipment/edit-shipment-stage", data).then((response) => {
                        if (response) {
                            this.setState({ loading: false });
                            message.success(response.message);
                            this.props.history.goBack();
                        } else {
                            this.setState({ loading: false });
                            message.error(response.message);
                        }
                    });
                }
            }
        });

    };



    render() {
        const { getFieldDecorator } = this.props.form;
        const { shipmentTypeStageData } = this.state;
        const formItemLayout = {
            labelCol: {
                xs: {
                    span: 24,
                },
                sm: {
                    span: 5,
                },
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: {
                    span: 12,
                    offset: 1,
                },
                md: {
                    span: 12,
                    offset: 1,
                },
                lg: {
                    span: 12,
                    offset: 1,
                },
                xl: {
                    span: 10,
                    offset: 1,
                },
            },
        };
        const tailFormItemLayout = {
            wrapperCol: {
                xs: {
                    span: 24,
                    offset: 6,
                },
                sm: {
                    span: 16,
                    offset: 6,
                },
                xl: {
                    offset: 6,
                },
            },
        };

        return (
            <LayoutContentWrapper>
                <div className='add_header'>
                    <h2 style={{ marginBottom: "0" }}>
                        &emsp;View Shipment Type Stage
                    </h2>
                    <button className='backButton' onClick={() => this.props.history.goBack()}>
                        <i className='fa fa-chevron-left' aria-hidden='true' /> Back
                    </button>
                </div>
                <LayoutContent>
                    <div>
                        <Spin spinning={this.state.contentloader} indicator={antIcon}>
                            <Form {...formItemLayout} onSubmit={this.handleSubmit}>
                                <Form.Item label='Stage name'>
                                    {getFieldDecorator("name", {
                                        initialValue: shipmentTypeStageData && shipmentTypeStageData.name,
                                        rules: [
                                            {
                                                required: true,
                                                message: "Please input stage name!",
                                                whitespace: true,
                                            },
                                        ],
                                    })(<Input disabled />)}
                                </Form.Item>

                                <Form.Item label='Stage Order'>
                                    {getFieldDecorator("order_of_stages", {
                                        initialValue: shipmentTypeStageData && shipmentTypeStageData.order_of_stages,
                                        rules: [
                                            {
                                                required: true,
                                                message: "Please input stage order!",
                                            },
                                        ],
                                    })(<Input disabled placeholder='Stage Order' />)}
                                </Form.Item>


                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator("add_items_to_inventory", {
                                        valuePropName: "checked",
                                        initialValue: shipmentTypeStageData && shipmentTypeStageData.add_items_to_inventory,
                                    })(<Checkbox
                                        disabled
                                    >Add Items To Inventory</Checkbox>)}
                                </Form.Item>

                                {this.state.isMakeAddScanVisible && (
                                    <Form.Item
                                        label=" "
                                        colon={false}
                                    >
                                        {getFieldDecorator("scan_require", {
                                            valuePropName: "checked",
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.scan_require,
                                        })(<Checkbox
                                            disabled
                                        >Additional Scan</Checkbox>)}
                                    </Form.Item>
                                )}

                                {this.state.isMakeDefaultManualLabelVisible && (
                                    <Form.Item
                                        label=" "
                                        colon={false}
                                    >
                                        {getFieldDecorator("allow_default_manual_label", {
                                            valuePropName: "checked",
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.allow_default_manual_label,
                                        })(<Checkbox
                                            disabled
                                        >Default Manual Label Mode</Checkbox>)}
                                    </Form.Item>
                                )}

                                {
                                    this.state.integrationKeyStatus ?
                                        (
                                            <Form.Item
                                                label=" "
                                                colon={false}
                                            >
                                                {getFieldDecorator('assign_storage_units_to_items', {
                                                    valuePropName: 'checked',
                                                    initialValue: shipmentTypeStageData && shipmentTypeStageData.assign_storage_units_to_items,
                                                })(
                                                    <Checkbox
                                                        disabled
                                                    >
                                                        Add Items To Storage
                                                    </Checkbox>
                                                )}
                                            </Form.Item>
                                        ) : (
                                            ''
                                        )
                                }

                                {
                                    this.state.integrationKeyStatus ?
                                        (
                                            <Form.Item
                                                label=" "
                                                colon={false}
                                            >
                                                {getFieldDecorator('unassign_storage_units_from_items', {
                                                    valuePropName: 'checked',
                                                    initialValue: shipmentTypeStageData && shipmentTypeStageData.unassign_storage_units_from_items,
                                                })(
                                                    <Checkbox
                                                        disabled
                                                    >
                                                        Remove Items From Storage
                                                    </Checkbox>
                                                )}
                                            </Form.Item>
                                        ) : (
                                            ''
                                        )
                                }

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator("remove_items_to_inventory", {
                                        valuePropName: "checked",
                                        initialValue: shipmentTypeStageData && shipmentTypeStageData.remove_items_to_inventory,
                                    })(<Checkbox
                                        disabled
                                    >Remove Items From Inventory</Checkbox>)}
                                </Form.Item>

                                {this.state.isMakeRemoveScanVisible && (
                                    <Form.Item
                                        label=" "
                                        colon={false}
                                    >
                                        {getFieldDecorator("remove_scan_require", {
                                            valuePropName: "checked",
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.remove_scan_require,
                                        })(<Checkbox
                                            disabled
                                        >Additional Scan</Checkbox>)}
                                    </Form.Item>
                                )}


                                {this.state.isMakeEnablePartialComplete && (
                                    <Form.Item
                                        label=" "
                                        colon={false}
                                    >
                                        {getFieldDecorator("enable_partial_complete_stage", {
                                            valuePropName: "checked",
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.enable_partial_complete_stage,
                                        })(<Checkbox
                                            disabled={!this.state.allowEditEnablePartialCompleteStage}
                                        >Enable Partial Complete Stage</Checkbox>)}
                                    </Form.Item>
                                )}

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator("is_add_exceptions", {
                                        valuePropName: "checked",
                                        initialValue: shipmentTypeStageData && shipmentTypeStageData.is_add_exceptions,
                                    })(<Checkbox
                                        disabled
                                    >Make Exceptions Mandatory</Checkbox>)}
                                </Form.Item>

                                {this.state.isMakeExceptions && (
                                    <Form.Item
                                        label=" "
                                        colon={false}
                                    >
                                        {getFieldDecorator("show_no_exceptions", {
                                            valuePropName: "checked",
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.show_no_exceptions,
                                        })(<Checkbox disabled>Show No Exceptions</Checkbox>)}
                                    </Form.Item>
                                )}

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator("PDF_time_require", {
                                        valuePropName: "checked",
                                        initialValue: shipmentTypeStageData && shipmentTypeStageData.PDF_time_require,
                                    })(<Checkbox disabled>Include Signature Time In PDF</Checkbox>)}
                                </Form.Item>

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator("supervisor_signature_require", {
                                        valuePropName: "checked",
                                        initialValue: shipmentTypeStageData && shipmentTypeStageData.supervisor_signature_require,
                                    })(<Checkbox disabled>Carrier Signature Required</Checkbox>)}
                                </Form.Item>

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator(
                                        `supervisor_signature_require_at_origin_to_all_pages`,
                                        {
                                            valuePropName: "checked",
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.supervisor_signature_require_at_origin_to_all_pages,
                                        }
                                    )(<Checkbox
                                        disabled
                                    >Apply this signature at origin to all pages of the inventory</Checkbox>)}
                                </Form.Item>

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator(
                                        `supervisor_signature_require_at_destination_to_all_pages`,
                                        {
                                            valuePropName: "checked",
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.supervisor_signature_require_at_destination_to_all_pages,
                                        }
                                    )(<Checkbox
                                        disabled
                                    >Apply this signature at destination to all pages of the inventory</Checkbox>)}
                                </Form.Item>

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator(`why_supervisor_signature_require_note`,
                                        {
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.why_supervisor_signature_require_note
                                        }
                                    )(< TextArea disabled rows={5} placeholder='Carrier Signature Note' maxLength={1000} />)}
                                </Form.Item>

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator("customer_signature_require", {
                                        valuePropName: "checked",
                                        initialValue: shipmentTypeStageData && shipmentTypeStageData.customer_signature_require,
                                    })(<Checkbox disabled>Customer Signature Required</Checkbox>)}
                                </Form.Item>

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator(
                                        `customer_signature_require_at_origin_to_all_pages`,
                                        {
                                            valuePropName: "checked",
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.customer_signature_require_at_origin_to_all_pages,
                                        }
                                    )(<Checkbox
                                        disabled
                                    >Apply this signature at origin to all pages of the inventory</Checkbox>)}
                                </Form.Item>

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator(
                                        `customer_signature_require_at_destination_to_all_pages`,
                                        {
                                            valuePropName: "checked",
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.customer_signature_require_at_destination_to_all_pages,
                                        }
                                    )(<Checkbox
                                        disabled
                                    >Apply this signature at destination to all pages of the inventory</Checkbox>)}
                                </Form.Item>

                                <Form.Item
                                    label=" "
                                    colon={false}
                                >
                                    {getFieldDecorator(`why_customer_signature_require_note`,
                                        {
                                            initialValue: shipmentTypeStageData && shipmentTypeStageData.why_customer_signature_require_note
                                        }
                                    )(< TextArea disabled rows={5} placeholder='Customer Signature Note' maxLength={1000} />)}
                                </Form.Item>
                                <Form.Item {...tailFormItemLayout}>
                                    <Button
                                        className='submitButton'
                                        loading={this.state.loading}
                                        type='primary'
                                        htmlType='submit'>
                                        Submit
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Spin>
                    </div>
                </LayoutContent>
            </LayoutContentWrapper>
        );
    }
}

export default Form.create()(connect(null, { changeCurrent })(ViewShipmentTypeStage));
