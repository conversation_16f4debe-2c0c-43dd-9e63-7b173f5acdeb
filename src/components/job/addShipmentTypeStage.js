import React from "react";
import { connect } from "react-redux";

import { Button, Form, Icon, Input, Spin, message, Modal, Checkbox, InputNumber, Select, Tooltip } from "antd";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import Api from "../../api/api-handler";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';
import { find } from 'lodash';


const { changeCurrent } = appActions;
const { TextArea } = Input;
const { Option } = Select;

const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddShipmentType extends React.Component {
	state = {
		contentloader: false,
		order_of_stages: 0,
		local_shipment_type_id: "",
		loading: false,
		integrationKeyStatus: false,
		integrationKey: "",
		warehouseId: "",
		storageShipmentJobId: "",
		trueCheckBox: false,
		allStageDetails: [],
		isScanIntoStoragePresent: false,
		pdf_time_stamp_checked: false,
		isMakeExceptions: false,
		isMakeEnablePartialComplete: false,
		currentJobStageNumber: 1,
		findSupervisorSignatureRequireAtOrigin: false,
		findSupervisorSignatureRequireAtDestination: false,
		findCustomerSignatureRequireAtOrigin: false,
		findCustomerSignatureRequireAtDestination: false,
		isMakeDefaultManualLabelVisible: false,
		isMakeRemoveScanVisible: false,
		isMakeAddScanVisible: false,
		isRemoveItemFromInvnetoryChecked: false,
		additionalValidationModal: false,

	}

	componentDidMount() {
		if (this.props && this.props.location && this.props.location.state && this.props.location.state.inventoryData) {
			const checks = this.props.location.state.shipmnetTypeInfo;
			const jobStatus = this.props.location.state.inventoryData.is_job_complete_flag
			const warehouseId = this.props.location.state.inventoryData.warehouseId;
			const storageShipmentJobId = this.props.location.state.inventoryData.storage_shipment_job_id;
			const getCurrentStageNumber = this.props.location.state.inventoryData.shipment_type_for_shipment.current_job_stage
			let isScanIntoStoragePresent = false;
			const findScanIntoStorageRecord = find(checks, ['scan_into_storage', 1]);

			checks.forEach((newData) => {
				if (newData.supervisor_signature_require_at_origin_to_all_pages === "yes") {
					this.setState({
						findSupervisorSignatureRequireAtOrigin: true
					})
				}
				if (newData.supervisor_signature_require_at_destination_to_all_pages === "yes") {
					this.setState({
						findSupervisorSignatureRequireAtDestination: true
					})
				}
				if (newData.customer_signature_require_at_origin_to_all_pages === "yes") {
					this.setState({
						findCustomerSignatureRequireAtOrigin: true
					})
				}
				if (newData.customer_signature_require_at_destination_to_all_pages === "yes") {
					this.setState({
						findCustomerSignatureRequireAtDestination: true
					})
				}
			});

			if (findScanIntoStorageRecord) {
				isScanIntoStoragePresent = true;
			}

			this.setState({
				isJobComplete: jobStatus,
				local_shipment_type_id: checks[0].local_shipment_type_id,
				shipment_job_id: checks[0].shipment_job_id,
				order_of_stages: checks.length,
				warehouseId: warehouseId,
				storageShipmentJobId: storageShipmentJobId,
				allStageDetails: checks,
				isScanIntoStoragePresent: isScanIntoStoragePresent,
				currentJobStageNumber: getCurrentStageNumber,
				pdf_time_stamp_checked: localStorage.getItem("pdf_time_stamp_checked")
					&& localStorage.getItem("pdf_time_stamp_checked") == 1 ? true
					: false
			});
		}

		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							integrationKeyStatus: true,
							integrationKey: response.data.integration_key
						});
					} else {
						this.setState({
							integrationKeyStatus: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}

	addShipmentJson = async (shipmentInfo, accessToken, fetchCompanyResponse, addCustomerResponse) => {
		const addShipmentJson = JSON.stringify({
			shipmentName: shipmentInfo.shipment_name,
			customerId: addCustomerResponse.data.data.id,
			warehouseId: fetchCompanyResponse.data.data.warehouses[0].id,
			contactReference: shipmentInfo.contact_reference,
			accountReference: shipmentInfo.account_reference,
			opportunityTitle: "",
			opportunityReference: shipmentInfo.opportunity_reference,
			estArrival: shipmentInfo.pickup_date,
			estDelivery: shipmentInfo.delivery_date,
			workOrderNotes: [

			],
			workOrderReference: shipmentInfo.wo_reference,
			externalReference: [

			],
			moveCoord: "",
			source: shipmentInfo.source,
			volume: shipmentInfo.estimated_volume,
			weight: shipmentInfo.estimated_weight,
			estUnits: "",
			originAddress: {
				addressLine1: shipmentInfo.pickup_address,
				addressLine2: shipmentInfo.pickup_address2,
				city: shipmentInfo.pickup_city,
				state: shipmentInfo.pickup_state,
				zipcode: shipmentInfo.pickup_zipcod,
				country: shipmentInfo.pickup_country,

			},
			originAddress: {
				addressLine1: shipmentInfo.delivery_address,
				addressLine2: shipmentInfo.delivery_address2,
				city: shipmentInfo.delivery_city,
				state: shipmentInfo.delivery_state,
				zipcode: shipmentInfo.delivery_zipcode,
				country: shipmentInfo.delivery_country,
			},
			importedTags: [],
			moverInventoryShipmentId: shipmentInfo.job_id,
		});

		axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/shipments`, addShipmentJson,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': accessToken
				}
			})
			.then((addShipmentResponse) => {
				this.setState({ contentloader: false, loading: false });
				let params = {
					shipment_job_id: shipmentInfo.job_id,
					shipmentIdStorage: addShipmentResponse.data.data.id,
					warehouseIdStorage: addShipmentResponse.data.data.warehouseId
				};

				const data = [];
				data["data"] = params;


				API.post("api/admin/shipment/storageId/update", data)
					.then((storageIdUpdate) => {
						if (storageIdUpdate.status === 1) {
							this.setState({ contentloader: false, loading: false });
							message.success("added successfully")
							this.props.history.goBack();

						} else {
							this.setState({ loading: false });
							message.error(storageIdUpdate.message);
						}
					})
					.catch((error) => {
						message.error(error.message);
						this.setState({ loading: false });
					});
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false });
			});
	}

	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (this.state.isMakeAddScanCheckedCurrentStage || this.state.isMakeRemoveScanCheckedCurrentStage) {
				this.setState({
					additionalValidationModal: true
				})
			}
			else {
				let params = {
					name: values.name,
					order_of_stages: values.order_of_stages.key,
					scan_require: values.scan_require,
					scan_into_storage: values.scan_into_storage,
					remove_scan_require: values.remove_scan_require,
					scan_out_of_storage: values.scan_out_of_storage,
					is_add_item: values.scan_into_storage == true ? true : values.is_add_item,
					is_add_exceptions: values.is_add_exceptions,
					allow_default_manual_label: values.allow_default_manual_label,
					add_items_to_inventory: values.add_items_to_inventory,
					assign_storage_units_to_items: values.assign_storage_units_to_items,
					unassign_storage_units_from_items: values.unassign_storage_units_from_items,
					remove_items_to_inventory: values.remove_items_to_inventory,
					enable_partial_complete_stage: values.enable_partial_complete_stage,
					show_no_exceptions: values.is_add_exceptions ? (values.show_no_exceptions !== undefined && values.show_no_exceptions !== null && values.show_no_exceptions !== "") ? values.show_no_exceptions : false : false,
					PDF_time_require: values.PDF_time_require,
					supervisor_signature_require: values.supervisor_signature_require,
					customer_signature_require: values.customer_signature_require,
					why_customer_signature_require_note: values.why_customer_signature_require_note,
					why_supervisor_signature_require_note: values.why_supervisor_signature_require_note,
					local_shipment_type_id: this.state.local_shipment_type_id,
					shipment_job_id: this.state.shipment_job_id,
					customer_signature_require_at_origin_to_all_pages: values.customer_signature_require_at_origin_to_all_pages,
					customer_signature_require_at_destination_to_all_pages: values.customer_signature_require_at_destination_to_all_pages,
					supervisor_signature_require_at_origin_to_all_pages: values.supervisor_signature_require_at_origin_to_all_pages,
					supervisor_signature_require_at_destination_to_all_pages: values.supervisor_signature_require_at_destination_to_all_pages,
					isJobComplete: this.state.isJobComplete
				};

				if (!err) {
					const data = [];
					data["data"] = params;
					this.setState({ loading: true });
					API.post("api/admin/shipment-type-stage/add", data).then((response) => {
						if (response.status === 1) {
							this.setState({ loading: false });
							message.success(response.message);
							this.props.history.goBack();
						}
						else {
							this.setState({ loading: false });
							message.error(response.message);
						}
					});
				}
			}
		});
	};

	checkBoxHandler = (event, fieldName) => {
		const { checked } = event.target;
		if (fieldName === 'remove_scan_require' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: true });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false,
				isMakeDefaultManualLabelVisible: false
			})
		}
		else if (fieldName === 'scan_require' && checked) {
			this.props.form.setFieldsValue({ scan_require: true });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false,
				isMakeDefaultManualLabelVisible: false
			})
		}
		else if (fieldName === 'add_items_to_inventory' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: true });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false,
				isMakeDefaultManualLabelVisible: true
			})
		}
		else if (fieldName === 'assign_storage_units_to_items' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: true });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false,
				isMakeDefaultManualLabelVisible: false
			})
		}
		else if (fieldName === 'unassign_storage_units_from_items' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: true });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: true,
				isMakeDefaultManualLabelVisible: false,
				isRemoveItemFromInvnetoryChecked: false
			})
		}
		else if (fieldName === 'remove_items_to_inventory' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: true });
			this.setState({
				isMakeEnablePartialComplete: true,
				isMakeDefaultManualLabelVisible: false,
				isRemoveItemFromInvnetoryChecked: true
			})
		}
		else {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false,
				isMakeDefaultManualLabelVisible: false
			})
		}
	}

	isMakeExceptionsHandler = (event) => {
		if (event.target.checked) {
			this.setState({
				isMakeExceptions: true
			})
		}
		else {
			this.setState({
				isMakeExceptions: false,
			})
		}
	}

	OrderChangeHandler = (e) => {
		let find = e.key - 1
		const checks = this.props.location.state.shipmnetTypeInfo;
		function findOrder(checks, find) {
			for (let i = 0; i < checks.length; i++) {
				if (checks[i].order_of_stages === find) {
					return checks[i];
				}
			}
			return null
		}
		let result = findOrder(checks, find);
		let currentStageData = findOrder(checks, e.key);

		this.setState({
			isMakeAddScanCheckedCurrentStage: currentStageData ? currentStageData.scan_require : false,
			isMakeRemoveScanCheckedCurrentStage: currentStageData ? currentStageData.remove_scan_require : false,
		})

		if (result.add_items_to_inventory) {
			this.setState({
				isMakeAddScanVisible: true
			})
		}
		if (result.remove_items_to_inventory) {
			this.setState({
				isMakeRemoveScanVisible: true
			})
		}

		if (!result.add_items_to_inventory) {
			this.setState({
				isMakeAddScanVisible: false
			})
		}
		if (!result.remove_items_to_inventory) {
			this.setState({
				isMakeRemoveScanVisible: false
			})
		}
	};

	handleCancel = () => {
		this.setState({ additionalValidationModal: false, loading: false });
	};

	render() {
		const { currentJobStageNumber, allStageDetails } = this.state;
		const keyValueArray = [];

		for (let i = currentJobStageNumber + 1; i <= allStageDetails.length + 1; i++) {
			const key = i;
			const value = i;
			keyValueArray.push({ key, value });
		}
		const { getFieldDecorator } = this.props.form;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add Shipment Type
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent style={{ margin: "0 20px", height: "93%", overflowY: "auto" }}>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>

								<div>
									<div>
										<Form.Item label='Stage Name'>
											{getFieldDecorator(`name`, {
												placeholder: "Stage Name",
												rules: [
													{
														required: true,
														message: "Please input stage name!",
														whitespace: true,
													},
												],
											})(<Input placeholder='Stage Name' />)}
										</Form.Item>

										{/* <Form.Item label='Stage Order'>
											{getFieldDecorator(`order_of_stages`, {
												initialValue: this.state.order_of_stages + 1
											})(<Input disabled placeholder='Stage Order' />)}
										</Form.Item> */}

										<Form.Item label='Stage Order'>
											{getFieldDecorator("order_of_stages", {
												rules: [
													{
														required: true,
														message: "Please select stage order",
													},
												],
											})(
												<Select
													size={"default"}
													labelInValue
													placeholder='Select Stage Order'
													style={{ width: "100%" }}
													onChange={this.OrderChangeHandler}
												>
													{keyValueArray.map((e) => (
														<Option key={e.key} value={e.value}>
															{e.value}
														</Option>
													))
													}
												</Select>
											)}

										</Form.Item>

										<Form.Item
											label=" "
											colon={false}>
											<h3>Stage Activities</h3>
										</Form.Item>

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator("add_items_to_inventory", {
												valuePropName: "checked",
											})(<Checkbox
												onChange={(e) => { this.checkBoxHandler(e, 'add_items_to_inventory') }}
											>
												<Tooltip
													title="This option is generally used for the first scan when creating a digital inventory or adding additional items to the inventory later.">
													Add Items To Inventory &nbsp;
													<Icon type="info-circle" />
												</Tooltip>

											</Checkbox>)}
										</Form.Item>

										{this.state.isMakeAddScanVisible && (
											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator("scan_require", {
													valuePropName: "checked",
												})(<Checkbox
													onChange={(e) => { this.checkBoxHandler(e, 'scan_require') }}
												>
													<Tooltip
														title="This option is generally used if an additional scan is required, such as when loading items into the truck.  The additional scan required option is only available after adding or removing items from inventory, not for storage.">
														Additional Scan&nbsp;
														<Icon type="info-circle" />

													</Tooltip>
												</Checkbox>)}
											</Form.Item>
										)}
										{this.state.isMakeDefaultManualLabelVisible && (
											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator("allow_default_manual_label", {
													valuePropName: "checked",
												})(<Checkbox
												>Default Manual Label Mode</Checkbox>)}
											</Form.Item>
										)}

										{
											this.state.integrationKeyStatus ?
												(
													<Form.Item
														label=" "
														colon={false}
													>
														{getFieldDecorator(
															`assign_storage_units_to_items`,
															{ valuePropName: "checked" }
														)(
															<Checkbox
																onChange={(e) => { this.checkBoxHandler(e, 'assign_storage_units_to_items') }}
															>
																<Tooltip
																	title="This option is generally used during 'Storage In' to scan items into your warehouse/storage facilities’ racks, shelves, bays, vaults, etc., or to a staging area.">
																	Add Items To Storage&nbsp;
																	<Icon type="info-circle" />
																</Tooltip>
															</Checkbox>
														)}
													</Form.Item>
												) : (
													''
												)
										}

										{
											this.state.integrationKeyStatus ?
												(
													<Form.Item
														label=" "
														colon={false}
													>

														{getFieldDecorator(
															`unassign_storage_units_from_items`,
															{ valuePropName: "checked" }
														)(
															<Checkbox
																onChange={(e) => { this.checkBoxHandler(e, 'unassign_storage_units_from_items') }}
															>
																<Tooltip
																	title="This option is generally used to scan items out of your warehouse or storage facility during Storage-Out.">
																	Remove Items From Storage&nbsp;
																	<Icon type="info-circle" />
																</Tooltip>
															</Checkbox>
														)}
													</Form.Item>
												) : (
													''
												)
										}

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator("remove_items_to_inventory", {
												valuePropName: "checked",
											})(<Checkbox
												onChange={(e) => { this.checkBoxHandler(e, 'remove_items_to_inventory') }}
											>

												<Tooltip
													title="This option is generally used to remove items from inventory, such as during delivery.">
													Remove Items From Inventory&nbsp;
													<Icon type="info-circle" />
												</Tooltip>
											</Checkbox>)}
										</Form.Item>

										{this.state.isMakeRemoveScanVisible && (
											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator("remove_scan_require", {
													valuePropName: "checked",
												})(<Checkbox
													onChange={(e) => { this.checkBoxHandler(e, 'remove_scan_require') }}
												>
													<Tooltip
														title="This option is generally used if an additional scan is required, such as when unloading items from the truck. The additional scan required option is only available after adding or removing items from inventory, not for storage.">
														Additional Scan&nbsp;
														<Icon type="info-circle" />
													</Tooltip>
												</Checkbox>)}
											</Form.Item>
										)}


										<Form.Item
											label=" "
											colon={false}>
											<h3>Additional Options</h3>
										</Form.Item>

										{this.state.isMakeEnablePartialComplete && (
											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator("enable_partial_complete_stage", {
													valuePropName: "checked",
												})(<Checkbox
												>
													<Tooltip
														title={this.state.isRemoveItemFromInvnetoryChecked ?
															"This option allows partial deliveries, such as when the customer requests delivery of only a portion of the inventory. If you select this option your crew will be able to complete a stage without scanning out all of the items either from inventory or storage. If this check box is not selected, your crew must scan every item out of inventory." : "This option allows partial checkouts from storage, such as when the customer requests delivery of only a portion of the items in storage. If you select this option your crew will be able to complete a stage without scanning out all of the items either from inventory or storage. If this check box is not selected, your crew must scan every item out of inventory. The Enable Partial Complete Stage option is only available when removing items from storage or inventory."}>
														Enable Partial Complete Stage&nbsp;
														<Icon type="info-circle" />
													</Tooltip>

												</Checkbox>)}
											</Form.Item>
										)}

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(`is_add_exceptions`, {
											})(
												<Checkbox
													onChange={e => { this.isMakeExceptionsHandler(e) }}
												>Make Exceptions Mandatory</Checkbox>)}
										</Form.Item>

										{this.state.isMakeExceptions && (
											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator(`show_no_exceptions`, {
													valuePropName: "checked",
												})(
													<Checkbox>Show No Exceptions</Checkbox>)}
											</Form.Item>
										)
										}

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(`PDF_time_require`, {
												valuePropName: "checked",
												initialValue: this.state.pdf_time_stamp_checked
											})(
												<Checkbox disabled={this.state.pdf_time_stamp_checked} >Include Signature Time In PDF</Checkbox>)}
										</Form.Item>

										< Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(
												`supervisor_signature_require`,
											)(<Checkbox>Carrier Signature Required</Checkbox>)}
										</Form.Item>

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(`supervisor_signature_require_at_origin_to_all_pages`,)(
												<Checkbox
													disabled={this.state.findSupervisorSignatureRequireAtOrigin}
												>Apply this signature at origin to all pages of the inventory</Checkbox>)}
										</Form.Item>

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(`supervisor_signature_require_at_destination_to_all_pages`,)(
												<Checkbox
													disabled={this.state.findSupervisorSignatureRequireAtDestination}
												>Apply this signature at destination to all pages of the inventory</Checkbox>)}
										</Form.Item>

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(`why_supervisor_signature_require_note`,
											)(<TextArea rows={5} placeholder='Carrier Signature Note' maxLength={1000} />)}
										</Form.Item>

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(
												`customer_signature_require`,
											)(<Checkbox>Customer Signature Required</Checkbox>)}
										</Form.Item>

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(`customer_signature_require_at_origin_to_all_pages`,
											)(<Checkbox
												disabled={this.state.findCustomerSignatureRequireAtOrigin}
											>Apply this signature at origin to all pages of the inventory</Checkbox>)}
										</Form.Item>

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(
												`customer_signature_require_at_destination_to_all_pages`,
											)(<Checkbox
												disabled={this.state.findCustomerSignatureRequireAtDestination}
											>Apply this signature at destination to all pages of the inventory</Checkbox>)}
										</Form.Item>

										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator(`why_customer_signature_require_note`,
												{}
											)(<TextArea rows={5} placeholder='Customer Signature Note' maxLength={1000} />)}
										</Form.Item>
									</div>
								</div>
								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.additionalValidationModal}
						centered
						maskClosable={false}
						onCancel={this.handleCancel}
						footer={null}
					>
						<p>{`This action is not allowed as the Previous stage is ${this.state.isMakeAddScanCheckedCurrentStage ? "Add items to Inventory" : "Remove items from Inventory"} and the current stage you are trying to replace is Additional Scan. So please add the new stage after Additional Scan or Delete the stages and add new ones according to the requirement.`}</p>
					</Modal>
				}
			</LayoutContentWrapper >
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(AddShipmentType));