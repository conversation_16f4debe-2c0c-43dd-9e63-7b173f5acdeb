import "../../static/css/add.css";

import { Button, Form, Icon, Input, Spin, message, Checkbox, Modal, Tooltip } from "antd";
import Api from "../../api/api-handler";

import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import React from "react";
import appActions from "../../redux/app/actions";
import { connect } from "react-redux";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';

import { orderBy, find, findIndex } from 'lodash';

const { changeCurrent } = appActions;
const { TextArea } = Input;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});

class EditShipmentTypeStage extends React.Component {
	state = {
		shipmentTypeStageData: null,
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		integrationKeyStatus: false,
		integrationKey: "",
		warehouseId: "",
		storageShipmentJobId: "",
		trueCheckBox: false,

		shipmentTypeDetails: [],
		isScanIntoStorageEditable: true,
		isScanOutOfStorageEditable: true,
		updateOtherStageDetails: [],
		pdf_time_stamp_checked: false,
		isMakeExceptions: false,
		isMakeEnablePartialComplete: false,
		isSupervisorSignatureAtOrigin: true,
		isSupervisorSignatureAtDestination: true,
		isCustomerSignatureAtOrigin: true,
		isCustomerSignatureAtDestination: true,
		isMakeDefaultManualLabelVisible: false,
		isMakeAddScanVisible: false,
		isMakeRemoveScanVisible: false,
		shipment_job_id: null,
		isNextStageAddAdditionalScan: false,
		isNextStageRemoveAdditionalScan: false,
		additionalScanModel: false,
		nextAdditionalStageId: null,
		isRemoveItemFromInvnetoryChecked: false
	};

	componentDidMount() {
		if (this.props && this.props.location && this.props.location.state && this.props.location.state.inventoryData) {
			const warehouseId = this.props.location.state.inventoryData.warehouseId;
			const storageShipmentJobId = this.props.location.state.inventoryData.storage_shipment_job_id;
			const ShipmentJobId = this.props.location.state.inventoryData.job_id;
			this.setState({
				shipment_job_id: ShipmentJobId,
				warehouseId: warehouseId,
				storageShipmentJobId: storageShipmentJobId,
				pdf_time_stamp_checked: localStorage.getItem("pdf_time_stamp_checked")
					&& localStorage.getItem("pdf_time_stamp_checked") == 1 ? true
					: false
			})
		}

		const id = this.props.match.params.id;
		this.setState({ contentloader: true, loading: true });

		const data = [];
		data["data"] = { local_shipment_stage_id: id };

		API.post("api/admin/shipment-type-for-shipment/view-shipment-stage", data).then((response) => {
			if (response) {
				if (response.data.is_add_exceptions === 1 || response.data.is_add_exceptions === true) {
					this.setState({ isMakeExceptions: true });
				}
				if (response.data.add_items_to_inventory === 1 || response.data.add_items_to_inventory === true) {
					this.setState({ isMakeDefaultManualLabelVisible: true });
				}
				if (
					response.data.unassign_storage_units_from_items === 1 ||
					response.data.unassign_storage_units_from_items === true ||
					response.data.remove_items_to_inventory === 1 ||
					response.data.remove_items_to_inventory === true
				) {
					this.setState({ isMakeEnablePartialComplete: true });
				}
				let isScanIntoStorageEditable = true;
				let isScanOutOfStorageEditable = true;

				if (response.data.order_of_stages > 1) {
					const check = response.shipmentTypeDetail[response.data.order_of_stages - 2]
					if (check.add_items_to_inventory === 1 || check.add_items_to_inventory === true) {
						this.setState({
							isMakeAddScanVisible: true
						})
					}
					if (check.remove_items_to_inventory === 1 || check.remove_items_to_inventory === true) {
						this.setState({
							isMakeRemoveScanVisible: true
						})
					}
				}

				if (response.shipmentTypeDetail.length > response.data.order_of_stages) {
					this.setState({
						isNextStageAddAdditionalScan: response.shipmentTypeDetail[response.data.order_of_stages].scan_require,
						isNextStageRemoveAdditionalScan: response.shipmentTypeDetail[response.data.order_of_stages].remove_scan_require,
						nextAdditionalStageId: null
					})
				}

				if (response.shipmentTypeDetail) {

					const supervisorSignatureAtOrigin = find(response.shipmentTypeDetail, ['supervisor_signature_require_at_origin_to_all_pages', 1])
					if (supervisorSignatureAtOrigin) {
						if (supervisorSignatureAtOrigin.supervisor_signature_require_at_origin_to_all_pages === response.data.supervisor_signature_require_at_origin_to_all_pages) {
							this.setState({
								isSupervisorSignatureAtOrigin: false
							})
						}
					} else {
						this.setState({
							isSupervisorSignatureAtOrigin: false
						})
					}
					const supervisorSignatureAtDestination = find(response.shipmentTypeDetail, ['supervisor_signature_require_at_destination_to_all_pages', 1])
					if (supervisorSignatureAtDestination) {
						if (supervisorSignatureAtDestination.supervisor_signature_require_at_destination_to_all_pages === response.data.supervisor_signature_require_at_destination_to_all_pages) {
							this.setState({
								isSupervisorSignatureAtDestination: false
							})
						}
					}
					else {
						this.setState({
							isSupervisorSignatureAtDestination: false
						})
					}
					const customerSignatureAtOrigin = find(response.shipmentTypeDetail, ['customer_signature_require_at_origin_to_all_pages', 1])
					if (customerSignatureAtOrigin) {
						if (customerSignatureAtOrigin.customer_signature_require_at_origin_to_all_pages === response.data.customer_signature_require_at_origin_to_all_pages) {
							this.setState({
								isCustomerSignatureAtOrigin: false
							})
						}
					}
					else {
						this.setState({
							isCustomerSignatureAtOrigin: false
						})
					}
					const customerSignatureAtDestination = find(response.shipmentTypeDetail, ['customer_signature_require_at_destination_to_all_pages', 1])
					if (customerSignatureAtDestination) {
						if (customerSignatureAtDestination.customer_signature_require_at_destination_to_all_pages === response.data.customer_signature_require_at_destination_to_all_pages) {
							this.setState({
								isCustomerSignatureAtDestination: false
							})
						}
					}
					else {
						this.setState({
							isCustomerSignatureAtDestination: false
						})
					}

					const findScanIntoStorageDetails = find(response.shipmentTypeDetail, ['scan_into_storage', 1]);
					if (findScanIntoStorageDetails) {
						if (findScanIntoStorageDetails.local_shipment_stage_id !== response.data.local_shipment_stage_id) {
							isScanIntoStorageEditable = false;
						}
					}

					const findScanOutOfStorageIndex = findIndex(response.shipmentTypeDetail, ['scan_out_of_storage', 1]);
					if (findScanOutOfStorageIndex !== -1) {
						if (response.shipmentTypeDetail[findScanOutOfStorageIndex].local_shipment_stage_id === response.data.local_shipment_stage_id) {
							isScanOutOfStorageEditable = false;
						}
					}

					const sortByOrderofStages = orderBy(response.shipmentTypeDetail, 'order_of_stages');
					if (response.data.local_shipment_stage_id === sortByOrderofStages[sortByOrderofStages.length - 1].local_shipment_stage_id) {
						isScanIntoStorageEditable = false;
					}
				}

				this.setState(
					{
						shipmentTypeStageData: response.data && response.data,
						contentloader: false,
						loading: false,
						shipmentTypeDetails: response.shipmentTypeDetail && response.shipmentTypeDetail,
						isScanIntoStorageEditable: isScanIntoStorageEditable,
						isScanOutOfStorageEditable: isScanOutOfStorageEditable
					},
					() => this.props.form.setFieldsValue({})
				);
			} else {
				message.error(response.message);
				this.setState({ contentloader: false, loading: false });
			}
		});

		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							integrationKeyStatus: true,
							integrationKey: response.data.integration_key
						});
					} else {
						this.setState({
							integrationKeyStatus: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

	}

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};

	handleUpload = ({ fileList }) => {

		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};

	addShipmentJson = async (shipmentInfo, accessToken, fetchCompanyResponse, addCustomerResponse) => {
		const addShipmentJson = JSON.stringify({
			shipmentName: shipmentInfo.shipment_name,
			customerId: addCustomerResponse.data.data.id,
			warehouseId: fetchCompanyResponse.data.data.warehouses[0].id,
			contactReference: shipmentInfo.contact_reference,
			accountReference: shipmentInfo.account_reference,
			opportunityTitle: "",
			opportunityReference: shipmentInfo.opportunity_reference,
			estArrival: shipmentInfo.pickup_date,
			estDelivery: shipmentInfo.delivery_date,
			workOrderNotes: [

			],
			workOrderReference: shipmentInfo.wo_reference,
			externalReference: [

			],
			moveCoord: "",
			source: shipmentInfo.source,
			volume: shipmentInfo.estimated_volume,
			weight: shipmentInfo.estimated_weight,
			estUnits: "",
			originAddress: {
				addressLine1: shipmentInfo.pickup_address,
				addressLine2: shipmentInfo.pickup_address2,
				city: shipmentInfo.pickup_city,
				state: shipmentInfo.pickup_state,
				zipcode: shipmentInfo.pickup_zipcod,
				country: shipmentInfo.pickup_country,

			},
			originAddress: {
				addressLine1: shipmentInfo.delivery_address,
				addressLine2: shipmentInfo.delivery_address2,
				city: shipmentInfo.delivery_city,
				state: shipmentInfo.delivery_state,
				zipcode: shipmentInfo.delivery_zipcode,
				country: shipmentInfo.delivery_country,
			},
			importedTags: [],
			moverInventoryShipmentId: shipmentInfo.job_id,
		});

		axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/shipments`, addShipmentJson,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': accessToken
				}
			})
			.then((addShipmentResponse) => {

				this.setState({ contentloader: false, loading: false });
				let params = {
					shipment_job_id: shipmentInfo.job_id,
					shipmentIdStorage: addShipmentResponse.data.data.id,
					warehouseIdStorage: addShipmentResponse.data.data.warehouseId
				};

				const data = [];
				data["data"] = params;


				API.post("api/admin/shipment/storageId/update", data)
					.then((storageIdUpdate) => {
						if (storageIdUpdate.status === 1) {
							this.setState({ contentloader: false, loading: false });
							message.success("update successfully")
							this.props.history.goBack();

						} else {
							this.setState({ loading: false });
							message.error(storageIdUpdate.message);
						}
					})
					.catch((error) => {
						message.error(error.message);
						this.setState({ loading: false });
					});
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false });
			});
	}

	checkShipmentExistJson = async (shipmentInfo, accessToken, fetchCompanyResponse, addCustomerResponse) => {
		const importShipmentId = shipmentInfo.shipment_job_id
		axios.get(`${MOVER_STORAGE_API_URL}import/mover-inventory/shipment-detail/${importShipmentId}`,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': accessToken
				}
			})
			.then((checkShipmentExistResponse) => {
				this.setState({ contentloader: false, loading: false });
				message.success("update successfully")
				this.props.history.goBack();
			})
			.catch((error) => {
				this.addShipmentJson(shipmentInfo, accessToken, fetchCompanyResponse, addCustomerResponse)
			});
	}

	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {

			if (this.state.isNextStageAddAdditionalScan && !values.add_items_to_inventory) {
				this.setState({
					additionalScanModel: true
				})
			}
			else if (this.state.isNextStageRemoveAdditionalScan && !values.remove_items_to_inventory) {
				this.setState({
					additionalScanModel: true
				})
			}
			else {
				let params = {
					local_shipment_stage_id: this.props.match.params.id,
					name: values.name,
					order_of_stages: values.order_of_stages,
					scan_require: values.scan_require,
					remove_scan_require: values.remove_scan_require,
					scan_into_storage: values.scan_into_storage,
					scan_out_of_storage: values.scan_out_of_storage,
					is_add_exceptions: values.is_add_exceptions,
					allow_default_manual_label: values.add_items_to_inventory ? values.allow_default_manual_label : false,
					add_items_to_inventory: values.add_items_to_inventory,
					assign_storage_units_to_items: values.assign_storage_units_to_items,
					unassign_storage_units_from_items: values.unassign_storage_units_from_items,
					remove_items_to_inventory: values.remove_items_to_inventory,
					enable_partial_complete_stage: values.remove_items_to_inventory || values.unassign_storage_units_from_items ? values.enable_partial_complete_stage : false,
					show_no_exceptions: values.is_add_exceptions ? values.show_no_exceptions : false,
					PDF_time_require: values.PDF_time_require,
					is_add_item: values.scan_into_storage == true ? true : values.is_add_item,
					supervisor_signature_require: values.supervisor_signature_require,
					customer_signature_require: values.customer_signature_require,
					why_supervisor_signature_require_note: values.why_supervisor_signature_require_note,
					why_customer_signature_require_note: values.why_customer_signature_require_note,
					customer_signature_require_at_origin_to_all_pages: values.customer_signature_require_at_origin_to_all_pages,
					customer_signature_require_at_destination_to_all_pages: values.customer_signature_require_at_destination_to_all_pages,
					supervisor_signature_require_at_origin_to_all_pages: values.supervisor_signature_require_at_origin_to_all_pages,
					supervisor_signature_require_at_destination_to_all_pages: values.supervisor_signature_require_at_destination_to_all_pages,
					shipment_job_id: this.state.shipment_job_id,
					nextAdditionalStageId: values.assign_storage_units_to_items || values.unassign_storage_units_from_items ? this.state.nextAdditionalStageId : null,
				};

				if (!err) {
					const data = [];

					data["data"] = params;

					if (this.state.updateOtherStageDetails.length) {
						data["data"]['updateOtherStageDetails'] = this.state.updateOtherStageDetails;
					}

					this.setState({ loading: true });

					API.post("api/admin/shipment-type-for-shipment/edit-shipment-stage", data).then((response) => {
						if (response) {
							this.setState({ loading: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false });
							message.error(response.message);
						}
					});
				}
			}
		});

	};



	checkBoxHandler = (event, fieldName) => {
		const { checked } = event.target;
		if (fieldName === 'remove_scan_require' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: true });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false, isMakeDefaultManualLabelVisible: false
			})
		}

		else if (fieldName === 'scan_require' && checked) {
			this.props.form.setFieldsValue({ scan_require: true });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false, isMakeDefaultManualLabelVisible: false
			})
		}
		else if (fieldName === 'add_items_to_inventory' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: true });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false, isMakeDefaultManualLabelVisible: true
			})
		}
		else if (fieldName === 'assign_storage_units_to_items' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: true });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false, isMakeDefaultManualLabelVisible: false
			})
		}
		else if (fieldName === 'unassign_storage_units_from_items' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: true });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: true, isMakeDefaultManualLabelVisible: false, isRemoveItemFromInvnetoryChecked: false
			})
		}
		else if (fieldName === 'remove_items_to_inventory' && checked) {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: true });
			this.setState({
				isMakeEnablePartialComplete: true, isMakeDefaultManualLabelVisible: false, isRemoveItemFromInvnetoryChecked: true
			})
		}
		else {
			this.props.form.setFieldsValue({ scan_require: false });
			this.props.form.setFieldsValue({ remove_scan_require: false });
			this.props.form.setFieldsValue({ add_items_to_inventory: false });
			this.props.form.setFieldsValue({ assign_storage_units_to_items: false });
			this.props.form.setFieldsValue({ unassign_storage_units_from_items: false });
			this.props.form.setFieldsValue({ remove_items_to_inventory: false });
			this.setState({
				isMakeEnablePartialComplete: false, isMakeDefaultManualLabelVisible: false
			})
		}
	}

	isMakeExceptionsHandler = (event) => {
		if (event.target.checked) {
			this.setState({
				isMakeExceptions: true
			})
		}
		else {
			this.setState({
				isMakeExceptions: false,
			})
		}
	}

	handleCancel = () => {
		this.setState({ additionalScanModel: false, loading: false });
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const { shipmentTypeStageData } = this.state;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Edit Shipment Type Stage
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Stage name'>
									{getFieldDecorator("name", {
										initialValue: shipmentTypeStageData && shipmentTypeStageData.name,
										rules: [
											{
												required: true,
												message: "Please input stage name!",
												whitespace: true,
											},
										],
									})(<Input />)}
								</Form.Item>

								<Form.Item label='Stage Order'>
									{getFieldDecorator("order_of_stages", {
										initialValue: shipmentTypeStageData && shipmentTypeStageData.order_of_stages,
										rules: [
											{
												required: true,
												message: "Please input stage order!",
											},
										],
									})(<Input disabled placeholder='Stage Order' />)}
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}>
									<h3>Stage Activities</h3>
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator("add_items_to_inventory", {
										valuePropName: "checked",
										initialValue: shipmentTypeStageData && shipmentTypeStageData.add_items_to_inventory,
									})(<Checkbox
										onChange={(e) => { this.checkBoxHandler(e, 'add_items_to_inventory') }}
									>
										<Tooltip
											title="This option is generally used for the first scan when creating a digital inventory or adding additional items to the inventory later.">
											Add Items To Inventory &nbsp;
											<Icon type="info-circle" />
										</Tooltip>


									</Checkbox>)}
								</Form.Item>

								{
									this.state.isMakeAddScanVisible &&
									(
										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator("scan_require", {
												valuePropName: "checked",
												initialValue: shipmentTypeStageData && shipmentTypeStageData.scan_require,
											})(<Checkbox
												onChange={(e) => { this.checkBoxHandler(e, 'scan_require') }}
											>
												<Tooltip
													title="This option is generally used if an additional scan is required, such as when loading items into the truck.  The additional scan required option is only available after adding or removing items from inventory, not for storage.">
													Additional Scan&nbsp;
													<Icon type="info-circle" />

												</Tooltip>
											</Checkbox>)}
										</Form.Item>
									)

								}

								{this.state.isMakeDefaultManualLabelVisible && (
									<Form.Item
										label=" "
										colon={false}
									>
										{getFieldDecorator("allow_default_manual_label", {
											valuePropName: "checked",
											initialValue: shipmentTypeStageData && shipmentTypeStageData.allow_default_manual_label,
										})(<Checkbox
										>Default Manual Label Mode</Checkbox>)}
									</Form.Item>
								)}

								{
									this.state.integrationKeyStatus ?
										(
											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator('assign_storage_units_to_items', {
													valuePropName: 'checked',
													initialValue: shipmentTypeStageData && shipmentTypeStageData.assign_storage_units_to_items,
												})(
													<Checkbox
														onChange={(e) => { this.checkBoxHandler(e, 'assign_storage_units_to_items') }}
													>
														<Tooltip
															title="This option is generally used during 'Storage In' to scan items into your warehouse/storage facilities’ racks, shelves, bays, vaults, etc., or to a staging area.">
															Add Items To Storage&nbsp;
															<Icon type="info-circle" />
														</Tooltip>
													</Checkbox>
												)}
											</Form.Item>
										) : (
											''
										)
								}

								{
									this.state.integrationKeyStatus ?
										(
											<Form.Item
												label=" "
												colon={false}
											>
												{getFieldDecorator('unassign_storage_units_from_items', {
													valuePropName: 'checked',
													initialValue: shipmentTypeStageData && shipmentTypeStageData.unassign_storage_units_from_items,
												})(
													<Checkbox
														onChange={(e) => { this.checkBoxHandler(e, 'unassign_storage_units_from_items') }}
													>
														<Tooltip
															title="This option is generally used to scan items out of your warehouse or storage facility during Storage-Out.">
															Remove Items From Storage&nbsp;
															<Icon type="info-circle" />
														</Tooltip>
													</Checkbox>
												)}
											</Form.Item>
										) : (
											''
										)
								}

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator("remove_items_to_inventory", {
										valuePropName: "checked",
										initialValue: shipmentTypeStageData && shipmentTypeStageData.remove_items_to_inventory,
									})(<Checkbox
										onChange={(e) => { this.checkBoxHandler(e, 'remove_items_to_inventory') }}
									>
										<Tooltip
											title="This option is generally used to remove items from inventory, such as during delivery.">
											Remove Items From Inventory&nbsp;
											<Icon type="info-circle" />
										</Tooltip>

									</Checkbox>)}
								</Form.Item>

								{
									this.state.isMakeRemoveScanVisible &&
									(
										<Form.Item
											label=" "
											colon={false}
										>
											{getFieldDecorator("remove_scan_require", {
												valuePropName: "checked",
												initialValue: shipmentTypeStageData && shipmentTypeStageData.remove_scan_require,
											})(<Checkbox
												onChange={(e) => { this.checkBoxHandler(e, 'remove_scan_require') }}
											>
												<Tooltip
													title="This option is generally used if an additional scan is required, such as when unloading items from the truck. The additional scan required option is only available after adding or removing items from inventory, not for storage.">
													Additional Scan&nbsp;
													<Icon type="info-circle" />
												</Tooltip>

											</Checkbox>)}
										</Form.Item>
									)

								}


								<Form.Item
									label=" "
									colon={false}>
									<h3>Additional Options</h3>
								</Form.Item>

								{this.state.isMakeEnablePartialComplete && (
									<Form.Item
										label=" "
										colon={false}
									>
										{getFieldDecorator("enable_partial_complete_stage", {
											valuePropName: "checked",
											initialValue: shipmentTypeStageData && shipmentTypeStageData.enable_partial_complete_stage,
										})(<Checkbox
										>
											<Tooltip
												title={this.state.isRemoveItemFromInvnetoryChecked ?
													"This option allows partial deliveries, such as when the customer requests delivery of only a portion of the inventory. If you select this option your crew will be able to complete a stage without scanning out all of the items either from inventory or storage. If this check box is not selected, your crew must scan every item out of inventory." : "This option allows partial checkouts from storage, such as when the customer requests delivery of only a portion of the items in storage. If you select this option your crew will be able to complete a stage without scanning out all of the items either from inventory or storage. If this check box is not selected, your crew must scan every item out of inventory. The Enable Partial Complete Stage option is only available when removing items from storage or inventory."}>
												Enable Partial Complete Stage&nbsp;
												<Icon type="info-circle" />
											</Tooltip>

										</Checkbox>)}
									</Form.Item>
								)}

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator("is_add_exceptions", {
										valuePropName: "checked",
										initialValue: shipmentTypeStageData && shipmentTypeStageData.is_add_exceptions,
									})(<Checkbox
										onChange={e => { this.isMakeExceptionsHandler(e) }}
									>Make Exceptions Mandatory</Checkbox>)}
								</Form.Item>

								{this.state.isMakeExceptions && (
									<Form.Item
										label=" "
										colon={false}
									>
										{getFieldDecorator("show_no_exceptions", {
											valuePropName: "checked",
											initialValue: shipmentTypeStageData && shipmentTypeStageData.show_no_exceptions,
										})(<Checkbox>Show No Exceptions</Checkbox>)}
									</Form.Item>
								)}

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator("PDF_time_require", {
										valuePropName: "checked",
										initialValue: shipmentTypeStageData && shipmentTypeStageData.PDF_time_require,
									})(<Checkbox>Include Signature Time In PDF</Checkbox>)}
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator("supervisor_signature_require", {
										valuePropName: "checked",
										initialValue: shipmentTypeStageData && shipmentTypeStageData.supervisor_signature_require,
									})(<Checkbox>Carrier Signature Required</Checkbox>)}
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator(
										`supervisor_signature_require_at_origin_to_all_pages`,
										{
											valuePropName: "checked",
											initialValue: shipmentTypeStageData && shipmentTypeStageData.supervisor_signature_require_at_origin_to_all_pages,
										}
									)(<Checkbox
										disabled={this.state.isSupervisorSignatureAtOrigin}
									>Apply this signature at origin to all pages of the inventory</Checkbox>)}
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator(
										`supervisor_signature_require_at_destination_to_all_pages`,
										{
											valuePropName: "checked",
											initialValue: shipmentTypeStageData && shipmentTypeStageData.supervisor_signature_require_at_destination_to_all_pages,
										}
									)(<Checkbox
										disabled={this.state.isSupervisorSignatureAtDestination}
									>Apply this signature at destination to all pages of the inventory</Checkbox>)}
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator(`why_supervisor_signature_require_note`,
										{
											initialValue: shipmentTypeStageData && shipmentTypeStageData.why_supervisor_signature_require_note
										}
									)(< TextArea rows={5} placeholder='Carrier Signature Note' maxLength={1000} />)}
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator("customer_signature_require", {
										valuePropName: "checked",
										initialValue: shipmentTypeStageData && shipmentTypeStageData.customer_signature_require,
									})(<Checkbox>Customer Signature Required</Checkbox>)}
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator(
										`customer_signature_require_at_origin_to_all_pages`,
										{
											valuePropName: "checked",
											initialValue: shipmentTypeStageData && shipmentTypeStageData.customer_signature_require_at_origin_to_all_pages,
										}
									)(<Checkbox
										disabled={this.state.isCustomerSignatureAtOrigin}
									>Apply this signature at origin to all pages of the inventory</Checkbox>)}
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator(
										`customer_signature_require_at_destination_to_all_pages`,
										{
											valuePropName: "checked",
											initialValue: shipmentTypeStageData && shipmentTypeStageData.customer_signature_require_at_destination_to_all_pages,
										}
									)(<Checkbox
										disabled={this.state.isCustomerSignatureAtDestination}
									>Apply this signature at destination to all pages of the inventory</Checkbox>)}
								</Form.Item>

								<Form.Item
									label=" "
									colon={false}
								>
									{getFieldDecorator(`why_customer_signature_require_note`,
										{
											initialValue: shipmentTypeStageData && shipmentTypeStageData.why_customer_signature_require_note
										}
									)(< TextArea rows={5} placeholder='Customer Signature Note' maxLength={1000} />)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.additionalScanModel}
						centered
						maskClosable={false}
						onCancel={this.handleCancel}
						footer={null}
					>
						<p>{`This action is not allowed as the Current stage is ${this.state.isNextStageAddAdditionalScan ? "Add items to Inventory" : "Remove items from Inventory"} and next Stage is Additional Scan. So please delete both the stages if you wish to change stage ${shipmentTypeStageData && shipmentTypeStageData.order_of_stages} to something else`}</p>
					</Modal>
				}
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditShipmentTypeStage));
