import { Card, Col, Divider, Form, Icon, Modal, Input, message, Row, Spin, Button, Radio, Alert, Upload } from "antd";
import React from "react";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import QRCode from "qrcode.react";


const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});

class ViewJob extends React.Component {
	state = {
		jobData: null,
		loading: false,
		contentloader: false,
		contentloaderComment: false,
		contentloaderNote: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		inventoryData: {},
		inventoryItemData: {},
		inventoryExceptionData: {},
		forcedInventory: [],
		scannedInventory: [],
		commentsArray: [],
		notesArray: [],
		ImageModal: false,
		ImageValue: "",
		visibleThumbnailModel: false,
		PhotoId: null,
		inventoryTags: [],
		addPhotosModalVisible: false,
		photoFileList: [],
		photoPreviewVisible: false,
		photoPreviewImage: '',
		uploadLoading: false,
		shipmentStage: []
	};
	componentDidMount() {
		const id = this.props.match.params.id;
		this.setState({
			commentId: id,
			shipmentStage: this.props.location.state.shipmentStage
		})
		this.props.changeCurrent("job");
		this.setState({ contentloader: true }, () => {
			this.fetchInventoryDetails(id);
		});
	}

	fetchInventoryDetails = (id) => {
		this.setState({ contentloader: true });
		API.get(`api/admin/inventory/${id}`)
			.then((response) => {
				if (response) {
					console.log("res", response)
					this.setState({
						inventoryData: response.data && response.data,
						commentsArray: response.data && response.data.comments,
						notesArray: response.data && response.data.inventory_notes,
						contentloader: false,
						inventoryItemData: response.data && response.data.item_photos,
						inventoryExceptionData: response.data && response.data.exceptions,
						forcedInventory: response.data && response.data.forced_inventory,
						scannedInventory: response.data && response.data.scanned_inventory,
						inventoryTags: response.data && response.data.item_tag ? response.data.item_tag : [],
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}
	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {
		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};
	// viewInventory(id) {
	// 	this.props.history.push(`/job/view-inventory/${id}`);
	// }
	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {
			let params = {
				reason: values.reason,
				altered_stage_id: this.state.alter_stage_id,
			};
			if (!err) {
				const data = [];

				data["data"] = params;
				this.setState({ loading: true });

				API.put("api/admin/shipment/force_stage_change/" + this.props.match.params.id, data)
					.then((response) => {
						if (response) {
							this.setState({ loading: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false });
					});
			}
		});
	};
	onShowModal = () => {
		this.setState({ showModal: true });
	};
	onCancelModal = () => {
		this.setState({ showModal: false });
	};
	changeValue = (value) => {
		this.setState({ alter_stage_id: value });
	};

	handleImageCancel = () => {
		this.setState({
			ImageModal: !this.state.ImageModal,
		});
	};
	toDate = (date) => {
		let s = new Date(date).toLocaleTimeString([], {
			day: "numeric",
			month: "short",
			year: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
		return s;
	};

	editItemFun() {
		const id = this.props.match.params.id;
		this.props.history.push({
			pathname: `/job/edit-inventory/${id}`,
		});
	}

	openThumbnailModel = (item) => {
		this.setState({
			visibleThumbnailModel: true,
			PhotoId: item.photo_id
		})
	}

	handleThumbnailUpdate = () => {
		const inventory_id = this.props.match.params.id;
		let params = {
			inventory_photo_id: this.state.PhotoId,
			inventory_id: inventory_id
		};
		const data = [];
		data["data"] = params;
		this.setState({ loading: true });
		API.post("api/home/<USER>", data)
			.then((response) => {
				if (response) {
					this.fetchInventoryDetails(inventory_id);
					this.setState({
						loading: false,
						visibleThumbnailModel: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						loading: false,
						visibleThumbnailModel: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({
					loading: false,
					visibleThumbnailModel: false,
				});
			});
	}

	handleThumbnailCancel = () => {
		this.setState({
			visibleThumbnailModel: false,
		});
	}

	showAddPhotosModal = () => {
		this.setState({
			addPhotosModalVisible: true,
			photoFileList: []
		});
	};

	handleAddPhotosCancel = () => {
		this.setState({ addPhotosModalVisible: false });
	};

	handlePhotoPreview = async file => {
		if (!file.url && !file.preview) {
			file.preview = await this.getBase64(file.originFileObj);
		}

		this.setState({
			photoPreviewImage: file.url || file.preview,
			photoPreviewVisible: true,
		});
	};

	handlePhotoPreviewCancel = () => {
		this.setState({ photoPreviewVisible: false });
	};

	getBase64 = (file) => {
		return new Promise((resolve, reject) => {
			const reader = new FileReader();
			reader.readAsDataURL(file);
			reader.onload = () => resolve(reader.result);
			reader.onerror = error => reject(error);
		});
	};

	handlePhotoChange = ({ fileList }) => {
		const filteredFileList = fileList.slice(0, 9);
		this.setState({ photoFileList: filteredFileList });
	};

	handleUploadPhotos = () => {
		this.setState({ contentloader: true });
		const { photoFileList } = this.state;
		const inventoryId = this.props.match.params.id;

		const addItemStage = this.props.location.state.shipmentStage.find(
			(stage) =>
				stage.add_items_to_inventory === 1
		);

		if (photoFileList.length === 0) {
			message.warning("Please select at least one photo to upload");
			this.setState({ contentloader: true });
			return;
		}
		this.setState({ uploadLoading: true });
		const data = [];
		const formData = new FormData();
		formData.append("inventory_id", inventoryId);
		formData.append("current_job_stage", addItemStage.local_shipment_stage_id);
		photoFileList.forEach((file, index) => {
			const fieldName = index === 0 ? "photo" : `photo${index}`;
			console.log("file", file)
			console.log("fieldName", fieldName)
			formData.append(fieldName, file.originFileObj);
		});
		data["data"] = formData;
		API.post("api/home/<USER>/add", data, {
			headers: {
				'Content-Type': 'multipart/form-data'
			}
		})
			.then(response => {
				if (response) {
					message.success("Photos uploaded successfully!");
					this.setState({
						uploadLoading: false,
						addPhotosModalVisible: false,
						photoFileList: []
					});
					this.fetchInventoryDetails(inventoryId);
				} else {
					message.error(response.message || "Failed to upload photos");
					this.setState({ uploadLoading: false });
				}
			})
			.catch(error => {
				message.error("An error occurred while uploading photos");
				this.setState({ uploadLoading: false });
			});
	};

	handleSubmitAddComment = (e) => {
		e.preventDefault();
		const inventoryId = this.props.match.params.id;
		this.props.form.validateFieldsAndScroll(['comment'], (err, values) => {
			let params = {
				comment: values.comment,
				inventoryId
			};

			if (!err) {
				const data = [];

				data["data"] = params;
				this.setState({ contentloaderComment: true });
				API.post("api/general/customer/inventory/add-comment", data)
					.then((response) => {
						if (response.status === 1) {
							this.setState({ contentloaderComment: false });
							message.success(response.message);
							this.props.form.resetFields(['comment']);
							this.fetchInventoryDetails(inventoryId);
						} else {
							this.setState({ contentloaderComment: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ contentloaderComment: false });
					});
			}
		});
	};

	handleSubmitAddNote = (e) => {
		e.preventDefault();
		const inventoryId = this.props.match.params.id;
		this.props.form.validateFieldsAndScroll(['note'], (err, values) => {
			let params = {
				note: values.note,
				inventoryId
			};

			if (!err) {
				const data = [];

				data["data"] = params;
				this.setState({ contentloaderNote: true });
				API.post("api/general/customer/inventory/add-note", data)
					.then((response) => {
						if (response.status === 1) {
							this.setState({ contentloaderNote: false });
							message.success(response.message);
							this.props.form.resetFields(['note']);
							this.fetchInventoryDetails(inventoryId);
						} else {
							this.setState({ contentloaderNote: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ contentloaderNote: false });
					});
			}
		});
	};

	render() {
		const { getFieldDecorator } = this.props.form;


		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Item Details
					</h2>
					<div>
						<Button
							style={{
								marginTop: "0",
								marginRight: "10px"
							}}
							type="primary"
							onClick={this.showAddPhotosModal}
						>
							Add Photos
						</Button>
						<Button
							style={{
								marginTop: "0",
								marginRight: "10px"
							}}
							type='primary'
							onClick={() => this.editItemFun()}>
							Edit Item
						</Button>
						<button className='backButton' onClick={() => this.props.history.goBack()}>
							<i className='fa fa-chevron-left' aria-hidden='true' /> Back
						</button>
					</div>
				</div>


				<LayoutContent>

					<div>
						<Spin spinning={this.state.contentloaderComment} indicator={antIcon}>
							<Form  {...formItemLayout} onSubmit={this.handleSubmitAddComment}>
								<Row>
									<Col className='item_card_box' span={18}>
										<Form.Item label='Customer Comment'>
											{getFieldDecorator("comment", {
												placeholder: "Enter comment",
												rules: [
													{
														required: true,
														message: "Please fill the required field!",
														whitespace: true,
													},
												],
											})(<Input style={{ width: "500px" }} placeholder='Add Comment' />)}
										</Form.Item>
										<Form.Item {...tailFormItemLayout}>
											<Button
												className='submitButton'
												loading={this.state.contentloaderComment}
												type='primary'
												htmlType='submit'>
												Add Comment
											</Button>
										</Form.Item>
									</Col>
								</Row>
							</Form>
						</Spin>
					</div >
					<div>
						<Spin spinning={this.state.contentloaderNote} indicator={antIcon}>
							<Form  {...formItemLayout} onSubmit={this.handleSubmitAddNote}>
								<Row>
									<Col className='item_card_box' span={18}>
										<Form.Item label='Internal Note'>
											{getFieldDecorator("note", {
												placeholder: "Enter note",
												rules: [
													{
														required: true,
														message: "Please fill the required field!",
														whitespace: true,
													},
												],
											})(<Input style={{ width: "500px" }} placeholder='Add Note' />)}
										</Form.Item>
										<Form.Item {...tailFormItemLayout}>
											<Button
												className='submitButton'
												loading={this.state.contentloaderNote}
												type='primary'
												htmlType='submit'>
												Add Note
											</Button>
										</Form.Item>
									</Col>
								</Row>
							</Form>
						</Spin>
					</div >
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Divider orientation='center' type='horizontal'>
								<div className='display-flex'>
									<label className='fs-16 medium-text'>
										<h2>Item Summary </h2>
									</label>
								</div>
							</Divider>

							<Row>
								<Col className='item_card_box' span={11}>
									<p>

										{
											this.state &&
												this.state.inventoryData &&
												this.state.inventoryData.isManualLabel === 1

												||
												this.state &&
												this.state.inventoryData &&
												this.state.inventoryData.isManualLabel === "true"

												||
												this.state &&
												this.state.inventoryData &&
												this.state.inventoryData.isManualLabel === true
												?
												<>
													<b>Manual Label :</b>{" "}
													M-
													{this.state &&
														this.state.inventoryData &&
														this.state.inventoryData.color.charAt(0).toUpperCase()
													}/
													{this.state &&
														this.state.inventoryData &&
														this.state.inventoryData.lot_no
													}/
													{this.state &&
														this.state.inventoryData &&
														this.state.inventoryData.label_no
													}
												</>
												:
												<>
													<b>Label/Qr Code: </b>{" "}
													{this.state.inventoryData &&
														this.state.inventoryData.item_qr &&
														this.state.inventoryData.item_qr.label_number
														? this.state.inventoryData.item_qr.label_number
														: ""}
													/#
													{this.state.inventoryData && this.state.inventoryData.item_qr
														? this.state.inventoryData.item_qr.random_number
														: ""}
												</>
										}
									</p>
									{/* <p>
										<b>QR Code: </b>{" "}
										{this.state.inventoryData &&
											this.state.inventoryData.item_qr
											? this.state.inventoryData.item_qr.random_number
											: ""}
									</p> */}
									<p>
										<b>Item Name/Description: </b> {this.state.inventoryData.item_name}
									</p>

									<p>
										{this.state.inventoryTags.length > 0
											?
											<>
												<b>Item Tags</b>
												{
													this.state.inventoryTags.map((e, i) => (
														<ul>
															<li>{e.name}</li>
														</ul>

													))
												}
											</>
											: null}
									</p>
									<p>
										<b>Added By: </b>{" "}
										{this.state.inventoryData.prepared_staff &&
											this.state.inventoryData.prepared_staff.first_name +
											" " +
											this.state.inventoryData.prepared_staff.last_name}
									</p>
									<p>
										<b>Room Name: </b> {this.state.inventoryData.room && this.state.inventoryData.room.name}
									</p>
									<p>
										<b>Carton: </b>{" "}
										{this.state.inventoryData.is_carton === 0 || this.state.inventoryData.is_carton === "0"
											? "No"
											: "Yes"}
									</p>
									<p>
										<b>Packed By: </b>
										{this.state.inventoryData.is_carton === 0 || this.state.inventoryData.is_carton === "0"
											? " - "
											: this.state.inventoryData.packed_by
												? this.state.inventoryData.packed_by
												: " - "}
									</p>
									<p>
										<b>Packed By User: </b>{" "}
										{this.state.inventoryData.packed_by !== "Packed by Owner" &&
											this.state.inventoryData.carrier_packed
											? this.state.inventoryData.carrier_packed.first_name +
											" " +
											this.state.inventoryData.carrier_packed.last_name
											: " - "}
									</p>
									<p>
										<b>Disassembled: </b>{" "}
										{this.state.inventoryData.is_disassembled === "0" ||
											this.state.inventoryData.is_disassembled === 0
											? "No"
											: "Yes"}
									</p>
									<p>
										<b>Disassembled By: </b>{" "}
										{this.state.inventoryData.is_disassembled === "0" ||
											this.state.inventoryData.is_disassembled === 0
											? " - "
											: this.state.inventoryData.disassembled_by
												? this.state.inventoryData.disassembled_by
												: " - "}
										{/* {this.state.inventoryData.disassembled_by === "By Customer" ? "By Customer" : this.state.inventoryData.disassembled_user ? this.state.inventoryData.disassembled_by : " - "} */}
									</p>
									<p>
										<b>Disassembled By User: </b>
										{this.state.inventoryData.disassembled_by === "By Customer"
											? " - "
											: this.state.inventoryData.disassembled_user
												? this.state.inventoryData.disassembled_user.first_name +
												" " +
												this.state.inventoryData.disassembled_user.last_name
												: "-"}
									</p>
									<p>
										<b>Storage Location: </b>
										{
											(this.state.inventoryData.unit_list !== "" && this.state.inventoryData.unit_list !== undefined && this.state.inventoryData.unit_list !== null)
												?
												this.state.inventoryData.unit_list.currentLocation
												: ""
										}
									</p>

									<p>
										<b>Unit No: </b>
										{
											(this.state.inventoryData.unit_list !== "" && this.state.inventoryData.unit_list !== undefined && this.state.inventoryData.unit_list !== null)
												?
												this.state.inventoryData.unit_list.name
												: ""
										}
									</p>

									<p>
										<b>Customer Comments: </b> {this.state && this.state.inventoryData && (this.state.inventoryData.notes !== undefined && this.state.inventoryData.notes !== String(null)) ? this.state.inventoryData.notes : ""}
									</p>

								</Col>
								<Col className='item_card_box' span={11}>
									<p>
										<b>Scanned: </b> {this.state.inventoryData.isScanned === "no" ? "No" : "Yes"}
									</p>
									<p>
										<b>Override: </b> {this.state.inventoryData.isOverride === "no" ? "No" : "Yes"}
									</p>
									<p>
										<b>Weight: </b> {this.state.inventoryData.weight} Lbs.
									</p>
									<p>
										<b>Volume: </b> {this.state.inventoryData.volume} Cu.Ft.
									</p>
									<p>
										<b>Electronics: </b>{" "}
										{this.state.inventoryData.is_electronics === "0" ||
											this.state.inventoryData.is_electronics === 0
											? "No"
											: "Yes"}
									</p>
									<p>
										<b>Electronics Serial Number: </b>{" "}
										{this.state.inventoryData.serial_number !== ""
											? this.state.inventoryData.serial_number
											: "-"}
									</p>
									<p>
										<b>Pro Gear: </b>{" "}
										{this.state.inventoryData.is_pro_gear === "0" ||
											this.state.inventoryData.is_pro_gear === 0
											? "No"
											: (this.state.inventoryData.progear_name == "Member" || this.state.inventoryData.progear_name == "member") ? "Yes-Member" : "Yes-Spouse"}
									</p>
									<p>
										<b>Pro Gear Weight: </b> {this.state.inventoryData.pro_gear_weight}
									</p>
									<p>
										<b>Pads: </b> {this.state.inventoryData.pads_used}
									</p>
									<p>
										<b>High Value: </b>{" "}
										{this.state.inventoryData.is_high_value === "0" ||
											this.state.inventoryData.is_high_value === 0
											? "No"
											: "Yes"}
									</p>

									<p>
										<b>Declared Value: </b> ${this.state.inventoryData.declared_value}
									</p>
									<p>
										<b>Internal Note: </b> {this.state && this.state.inventoryData && (this.state.inventoryData.internal_note !== undefined && this.state.inventoryData.internal_note !== String(null)) ? this.state.inventoryData.internal_note : ""}
									</p>
								</Col>
							</Row>

							<Divider orientation='center' type='horizontal'>
								<div className='display-flex'>
									<label className='fs-16 medium-text'>
										<h2>Exceptions</h2>
									</label>
								</div>
							</Divider>

							<Row>
								{this.state.inventoryExceptionData.length > 0
									? this.state.inventoryExceptionData.map((e, i) => {

										return (

											<Col className='item_card_box' span={12}>
												<p>
													<b>Exception {i + 1}</b>
												</p>
												<p>
													<b>Note: </b> {e.notes}
												</p>
												<p>
													<b>Exceptions: </b>{" "}
													{e.eid.length > 0
														?
														(
															e.eid.length == 1
														) ?
															e.eid.map((eid) => {
																return (
																	<span>{`${eid.exception_name ? eid.exception_name + " .  " : ""}`}</span>
																)
															})
															:
															e.eid.map((eid) => {
																return (
																	<span>{`${eid.exception_name ? eid.exception_name + ",  " : ""}`}</span>
																)
															})
														: "-"}
												</p>
												<p>
													<b>Locations : </b>{" "}
													{e.lid.length > 0
														?
														(
															e.lid.length == 1
														)
															?
															e.lid.map((lid) => {
																return (
																	<span>{`${lid.location_name ? lid.location_name + ". " : ""}`}</span>
																)
															})
															:
															e.lid.map((lid) => {
																return (
																	<span>{`${lid.location_name ? lid.location_name + ",  " : ""}`}</span>
																)
															})
														: "-"}
												</p>
											</Col>
										)
									})

									: null}
							</Row>

							<Divider orientation='center' type='horizontal'>
								<div className='display-flex'>
									<label className='fs-16 medium-text'>
										<h2>Comments & Notes</h2>
									</label>
								</div>
							</Divider>

							<Row>

								{this.state.commentsArray.length && this.state.commentsArray.length > 0
									?
									<Col className='item_card_box' span={11}>
										<h1><b>Customer Comments</b></h1>
										{
											this.state.commentsArray.map((e, i) => (
												<ul>
													<li><b>{e.comment} </b></li>
												</ul>

											))
										}
									</Col>
									: null}


								{this.state.notesArray.length && this.state.notesArray.length > 0
									?
									<Col className='item_card_box' span={11}>
										<h1><b>Internal Notes</b></h1>
										{
											this.state.notesArray.map((e, i) => (
												<ul>
													<li><b>{e.note} </b></li>
												</ul>

											))
										}
									</Col>
									: null}

							</Row>

							<Divider orientation='center' type='horizontal'>
								<div className='display-flex'>
									<label className='fs-16 medium-text'>
										<h2>Images</h2>
									</label>
								</div>
							</Divider>

							<Row  >
								{this.state.inventoryItemData.length > 0
									? this.state.inventoryItemData.map((e) => (
										<Col className='item_card_box' span={5}>
											<Card
												className='item_card'
												hoverable
												cover={
													<img
														onClick={() => this.setState({ ImageModal: true, ImageValue: e.item_photo || "" })}
														alt='item_image'
														src={e.item_photo || ""}
														height='170'
														style={{
															objectFit: 'cover',
															width: '100%',
															borderRadius: '8px',
															boxShadow: '0 4px 8px rgba(0,0,0,0.2)'
														}}
													/>
												}
											>
												<div style={{ display: "flex", margin: "10px 0" }}>
													<p style={{ margin: "0", marginRight: "5px" }}>
														{e.shipment_type_stage
															? "Stage " + e.shipment_type_stage.order_of_stages + ": "
															: "Stage: "}
													</p>
													<strong>
														{e.shipment_type_stage
															? e.shipment_type_stage.name
															: this.props.location.state
																? this.props.location.state.initialStage
																: "Initial Stage"}
													</strong>
												</div>

												<p>
													Note: <strong>{e.description ? e.description : "Item Scanned & Added!"}</strong>
												</p>

												<Radio
													checked={e.is_thumbnail === 1}
													disabled={e.is_thumbnail === 1}
													onChange={() => this.openThumbnailModel(e)}
												>
													Use as a Thumbnail
												</Radio>
											</Card>

										</Col>
									))
									: null}
							</Row>

							<Divider orientation='center' type='horizontal'>
								<div className='display-flex'>
									<label className='fs-16 medium-text'>
										<h2>Scan/Override History</h2>
									</label>
								</div>
							</Divider>
							<Row>
								<Col span={24}>
									<div></div>
								</Col>
							</Row>

							<Row style={{ display: "flex" }}>
								<Col className='item_card_box' span={12}>
									<h2 style={{ textAlign: "center", padding: "10px" }}>Scan History</h2>
									<div
										style={{
											marginBottom: "15px",
											borderBottom: "1px solid #ccc",
										}}>
										<h3>Stage:&nbsp;&nbsp;Item Scanned & Added.</h3>
										<h3>
											Username:&nbsp;&nbsp;
											{this.state.inventoryData.prepared_staff &&
												this.state.inventoryData.prepared_staff.first_name +
												" " +
												this.state.inventoryData.prepared_staff.last_name}
										</h3>
										<h3>
											Date/Time:&nbsp;&nbsp;
											{this.toDate(this.state.inventoryData.created_at)}
										</h3>
									</div>
									{this.state.scannedInventory.length > 0 ? (
										this.state.scannedInventory.map((e) => (
											<div
												style={{
													marginBottom: "15px",
													borderBottom: "1px solid #ccc",
												}}>
												<h3>Stage:&nbsp;&nbsp;{e.shipment_type_stage_for_shipment && e.shipment_type_stage_for_shipment.name}</h3>
												<h3>
													Username:&nbsp;&nbsp;
													{e.scanned_by_staff ? e.staff.first_name + " " + e.staff.last_name : " - "}{" "}
												</h3>
												<h3>Date/Time:&nbsp;&nbsp;{this.toDate(e.created_at)}</h3>
											</div>
										))
									) : (
										<h3 style={{ textAlign: "center", color: "#ccc" }}>No scanned history!</h3>
									)}
								</Col>
								<Col className='item_card_box' span={12}>
									<h2 style={{ textAlign: "center", padding: "10px" }}>Override History</h2>
									{this.state.forcedInventory.length > 0 ? (
										this.state.forcedInventory.map((e) => (
											<div
												style={{
													marginBottom: "15px",
													borderBottom: "1px solid #ccc",
												}}>
												<h3>Stage:&nbsp;&nbsp;{e.shipment_type_stage_for_shipment && e.shipment_type_stage_for_shipment.name}</h3>

												<h3>
													Username:&nbsp;&nbsp;
													{e.staff ? e.staff.first_name + " " + e.staff.last_name : " - "}{" "}
												</h3>
												<h3>Date/Time:&nbsp;&nbsp;{this.toDate(e.updated_at)}</h3>
												<h3>Reason:&nbsp;&nbsp;{e.reason}</h3>
											</div>
										))
									) : (
										<h3 style={{ textAlign: "center", color: "#ccc" }}>No override history!</h3>
									)}
								</Col>
							</Row>
						</Spin>
					</div>
				</LayoutContent>
				{
					<Modal
						bodyStyle={{
							textAlign: "center",
							padding: "20px",
							background: "#f8f8f8"
						}}
						title={null}
						visible={this.state.ImageModal}
						cancelText='Close'
						centered
						maskClosable={false}
						onOk={this.handleImageCancel}
						onCancel={this.handleImageCancel}
						width="auto"
						footer={[
							<a
								key="download"
								href={this.state.ImageValue}
								download="inventoryImage.jpg"
								target="_blank"
								rel="noopener noreferrer"
							>
								<Button type="primary">Download</Button>
							</a>,
							<Button style={{ marginLeft: "10px" }} key="close" onClick={this.handleImageCancel}>
								Close
							</Button>
						]}
					>
						<div style={{
							display: "flex",
							justifyContent: "center",
							alignItems: "center",
							maxHeight: "80vh",
							maxWidth: "90vw",
							overflow: "hidden"
						}}>
							<img
								alt='item_image'
								src={this.state.ImageValue}
								style={{
									maxHeight: "70vh",
									maxWidth: "100%",
									objectFit: "contain",
									filter: "contrast(1.15) brightness(1.05)", // Enhance contrast and brightness
									boxShadow: "0 4px 12px rgba(0,0,0,0.15)", // Add subtle shadow for depth
									borderRadius: "4px" // Slightly round the corners
								}}
							/>
						</div>
					</Modal>
				}

				{
					<Modal
						title='Are You Sure?'
						visible={this.state.visibleThumbnailModel}
						onOk={this.handleThumbnailUpdate}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleThumbnailCancel}>
						<p>Are you sure you want to update this image as thumbnail?</p>
					</Modal>
				}

				<Modal
					title="Add Photos"
					visible={this.state.addPhotosModalVisible}
					onCancel={this.handleAddPhotosCancel}
					width={800}
					footer={[
						<Button key="back" onClick={this.handleAddPhotosCancel}>
							Cancel
						</Button>,
						<Button
							key="submit"
							type="primary"
							loading={this.state.uploadLoading}
							onClick={this.handleUploadPhotos}
						>
							Upload
						</Button>,
					]}
				>
					<Spin spinning={this.state.uploadLoading}>
						<div className="clearfix">
							<div style={{ marginBottom: 16 }}>
								<Alert
									description="You can add up to 9 photos. Preview and remove them before uploading."
									type="info"
									showIcon
								/>
							</div>
							<Upload
								multiple={true}
								listType="picture-card"
								fileList={this.state.photoFileList}
								onPreview={this.handlePhotoPreview}
								onChange={this.handlePhotoChange}
								beforeUpload={() => false}
								accept="image/*"
							>
								{this.state.photoFileList.length >= 9 ? null : (
									<div>
										<Icon type="plus" />
										<div className="ant-upload-text">Select</div>
									</div>
								)}
							</Upload>
							<Modal
								visible={this.state.photoPreviewVisible}
								footer={null}
								onCancel={this.handlePhotoPreviewCancel}
							>
								<img
									alt="preview"
									style={{ width: '100%' }}
									src={this.state.photoPreviewImage}
								/>
							</Modal>
						</div>
					</Spin>
				</Modal>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(ViewJob));
