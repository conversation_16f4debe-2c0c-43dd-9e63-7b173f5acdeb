import {
	Button,
	DatePicker,
	Form,
	Icon,
	Input,
	InputNumber,
	message,
	Select,
	Spin,
	Tag,
	List,
	Checkbox
} from "antd";
import moment from "moment";
import React from "react";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";

import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import { GOOGLE_API_KEY } from "../../static/data/constants";

import axios from 'axios';
import PlacesAutocomplete, {
	geocodeByAddress,
} from 'react-places-autocomplete';


const { TextArea } = Input;
const { Option } = Select;
// alert('hi');
const { changeCurrent } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddJob extends React.Component {
	state = {
		isPickupDateMandatoryFlag: false,
		loading: false,
		contentloader: true,
		previewVisible: false,
		previewImage: "",
		sourceList: ["Standalone", "External", "MG"],
		fileList: [],
		companyList: [],
		tags: [],
		warehousesList: [],
		adminId: "",
		companyId: "",
		staffId: "",
		shipmentTypeData: null,
		customerData: null,
		customerDataFromField: null,
		staffData: null,
		customerSearch: "",
		integrationKeyStatus: false,
		warehouseLoader: false,
		integrationKey: "",
		consumerLoginAccessToken: "",
		storageCompanyId: "",
		companyName: this.props.company
			? this.props.company
			: this.props.location.state && this.props.location.state.customerDirect
				? this.props.location.state.customerDirect.company_name
				: null,
		companyID: this.props.companyID
			? this.props.companyID
			: this.props.location.state && this.props.location.state.customerDirect
				? this.props.location.state.customerDirect.company_id
				: null,
		customerDirect: this.props.location.state ? this.props.location.state.customerDirect : null,
		PickupAddress: "",
		PickupCity: "",
		PickupCountry: "",
		PickupState: "",
		PickupZipCode: "",
		DeliveryAddress: "",
		DeliveryCity: "",
		DeliveryCountry: "",
		DeliveryState: "",
		DeliveryZipCode: "",
	};

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {

		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};

	componentDidMount() {
		// this.props.changeCurrent("sub-categories");
		// alert("componentDidMount");

		this.setState({
			PickupAddress: this.props.location.state
				&& this.props.location.state.customerDirect
				&& this.props.location.state.customerDirect.address1 ? this.props.location.state.customerDirect.address1 : "",
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});

		this.setState({ contentloader: true, loading: true, warehouseLoader: true })


		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							contentloader: false,
							integrationKeyStatus: true,
							integrationKey: response.data.integration_key,
							storageCompanyId: response.data.key_company.storage_company_id
						});
						this.consumerLoginJson(response.data.integration_key);
					} else {
						this.setState({
							contentloader: false,
							loading: false,
							warehouseLoader: false,
							integrationKeyStatus: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({
					contentloader: false,
					loading: false,
					warehouseLoader: false,
				});
			});

		this.fetchInitialRender();

	}

	consumerLoginJson = (integrationKey) => {
		this.setState({ contentloader: true, loading: true, warehouseLoader: true })
		const consumerLoginJson = JSON.stringify({
			companyIdTokenMoverInventory: this.state.integrationKey ? this.state.integrationKey : integrationKey,
			email: "<EMAIL>",
			password: "5PLaRAqq",
			deviceToken: "abcd",
			deviceType: 0,
		});

		axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson,
			{
				headers: {
					'Content-Type': 'application/json'
				}
			})
			.then((consumerLoginResponse) => {
				this.setState({ consumerLoginAccessToken: consumerLoginResponse.data.data.accessToken });
				this.fetchCompanyResponse(integrationKey, consumerLoginResponse.data.data.accessToken);
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false, warehouseLoader: false });
			});


	}

	fetchCompanyResponse = (integrationKey, accessToken) => {
		this.setState({ contentloader: true, loading: true, warehouseLoader: true })

		const fetchCompanyJson = JSON.stringify({
			companyIdTokenMoverInventory: this.state.integrationKey ? this.state.integrationKey : integrationKey,
		});

		axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/companies`, fetchCompanyJson,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': this.state.consumerLoginAccessToken ? this.state.consumerLoginAccessToken : accessToken
				}
			})
			.then((fetchCompanyResponse) => {
				this.setState({ warehousesList: fetchCompanyResponse.data.data.warehouses });
				this.setState({ contentloader: false, loading: false, warehouseLoader: false });
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false, warehouseLoader: false });
			});
	}

	fetchInitialRender = () => {

		const data = [];
		API.get("api/admin/shipment-type/basic/", data)
			.then((response) => {
				if (response) {
					this.setState({
						shipmentTypeData: response.data,
						contentloader: false,
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.get(`api/admin/customer/basic/?search=${this.state.customerSearch}`, data)
			.then((response) => {
				if (response) {
					const newData = response.data.filter((data, i) => {
						return (data.company_id == this.state.companyID && data.status === "active")
					})
					this.setState({
						customerData: newData,
						contentloader: false,
					});
				} else {
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.get(`api/admin/tag/list/shipment`)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							tags: response.data,
							contentloader: false,
						});
					} else {
						message.error(response.message);
						this.setState({ contentloader: false });
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.post("api/admin/company/list", data)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							companyList: response.data.companyList.rows,
							contentloader: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							contentloader: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.get("api/admin/staff/basic/0", data)
			.then((response) => {
				if (response) {
					this.setState({
						staffData: response.data && response.data,
						contentloader: false,
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	};


	fetchCustomerDropdownList = (serchProps) => {
		API.get(`api/admin/customer/basic/?search=${serchProps.searchFieldValue}`)
			.then((response) => {
				if (response) {
					const newData = response.data.filter((data, i) => {
						return (data.company_id == this.state.companyID && data.status === "active")
					})
					this.setState({
						customerData: newData,
						contentloader: false,
					});
				} else {
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	};

	OnCustomerChange = (values) => {
		this.setState({ customerDataFromField: values });
		this.setState({
			PickupAddress: (values.address1 !== undefined && values.address1 !== null && values.address1 !== "") ? values.address1 : "",
			PickupCity: (values.city !== undefined && values.city !== null && values.city !== "") ? values.city : "",
			PickupState: (values.state !== undefined && values.state !== null && values.state !== "") ? values.state : "",
			PickupCountry: (values.country !== undefined && values.country !== null && values.country !== "") ? values.country : "",
			PickupZipCode: (values.zipCode !== undefined && values.zipCode !== null && values.zipCode !== "") ? values.zipCode : "",
		});
	};
	onSearch = (name, value) => {
		const data = {
			searchFieldName: name,
			searchFieldValue: value,
		};
		this.fetchCustomerDropdownList(data);
	};

	minOne = (rule, value, callback) => {
		if (value && value <= 0) {
			callback("Estimated weight must be greater then 0!");
		}
		else if (value && value > 10000000) {
			callback("Estimated weight must be less than or equal to 10000000!");
		}
		else {
			callback();
		}
	};

	minVolumeOne = (rule, value, callback) => {
		if (value && value <= 0) {
			callback("Volume must be greater then 0!");
		}
		else {
			callback();
		}
	};


	addShipmentJson = async (response, customerDetails) => {

		function formatDate(inputDate) {
			const date = new Date(inputDate);
			const year = date.getUTCFullYear();
			const month = String(date.getUTCMonth() + 1).padStart(2, '0');
			const day = String(date.getUTCDate()).padStart(2, '0');
			return `${month}-${day}-${year}`;
		}


		const addShipmentJson = JSON.stringify({
			shipmentName: response.data.shipment_name,
			customerId: customerDetails.data.storage_customer_id,
			warehouseId: response.data.warehouseId,
			contactReference: response.data.contact_reference,
			accountReference: response.data.account_reference,
			opportunityReference: response.data.opportunity_reference,
			estArrival: formatDate(response.data.pickup_date),
			estDelivery: formatDate(response.data.delivery_date),
			workOrderNotes: [

			],
			workOrderReference: response.data.wo_reference,
			externalReference: [

			],
			moveCoord: "",
			source: response.data.source,
			volume: response.data.estimated_volume,
			weight: response.data.estimated_weight,
			estUnits: "",
			originAddress: {
				addressLine1: response.data.pickup_address,
				addressLine2: response.data.pickup_address2,
				city: response.data.pickup_city,
				state: response.data.pickup_state,
				zipcode: response.data.pickup_zipcod,
				country: response.data.pickup_country,

			},
			destinationAddress: {
				addressLine1: response.data.delivery_address,
				addressLine2: response.data.delivery_address2,
				city: response.data.delivery_city,
				state: response.data.delivery_state,
				zipcode: response.data.delivery_zipcode,
				country: response.data.delivery_country,
			},
			importedTags: [],
			moverInventoryShipmentId: response.data.shipment_job_id,
			createdFromMoverInventory: true
		});


		axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/shipments`, addShipmentJson,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': this.state.consumerLoginAccessToken
				}
			})
			.then((addShipmentResponse) => {
				let params = {
					shipment_job_id: response.data.shipment_job_id,
					shipmentIdStorage: addShipmentResponse.data.data.id,
				};
				const data = [];
				data["data"] = params;


				API.post("api/admin/shipment/storageId/update", data)
					.then((storageIdUpdate) => {
						if (storageIdUpdate.status === 1) {
							this.setState({ contentloader: false, loading: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false, contentloader: false });
							message.error(storageIdUpdate.message);
						}
					})
					.catch((error) => {
						message.error(error.message);
						this.setState({ loading: false, contentloader: false });
					});
			})
			.catch((error) => {
				this.setState({ loading: false, contentloader: false });

			});
	}


	fetchCustomerDetails = (response) => {
		const data = [];
		data["data"] = { customer_id: response.data.customer_id };
		API.post("api/admin/customer/view-customer", data)
			.then((customerDetails) => {
				if (customerDetails) {
					this.addShipmentJson(response, customerDetails);
				} else {
					this.setState({ loading: false, contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ loading: false, contentloader: false });

			});
	}

	isPickDateMandatoryCheckFunc = (e) => {
		const result = this.state.shipmentTypeData.find(shipmentType => {
			if (shipmentType.shipment_type_id == e.key) {
				return shipmentType;
			}
		});
		if (result.is_pickup_date_mandatory == 1) {
			this.setState({
				isPickupDateMandatoryFlag: true
			})
		}
		else {
			this.setState({
				isPickupDateMandatoryFlag: false
			})
		}
	}


	handleSubmit = (e) => {

		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {
			const tag = [];
			if (values.tags) {
				values.tags.forEach((item) => {
					tag.push(item.key);
				});
			}
			let params = {
				shipment_type_id: values.shipment_type.key,
				company_id: values.company_id.key,
				email: (values.email !== undefined) ? values.email : "",
				warehouse: (values.warehouse !== undefined) ? values.warehouse : "",
				warehouseId: this.state.integrationKeyStatus ? ((values.warehouse_id.key !== undefined) ? values.warehouse_id.key : "") : "",
				customer_id: (values.customer_id.key !== undefined) ? values.customer_id.key : "",
				pickup_address: (values.pickup_address !== undefined) ? values.pickup_address : this.state.PickupAddress,
				pickup_address2: (values.pickup_address2 !== undefined) ? values.pickup_address2 : "",
				pickup_city: (values.pickup_city !== undefined) ? values.pickup_city : "",
				pickup_state: (values.pickup_state !== undefined) ? values.pickup_state : "",
				pickup_zipcode: (values.pickup_zipcode !== undefined) ? values.pickup_zipcode : "",
				pickup_country: (values.pickup_country !== undefined) ? values.pickup_country : "",
				delivery_address: (values.delivery_address !== undefined) ? values.delivery_address : this.state.DeliveryAddress,
				delivery_address2: (values.delivery_address2 !== undefined) ? values.delivery_address2 : "",
				delivery_city: (values.delivery_city !== undefined) ? values.delivery_city : "",
				delivery_state: (values.delivery_state !== undefined) ? values.delivery_state : "",
				delivery_zipcode: (values.delivery_zipcode !== undefined) ? values.delivery_zipcode : "",
				delivery_country: (values.delivery_country !== undefined) ? values.delivery_country : "",
				external_reference: (values.external_reference !== undefined) ? values.external_reference : "",
				external_reference_2: (values.external_reference_2 !== undefined) ? values.external_reference_2 : "",
				external_reference_3: (values.external_reference_3 !== undefined) ? values.external_reference_3 : "",
				pickup_date: (values.pickup_date_estimated !== undefined && values.pickup_date_estimated !== "") ? values.pickup_date_estimated : null,
				delivery_date: (values.delivery_date_estimated !== undefined && values.delivery_date_estimated !== "") ? values.delivery_date_estimated : null,
				shipment_name: (values.shipment_name !== undefined) ? values.shipment_name : "",
				estimated_weight: (values.estimated_weight !== undefined && values.estimated_weight !== "") ? values.estimated_weight : null,
				estimated_volume: (values.estimated_volume !== undefined && values.estimated_volume !== "") ? values.estimated_volume : null,
				notes: (values.notes !== undefined) ? values.notes : "",
				source: (values.source !== undefined) ? values.source.key : "",
				contact_reference: (values.contact_reference !== undefined) ? values.contact_reference : "",
				account_reference: (values.account_reference !== undefined) ? values.account_reference : "",
				opportunity_reference: (values.opportunity_reference !== undefined) ? values.opportunity_reference : "",
				move_coordinator: (values.move_coordinator !== undefined) ? values.move_coordinator : "",
				wo_reference: (values.wo_reference !== undefined) ? values.wo_reference : "",
				tag: JSON.stringify(tag),
			};


			if (!err || err === null) {
				const data = [];
				data["data"] = params;
				this.setState({ loading: true, contentloader: true });
				API.post("api/admin/shipment/", data)
					.then((response) => {
						if (response.status === 1) {
							if (this.state.integrationKeyStatus && response.data.scanIntoStorageCheck) {
								this.fetchCustomerDetails(response);
							}
							else {
								this.setState({ loading: false, contentloader: false });
								message.success(response.message);
								this.props.history.push("/job");
							}
						} else {
							this.setState({ loading: false, contentloader: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false, contentloader: false });
					});
			} else {
				//console.log("inside else");
			}
		});
	};

	normFile = (e) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e && e.fileList;
	};

	handleChangePickupAddress = PickupAddress => {
		this.setState({ PickupAddress });
	};

	handleSelectPickupAddress = async (PickupAddress, placeId) => {
		if (placeId !== undefined && placeId !== "" && placeId !== null) {
			this.fetchPlaceDetails(placeId);
		}
		else {
			this.setState({ PickupAddress: PickupAddress });
		}
	};

	fetchPlaceDetails = async (placeId) => {
		let params = {
			placeId: placeId,
		};
		const data = [];
		data["data"] = params;
		this.setState({ loading: true, contentloader: true });

		API.post("api/admin/google/place/details", data)
			.then((response) => {
				const addressComponents = response.data && response.data.result && response.data.result.address_components;
				// Initialize variables to store city, state, and country
				let address, city, state, country, zipCode;

				// Iterate through the address components to find city, state, and country
				for (const component of addressComponents) {
					for (const type of component.types) {
						if (type === 'street_number' || type === 'route') {
							address = address ? `${address} ${component.long_name}` : component.long_name;
						} else if (type === 'locality') {
							city = component.long_name;
						} else if (type === 'administrative_area_level_1') {
							state = component.long_name;
						} else if (type === 'country') {
							country = component.long_name;
						} else if (type === 'postal_code') {
							zipCode = component.long_name;
						}
					}
				}

				this.setState({
					PickupAddress: address,
					PickupCity: city,
					PickupState: state,
					PickupCountry: country,
					PickupZipCode: zipCode
				});

				this.props.form.setFieldsValue({
					pickup_city: city,
					pickup_state: state,
					pickup_zipcode: zipCode,
					pickup_country: country,
				});

				this.setState({ loading: false, contentloader: false });
			})
			.catch((error) => {
				message.error(error.message);
				this.setState({ loading: false, contentloader: false });

			});
	};

	handleChangeDeliveryAddress = DeliveryAddress => {
		this.setState({ DeliveryAddress });
	};

	handleSelectDeliveryAddress = (PickupAddress, placeId) => {
		if (placeId !== undefined && placeId !== "" && placeId !== null) {
			this.fetchPlaceDetailsDelivery(placeId);
		}
		else {
			this.setState({ DeliveryAddress: PickupAddress });
		}
	};

	fetchPlaceDetailsDelivery = async (placeId) => {
		let params = {
			placeId: placeId,
		};
		const data = [];
		data["data"] = params;
		this.setState({ loading: true, contentloader: true });
		API.post("api/admin/google/place/details", data)
			.then((response) => {
				const addressComponents = response.data && response.data.result && response.data.result.address_components;
				let address, city, state, country, zipCode;
				for (const component of addressComponents) {
					for (const type of component.types) {
						if (type === 'street_number' || type === 'route') {
							address = address ? `${address} ${component.long_name}` : component.long_name;
						} else if (type === 'locality') {
							city = component.long_name;
						} else if (type === 'administrative_area_level_1') {
							state = component.long_name;
						} else if (type === 'country') {
							country = component.long_name;
						} else if (type === 'postal_code') {
							zipCode = component.long_name;
						}
					}
				}
				this.setState({
					DeliveryAddress: address,
					DeliveryCity: city,
					DeliveryState: state,
					DeliveryCountry: country,
					DeliveryZipCode: zipCode
				});

				this.props.form.setFieldsValue({
					delivery_city: city,
					delivery_state: state,
					delivery_zipcode: zipCode,
					delivery_country: country,
				});
				this.setState({ loading: false, contentloader: false });
			})
			.catch((error) => {
				this.setState({ loading: false, contentloader: false });
				message.error(error.message);
			});
	};

	autocompleteDropdownStyle = {
		position: 'absolute',
		top: '100%',
		left: 0,
		width: '500px',
		zIndex: 1,
		backgroundColor: '#ffffff',
		border: '1px solid #ccc',
		boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
		maxHeight: '200px',
		overflowY: 'auto',
		padding: '5px',
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const { customerDataFromField, customerDirect } = this.state;
		const initialEmail =
			customerDataFromField && customerDataFromField.email
				? customerDataFromField.email
				: customerDirect && customerDirect.email
					? customerDirect.email
					: "";
		const initialAddress1 =
			customerDataFromField &&
				customerDataFromField.address1 &&
				customerDataFromField.address1 !== "undefined"
				? customerDataFromField.address1
				: customerDirect && customerDirect.address1 && customerDirect.address1 !== "undefined"
					? customerDirect.address1
					: "";
		const initialAddress2 =
			customerDataFromField &&
				customerDataFromField.address2 &&
				customerDataFromField.address2 !== "undefined"
				? customerDataFromField.address2
				: customerDirect && customerDirect.address2 && customerDirect.address2 !== "undefined"
					? customerDirect.address2
					: "";
		const initialCity =
			customerDataFromField && customerDataFromField.city && customerDataFromField.city !== "undefined"
				? customerDataFromField.city
				: customerDirect && customerDirect.city && customerDirect.city !== "undefined"
					? customerDirect.city
					: "";
		const initialState =
			customerDataFromField &&
				customerDataFromField.state &&
				customerDataFromField.state !== "undefined"
				? customerDataFromField.state
				: customerDirect && customerDirect.state && customerDirect.state !== "undefined"
					? customerDirect.state
					: "";
		const initialZipCode =
			customerDataFromField &&
				customerDataFromField.zipCode &&
				customerDataFromField.zipCode !== "undefined"
				? customerDataFromField.zipCode
				: customerDirect && customerDirect.zipCode && customerDirect.zipCode !== "undefined"
					? customerDirect.zipCode
					: "";
		const initialcountry =
			customerDataFromField &&
				customerDataFromField.country &&
				customerDataFromField.country !== "undefined"
				? customerDataFromField.country
				: customerDirect && customerDirect.country && customerDirect.country !== "undefined"
					? customerDirect.country
					: "";

		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		// const dateFormat = "YYYY/MM/DD";


		function disabledDate(current) {
			// return current && current < moment().endOf("day");
			return current && current < moment().endOf('day').subtract(1, 'day');
		}

		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add Shipment
					</h2>
					<button className='backButton' onClick={() => this.props.history.push("/job")}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent
					style={{
						margin: "0 20px",
						height: "93%",
						overflowY: "auto",
					}}>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Spin spinning={this.state.warehouseLoader} indicator={antIcon}>

								<Form {...formItemLayout} onSubmit={this.handleSubmit}>
									<Form.Item label='Company Name'>
										{getFieldDecorator("company_id", {
											initialValue: this.state.companyName
												? {
													key: this.state.companyID,
													label: this.state.companyName,
												}
												: [],
											rules: [
												{
													required: true,
													message: "Please select company name",
												},
											],
										})(
											<Select
												size={"default"}
												labelInValue
												disabled
												placeholder='Select Company Name'
												style={{ width: "100%" }}>
												{this.state.companyList
													? this.state.companyList.map((e) => (
														<Option key={e.company_name} value={e.company_id}>
															{e.company_name}
														</Option>
													))
													: null}
											</Select>
										)}
									</Form.Item>

									<Form.Item label='Shipment Type'>
										{getFieldDecorator("shipment_type", {
											rules: [
												{
													required: true,
													message: "Please select shipment type",
												},
											],
										})(
											<Select
												size={"default"}
												labelInValue
												onChange={(e) => {
													this.isPickDateMandatoryCheckFunc(e);
												}}
												placeholder='Select Shipment Type'
												style={{ width: "100%" }}>
												{this.state.shipmentTypeData
													? this.state.shipmentTypeData.map((e) => (
														<Option key={e.shipment_type_id} value={e.shipment_type_id}>
															{e.name}
														</Option>
													))
													: null}
											</Select>
										)}
									</Form.Item>

									<Form.Item label='Customer Name'>
										{getFieldDecorator("customer_id", {
											initialValue:
												customerDirect && customerDirect.first_name
													? {
														key: customerDirect.customer_id,
														label: customerDirect.last_name
															? customerDirect.first_name + customerDirect.last_name
															: customerDirect.first_name,
													}
													: [],
											rules: [
												{
													required: true,
													message: "Please select customer name",
												},
											],
										})(
											<Select
												size={"default"}
												showSearch
												labelInValue
												optionFilterProp='children'
												placeholder='Select Customer Name'
												style={{ width: "100%" }}
												onSearch={this.onSearch.bind(null, "customer")}>
												{this.state.customerData
													? this.state.customerData.map((e) => (
														<Option
															key={e.customer_id}
															value={e.customer_id}
															onClick={this.OnCustomerChange.bind(null, e)}>
															{e.full_name}
														</Option>
													))
													: null}
											</Select>
										)}
									</Form.Item>

									<Form.Item label='Email'>
										{getFieldDecorator("email", {
											initialValue: initialEmail,
											rules: [
												{
													type: "email",
													message: "The input is not valid Email!",
												},
												{
													required: true,
													message: "Please input your Email!",
												},
											],
										})(<Input placeholder='Email' readOnly />)}
									</Form.Item>

									<Form.Item label='Shipment Name'>
										{getFieldDecorator("shipment_name", {
											placeholder: "Shipment Name",
											rules: [
												{
													required: true,
													message: "Please input shipment name!",
													whitespace: true,
												},
											],
										})(<Input placeholder='Shipment Name' />)}
									</Form.Item>

									<Form.Item label='Tags'>
										{getFieldDecorator(
											"tags",
											{}
										)(
											<Select
												getPopupContainer={trigger => trigger.parentNode}
												mode='multiple'
												labelInValue
												allowClear
												style={{ width: "100%" }}
												placeholder='Please select tags'>
												{this.state.tags.map((item, index) => (
													<Option value={item.tag_id} key={item.tag_id}>
														<Tag color={item.color ? item.color : ""}>{item.name}</Tag>
													</Option>
												))}
											</Select>
										)}
									</Form.Item>

									{
										this.state.integrationKeyStatus ?
											<Form.Item label='Warehouses'>
												{getFieldDecorator("warehouse_id", {
													rules: [
														{
															required: true,
															message: "Please select warehouse",
														},
													],
												})(
													<Select
														size={"default"}
														labelInValue
														placeholder='Select warehouse'
														style={{ width: "100%" }}>
														{this.state.warehousesList
															? this.state.warehousesList.map((e) => (
																<Option key={e.id} value={e.id}>
																	{e.name == "Sample Warehouse" ? e.name + " " + `(${e.companyName})` : e.name}
																</Option>
															))
															: null}
													</Select>
												)}
											</Form.Item>
											: ""
									}

									<Form.Item label='Pickup Date'>
										{getFieldDecorator(
											"pickup_date_estimated",
											{
												rules: [
													{
														required: this.state.isPickupDateMandatoryFlag ? true : false,
														message: "Please select Pickup date",
													},
												],
											}

										)(
											<DatePicker
												disabledDate={disabledDate}
												defaultPickerValue={moment}
												placeholder='Pickup date'
											/>
										)}
									</Form.Item>
									<Form.Item label='Estimated Delivery'>
										{getFieldDecorator(
											"delivery_date_estimated",
											{}
										)(
											<DatePicker
												disabledDate={disabledDate}
												defaultPickerValue={moment()}
												placeholder='Estimated delivery date'
											/>
										)}
									</Form.Item>


									<Form.Item label='Work Order Notes'>
										{getFieldDecorator("notes", {})(<TextArea rows={4} placeholder='Work Order Notes' />)}
									</Form.Item>

									{/* <Form.Item label='Origin Line 1'>
										{getFieldDecorator("pickup_address", {
											initialValue: initialAddress1,
										})(<Input placeholder='Origin Line 1' />)}
									</Form.Item> */}

									{/* <PlacesAutocomplete
										value={this.state.PickupAddress}
										onChange={this.handleChangePickupAddress}
										onSelect={this.handleSelectPickupAddress}
									>
										{({ getInputProps, suggestions, getSuggestionItemProps, loading }) => (
											<div>
												<Form.Item label='Origin Line 1'>
													<Input
														{...getInputProps({
															placeholder: 'Origin Line 1 ...',
														})}
													/>
													<div style={{ width: "500px" }} className="autocomplete-dropdown-container">
														{loading && <div>Loading...</div>}
														{suggestions.map(suggestion => {
															const style = {
																backgroundColor: suggestion.active ? "#f1f2f6" : "#ffffff"
															}
															return (
																<div
																	{...getSuggestionItemProps(suggestion, {
																		style
																	})}
																>
																	<List.Item>
																		<List.Item.Meta
																			description={suggestion.description}
																		/>
																	</List.Item>
																</div>
															);
														})}
													</div>
												</Form.Item>

											</div>
										)}
									</PlacesAutocomplete> */}

									<PlacesAutocomplete
										value={this.state.PickupAddress}
										onChange={this.handleChangePickupAddress}
										onSelect={this.handleSelectPickupAddress}
									>
										{({ getInputProps, suggestions, getSuggestionItemProps, loading }) => (
											<div>
												<Form.Item label='Origin Line 1'>
													<div style={{ position: 'relative' }}>
														<Input
															{...getInputProps({
																placeholder: 'Origin Line 1 ...',
															})}
														/>
														{suggestions.length > 0 && ( // Check if suggestions array has items
															<div
																style={this.autocompleteDropdownStyle}
																className="autocomplete-dropdown-container"
															>
																{loading && <div>Loading...</div>}
																{suggestions.map(suggestion => {
																	const style = {
																		backgroundColor: suggestion.active ? '#f1f2f6' : '#ffffff',
																		cursor: 'pointer',
																	};
																	return (
																		<div
																			key={suggestion.id} // Add a unique key for each suggestion
																			{...getSuggestionItemProps(suggestion, {
																				style,
																			})}
																		>
																			<List.Item>
																				<List.Item.Meta
																					description={suggestion.description}
																				/>
																			</List.Item>
																		</div>
																	);
																})}
															</div>
														)}
													</div>
												</Form.Item>
											</div>
										)}
									</PlacesAutocomplete>


									<Form.Item label='Origin Line 2'>
										{getFieldDecorator("pickup_address2", {
											initialValue: initialAddress2,
										})(<Input placeholder='Origin Line 2' />)}
									</Form.Item>

									<Form.Item label='Origin City'>
										{getFieldDecorator("pickup_city", {
											initialValue: initialCity ? initialCity : this.state.PickupCity,
										})(<Input placeholder='Origin City' />)}
									</Form.Item>

									<Form.Item label='Origin State'>
										{getFieldDecorator("pickup_state", {
											initialValue: initialState ? initialState : this.state.PickupState,
										})(<Input placeholder='Origin State' />)}
									</Form.Item>

									<Form.Item label='Origin Zipcode'>
										{getFieldDecorator("pickup_zipcode", {
											initialValue: initialZipCode ? initialZipCode : this.state.PickupZipCode,
										})(<Input placeholder='Origin Zipcode' />)}
									</Form.Item>

									<Form.Item label='Origin Country'>
										{getFieldDecorator("pickup_country", {
											initialValue: initialcountry ? initialcountry : this.state.PickupCountry,
										})(<Input placeholder='Origin Country' />)}
									</Form.Item>

									<PlacesAutocomplete
										value={this.state.DeliveryAddress}
										onChange={this.handleChangeDeliveryAddress}
										onSelect={this.handleSelectDeliveryAddress}
									>
										{({ getInputProps, suggestions, getSuggestionItemProps, loading }) => (
											<div>
												<Form.Item label='Destination Line 1'>
													<div style={{ position: 'relative' }}>
														<Input
															{...getInputProps({
																placeholder: 'Destination Line 1 ...',
															})}
														/>
														{suggestions.length > 0 && ( // Check if suggestions array has items
															<div
																style={this.autocompleteDropdownStyle}
																className="autocomplete-dropdown-container"
															>
																{loading && <div>Loading...</div>}
																{suggestions.map(suggestion => {
																	const style = {
																		backgroundColor: suggestion.active ? '#f1f2f6' : '#ffffff',
																		cursor: 'pointer',
																	};
																	return (
																		<div
																			key={suggestion.id} // Add a unique key for each suggestion
																			{...getSuggestionItemProps(suggestion, {
																				style,
																			})}
																		>
																			<List.Item>
																				<List.Item.Meta
																					description={suggestion.description}
																				/>
																			</List.Item>
																		</div>
																	);
																})}
															</div>
														)}
													</div>
												</Form.Item>
											</div>
										)}
									</PlacesAutocomplete>


									{/* <Form.Item label='Destination Line 1'>
										{getFieldDecorator("delivery_address", {})(<Input placeholder='Destination Line 1' />)}
									</Form.Item> */}

									<Form.Item label='Destination Line 2'>
										{getFieldDecorator("delivery_address2", {})(<Input placeholder='Destination Line 2' />)}
									</Form.Item>

									<Form.Item label='Destination City'>
										{getFieldDecorator("delivery_city", {
											initialValue: this.state.DeliveryCity ? this.state.DeliveryCity : "",
										})(<Input placeholder='Destination City' />)}
									</Form.Item>

									<Form.Item label='Destination State'>
										{getFieldDecorator("delivery_state", {
											initialValue: this.state.DeliveryState ? this.state.DeliveryState : "",
										})(<Input placeholder='Destination State' />)}
									</Form.Item>

									<Form.Item label='Destination Zipcode'>
										{getFieldDecorator("delivery_zipcode", {
											initialValue: this.state.DeliveryZipCode ? this.state.DeliveryZipCode : "",
										})(<Input placeholder='Destination Zipcode' />)}
									</Form.Item>

									<Form.Item label='Destination Country'>
										{getFieldDecorator("delivery_country", {
											initialValue: this.state.DeliveryCountry ? this.state.DeliveryCountry : "",
										})(<Input placeholder='Destination Country' />)}
									</Form.Item>


									<Form.Item label='Estimated Volume'>
										{getFieldDecorator("estimated_volume", {
											rules: [
												{
													validator: this.minVolumeOne
												},
											],
										})(<Input
											type="number"
											style={{ width: "100%" }}
											placeholder='Estimated Volume'
										/>)}
									</Form.Item>

									<Form.Item label='Estimated Weight'>
										{getFieldDecorator("estimated_weight", {
											rules: [
												{
													validator: this.minOne
												},
											],
										})
											(<Input
												type="number"
												style={{ width: "100%" }}
												placeholder='Estimated Weight'
												addonAfter="lbs"
											/>
											)
										}
									</Form.Item>

									{
										this.state.integrationKeyStatus ?
											"" :
											<Form.Item label='Warehouse Name'>
												{getFieldDecorator("warehouse", {})(<Input placeholder='Warehouse Name' />)}
											</Form.Item>
									}

									<Form.Item label='Move Coordinator'>
										{getFieldDecorator("move_coordinator", {})(<Input placeholder='Move Coordinator' />)}
									</Form.Item>

									<Form.Item label='Source'>
										{getFieldDecorator("source", {
											initialValue: { key: "Standalone" },
										})(
											<Select
												size={"default"}
												labelInValue
												placeholder='Select Source'
												style={{ width: "100%" }}>
												{this.state.sourceList.map((e) => (
													<Option key={e} value={e}>
														{e}
													</Option>
												))}
											</Select>
										)}
									</Form.Item>

									<Form.Item label='Contact Reference'>
										{getFieldDecorator("contact_reference", {})(<Input placeholder='Contact Reference' />)}
									</Form.Item>

									<Form.Item label='Account Reference'>
										{getFieldDecorator("account_reference", {})(<Input placeholder='Account Reference' />)}
									</Form.Item>

									<Form.Item label='Opportunity Reference'>
										{getFieldDecorator(
											"opportunity_reference",
											{}
										)(<Input placeholder='Opportunity Reference' />)}
									</Form.Item>

									<Form.Item label='WO Reference'>
										{getFieldDecorator("wo_reference", {})(<Input placeholder='WO Reference' />)}
									</Form.Item>

									<Form.Item label='External Reference'>
										{getFieldDecorator("external_reference", {})(<Input placeholder='External Reference' />)}
									</Form.Item>
									<Form.Item label='External Reference 2'>
										{getFieldDecorator("external_reference_2", {})(<Input placeholder='External Reference' />)}
									</Form.Item>
									<Form.Item label='External Reference 3'>
										{getFieldDecorator("external_reference_3", {})(<Input placeholder='External Reference' />)}
									</Form.Item>

									<Form.Item {...tailFormItemLayout}>
										<Button
											className='submitButton'
											loading={this.state.loading}
											type='primary'
											htmlType='submit'>
											Add Shipment
										</Button>
									</Form.Item>
								</Form>
							</Spin>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

const mapStateToProps = (state) => {
	return {
		company: state.Auth.company,
		companyID: Number(state.Auth.companyID),
	};
};

export default Form.create()(connect(mapStateToProps, { changeCurrent })(AddJob));
