import {
	Button,
	DatePicker,
	Form,
	Icon,
	Input,
	InputNumber,
	message,
	Select,
	Spin,
	Tag,
	List,
	Checkbox
} from "antd";
import moment from "moment";
import React from "react";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { MOVER_STORAGE_API_URL } from "../../static/data/constants";
import axios from 'axios';
import { GOOGLE_API_KEY } from "../../static/data/constants";

import PlacesAutocomplete, {
	geocodeByAddress,
} from 'react-places-autocomplete';

const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { TextArea } = Input;
const { Option } = Select;

class EditJob extends React.Component {
	state = {
		jobData: null,
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		sourceList: ["Standalone", "External", "MG"],
		fileList: [],
		companyList: [],
		tags: [],
		shipmentTypeData: [],
		customerData: [],
		allCustomerList: [],
		customerEmail: "",
		staffData: [],
		adminId: "",
		companyId: "",
		staffId: "",
		integrationKeyStatus: false,
		iShipmentAssignToUnits: false,
		warehouseLoader: false,
		integrationKey: "",
		warehousesList: [],
		storage_customer_id: "",
		storage_job_id: "",
		PickupAddress: "",
		PickupCity: "",
		PickupCountry: "",
		PickupState: "",
		PickupZipCode: "",
		DeliveryAddress: "",
		DeliveryCity: "",
		DeliveryCountry: "",
		DeliveryState: "",
		DeliveryZipCode: "",
		isJobComplete: false,
		isFirstStage: false,
		warehouseId: null
	};


	componentDidMount() {

		this.setState({
			adminId:
				((localStorage.getItem("adminid") !== ""))
					? localStorage.getItem("adminid")
					: null,
			companyId:
				((localStorage.getItem("companyID") !== ""))
					? localStorage.getItem("companyID")
					: null,
			staffId:
				((localStorage.getItem("staffId") !== ""))
					? localStorage.getItem("staffId")
					: null,
		});

		this.setState({ contentloader: true, loading: true, warehouseLoader: true })


		API.get(`api/admin/integrationKey/fetch`)
			.then((response) => {
				if (response) {
					if (response.data.status === "active") {
						this.setState({
							integrationKeyStatus: true,
							integrationKey: response.data.integration_key,
							storageCompanyId: response.data.key_company.storage_company_id
						});
						this.consumerLoginJson(response.data.integration_key);
					} else {
						this.setState({
							contentloader: false,
							loading: false,
							warehouseLoader: false,
							integrationKeyStatus: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		const id = this.props.match.params.id;
		this.props.changeCurrent("job");
		this.setState({ contentloader: true });

		const data1 = [];
		API.get("api/admin/shipment-type/basic/", data1)
			.then((response) => {
				if (response) {


					const newData = response.data.filter((data, i) => {
						return data.company_id == this.state.companyId
					})
					this.setState({
						shipmentTypeData: newData,
						contentloader: false,
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.get(`api/admin/tag/list/shipment`)
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							tags: response.data,
							contentloader: false,
						});
					} else {
						message.error(response.message);
						this.setState({ contentloader: false });
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.post("api/admin/company/list")
			.then((response) => {
				if (response) {
					if (response.status === 1) {
						this.setState({
							companyList: response.data.companyList.rows,
							contentloader: false,
						});
					} else {
						this.setState({
							usersList: response.data,
							contentloader: false,
						});
					}
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.get("api/admin/customer/basic/", data1)
			.then((response) => {
				if (response) {

					this.setState({
						allCustomerList: response.data,
						customerData: response.data,
						contentloader: false,
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		API.get("api/admin/staff/basic/0", data1)
			.then((response) => {
				if (response) {
					this.setState({
						staffData: response.data && response.data,
						contentloader: false,
					});
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});

		const data = [];
		data["data"] = { job_id: id };
		API.get("api/admin/shipment/" + id, data)
			.then((response) => {
				if (response) {
					this.setState(
						{
							PickupAddress:
								response.data &&
									response.data.pickup_address &&
									response.data.pickup_address !== "" &&
									response.data.pickup_address !== "undefined"
									? response.data.pickup_address
									: null,

							PickupCity:
								response.data &&
									response.data.pickup_city &&
									response.data.pickup_city !== "" &&
									response.data.pickup_city !== "undefined"
									? response.data.pickup_city
									: null,

							PickupState:
								response.data &&
									response.data.pickup_state &&
									response.data.pickup_state !== "" &&
									response.data.pickup_state !== "undefined"
									? response.data.pickup_state
									: null,

							PickupCountry:
								response.data &&
									response.data.pickup_country &&
									response.data.pickup_country !== "" &&
									response.data.pickup_country !== "undefined"
									? response.data.pickup_country
									: null,

							PickupZipCode:
								response.data &&
									response.data.pickup_zipcode &&
									response.data.pickup_zipcode !== "" &&
									response.data.pickup_zipcode !== "undefined"
									? response.data.pickup_zipcode
									: null,

							DeliveryAddress:
								response.data &&
									response.data.delivery_address &&
									response.data.delivery_address !== "" &&
									response.data.delivery_address !== "undefined"
									? response.data.delivery_address
									: null,

							DeliveryCity:
								response.data &&
									response.data.delivery_city &&
									response.data.delivery_city !== "" &&
									response.data.delivery_city !== "undefined"
									? response.data.delivery_city
									: null,

							DeliveryState:
								response.data &&
									response.data.delivery_state &&
									response.data.delivery_state !== "" &&
									response.data.delivery_state !== "undefined"
									? response.data.delivery_state
									: null,

							DeliveryCountry:
								response.data &&
									response.data.delivery_country &&
									response.data.delivery_country !== "" &&
									response.data.delivery_country !== "undefined"
									? response.data.delivery_country
									: null,

							DeliveryZipCode:
								response.data &&
									response.data.delivery_zipcode &&
									response.data.delivery_zipcode !== "" &&
									response.data.delivery_zipcode !== "undefined"
									? response.data.delivery_zipcode
									: null,


							jobData: response.data && response.data,
							warehouseId: response.data && response.data.warehouseId,
							customerEmail: response.data && response.data.email,
							storage_customer_id: response.data && response.data.customer_job && response.data.customer_job.storage_customer_id,
							storage_job_id: response.data && response.data.storage_shipment_job_id,
							isJobComplete: response.data && response.data.is_job_complete_flag === 1 ? true : false,
							isFirstStage: response.data && response.data.is_first_stage === "no" ? true : false,
							iShipmentAssignToUnits: response.data && response.data.is_shipment_assign_to_units === "true" ? true : false

						},
						() => this.props.form.setFieldsValue({})
					);
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}



	consumerLoginJson = (integrationKey) => {
		this.setState({ contentloader: true, loading: true, warehouseLoader: true })

		const consumerLoginJson = JSON.stringify({
			companyIdTokenMoverInventory: this.state.integrationKey ? this.state.integrationKey : integrationKey,
			email: "<EMAIL>",
			password: "5PLaRAqq",
			deviceToken: "abcd",
			deviceType: 0,
		});

		axios.post(`${MOVER_STORAGE_API_URL}login`, consumerLoginJson,
			{
				headers: {
					'Content-Type': 'application/json'
				}
			})
			.then((consumerLoginResponse) => {
				this.setState({ consumerLoginAccessToken: consumerLoginResponse.data.data.accessToken });
				this.fetchCompanyResponse(integrationKey, consumerLoginResponse.data.data.accessToken);
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false, warehouseLoader: false });
			});
	}

	fetchCompanyResponse = (integrationKey, accessToken) => {
		this.setState({ contentloader: true, loading: true, warehouseLoader: true })

		const fetchCompanyJson = JSON.stringify({
			companyIdTokenMoverInventory: this.state.integrationKey ? this.state.integrationKey : integrationKey,
		});

		axios.post(`${MOVER_STORAGE_API_URL}import/mover-inventory/companies`, fetchCompanyJson,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': this.state.consumerLoginAccessToken ? this.state.consumerLoginAccessToken : accessToken
				}
			})
			.then((fetchCompanyResponse) => {
				this.setState({ warehousesList: fetchCompanyResponse.data.data.warehouses });
				this.setState({ contentloader: false, loading: false, warehouseLoader: false });
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false, warehouseLoader: false });
			});
	}

	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};

	minOne = (rule, value, callback) => {
		if (value && value <= 0) {
			callback("Estimated weight must be greater then 0!");
		}
		else if (value && value > 10000000) {
			callback("Estimated weight must be less than or equal to 10000000!");
		}
		else {
			callback();
		}
	};

	handleUpload = ({ fileList }) => {
		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};
	fetchCustomerDropdownList = (serchProps) => {
		API.get(`api/admin/customer/basic/?search=${serchProps.searchFieldValue}`)
			.then((response) => {
				if (response) {

					this.setState({
						customerData: response.data,
						contentloader: false,
					});
				} else {
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	};
	OnCustomerChange = (values) => {
		this.setState({ customerEmail: values });
	};
	onSearch = (value) => {
		const data = {
			searchFieldValue: value,
		};
		this.fetchCustomerDropdownList(data);
	};
	isCompanyExist = (id, companyData) => {
		let status = false;
		for (let i = 0; i < companyData.length; i++) {
			let company_id = companyData[i].company_id;
			if (company_id === id) {
				status = true;
				break;
			}
		}
		return status;
	};


	editShipmentJson = async (response) => {
		function formatDate(inputDate) {
			const date = new Date(inputDate);
			const year = date.getUTCFullYear();
			const month = String(date.getUTCMonth() + 1).padStart(2, '0');
			const day = String(date.getUTCDate()).padStart(2, '0');
			return `${month}-${day}-${year}`;
		}
		const editShipmentJson = JSON.stringify(
			{
				id: this.state.jobData.storage_shipment_job_id,
				shipmentName: (response.shipment_name !== undefined) ? response.shipment_name : "",
				customerId: this.state.storage_customer_id,
				warehouseId: (response.warehouseId !== undefined) ? response.warehouseId : "",
				contactReference: (response.contact_reference !== undefined) ? response.contact_reference : "",
				accountReference: (response.account_reference !== undefined) ? response.account_reference : "",
				opportunityTitle: "",
				opportunityReference: (response.opportunity_reference !== undefined) ? response.opportunity_reference : '',
				estArrival: formatDate(response.pickup_date),
				estDelivery: formatDate(response.delivery_date),
				workOrderNotes: [

				],
				workOrderReference: "",
				externalReference: [

				],
				moveCoord: "",
				source: (response.source !== undefined) ? response.source : "",
				volume: (response.estimated_volume !== undefined) ? response.estimated_volume : "",
				weight: (response.estimated_weight !== undefined) ? response.estimated_weight : "",
				estUnits: "",
				originAddress: {
					addressLine1: (response.pickup_address !== undefined) ? response.pickup_address : "",
					addressLine2: (response.pickup_address2 !== undefined) ? response.pickup_address2 : "",
					city: (response.pickup_city !== undefined) ? response.pickup_city : "",
					state: (response.pickup_state !== undefined) ? response.pickup_state : "",
					zipcode: (response.pickup_zipcode !== undefined) ? response.pickup_state : "",
					country: (response.pickup_country !== undefined) ? response.pickup_country : ""
				},
				destinationAddress: {
					addressLine1: (response.delivery_address !== undefined) ? response.delivery_address : "",
					addressLine2: (response.delivery_address2 !== undefined) ? response.delivery_address2 : "",
					city: (response.delivery_city !== undefined) ? response.delivery_city : "",
					state: (response.delivery_state !== undefined) ? response.delivery_city : "",
					zipcode: (response.delivery_zipcode !== undefined) ? response.delivery_zipcode : "",
					country: (response.delivery_country !== undefined) ? response.delivery_country : ""
				},
				importedTags: [
				]
			}
		);


		axios.put(`${MOVER_STORAGE_API_URL}import/mover-inventory/shipments`, editShipmentJson,
			{
				headers: {
					'Content-Type': 'application/json',
					'accessToken': this.state.consumerLoginAccessToken
				}
			})
			.then((editShipmentResponse) => {
				this.setState({ loading: false, contentloader: false });
				message.success("Shipment update successfully.");
				this.props.history.goBack();
			})
			.catch((error) => {
				message.success("Shipment update fail!");
				this.setState({ loading: false, contentloader: false });
			});
	}

	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFieldsAndScroll((err, values) => {
			const tag = [];
			if (values.tags) {
				values.tags.forEach((item) => {
					tag.push(item.key);
				});
			}


			let params = {
				shipment_name: (values.shipment_name !== undefined) ? values.shipment_name : "",
				company_id: (values.company_id.key !== undefined) ? values.company_id.key : "",
				email: (values.email !== undefined) ? values.email : "",
				warehouseId: this.state.integrationKeyStatus ? values.warehouse_id.key : null,
				customer_id: (values.customer_id.key !== undefined) ? values.customer_id.key : "",
				pickup_address: this.state.PickupAddress,
				pickup_address2: (values.pickup_address2 !== undefined) ? values.pickup_address2 : "",
				pickup_city: (values.pickup_city !== undefined) ? values.pickup_city : "",
				pickup_state: (values.pickup_state !== undefined) ? values.pickup_state : "",
				pickup_zipcode: (values.pickup_zipcode !== undefined) ? values.pickup_zipcode : "",
				pickup_country: (values.pickup_country !== undefined) ? values.pickup_country : "",
				delivery_address: this.state.DeliveryAddress,
				delivery_address2: (values.delivery_address2 !== undefined) ? values.delivery_address2 : "",
				delivery_city: (values.delivery_city !== undefined) ? values.delivery_city : "",
				delivery_state: (values.delivery_state !== undefined) ? values.delivery_state : "",
				delivery_zipcode: (values.delivery_zipcode !== undefined) ? values.delivery_zipcode : "",
				delivery_country: (values.delivery_country !== undefined) ? values.delivery_country : "",
				external_reference: (values.external_reference !== undefined) ? values.external_reference : "",
				external_reference_2: (values.external_reference_2 !== undefined) ? values.external_reference_2 : "",
				external_reference_3: (values.external_reference_3 !== undefined) ? values.external_reference_3 : "",
				pickup_date: (values.pickup_date_estimated !== undefined && values.pickup_date_estimated !== "") ? values.pickup_date_estimated : null,
				delivery_date: (values.delivery_date_estimated !== undefined && values.delivery_date_estimated !== "") ? values.delivery_date_estimated : null,
				estimated_weight: (values.estimated_weight !== undefined && values.estimated_weight !== "") ? values.estimated_weight : null,
				estimated_volume: (values.estimated_volume !== undefined && values.estimated_volume !== "") ? values.estimated_volume : null,
				notes: (values.notes !== undefined) ? values.notes : "",
				warehouse: (values.warehouse !== undefined) ? values.warehouse : "",
				source: (values.source.key !== undefined) ? values.source.key : "",
				contact_reference: (values.contact_reference !== undefined) ? values.contact_reference : "",
				account_reference: (values.account_reference !== undefined) ? values.account_reference : "",
				opportunity_reference: (values.opportunity_reference !== undefined) ? values.opportunity_reference : "",
				move_coordinator: (values.move_coordinator !== undefined) ? values.move_coordinator : "",
				wo_reference: (values.wo_reference !== undefined) ? values.wo_reference : "",
				tag: JSON.stringify(tag),
			};

			if (!err) {
				const data = [];

				data["data"] = params;
				this.setState({ loading: true, contentloader: true });


				API.put("api/admin/shipment/" + this.props.match.params.id, data)
					.then((response) => {
						if (response) {
							if (this.state.integrationKeyStatus && (this.state.jobData.storage_shipment_job_id !== undefined && this.state.jobData.storage_shipment_job_id !== "" && this.state.jobData.storage_shipment_job_id !== null)) {
								this.editShipmentJson(params)
							}
							else {
								this.setState({ loading: false, contentloader: false });
								message.success(response.message);
								this.props.history.goBack();
							}
						} else {
							this.setState({ loading: false, contentloader: false });
							// message.error(response.message);
						}
					})
					.catch((error) => {
						this.setState({ loading: false, contentloader: false });

					});
			}
		});
	};


	handleChangePickupAddress = PickupAddress => {
		this.setState({ PickupAddress });
	};

	handleSelectPickupAddress = (PickupAddress, placeId) => {
		if (placeId !== undefined && placeId !== "" && placeId !== null) {
			this.fetchPlaceDetails(placeId);
		}
		else {
			this.setState({ PickupAddress: PickupAddress });
		}
	};

	fetchPlaceDetails = async (placeId) => {
		let params = {
			placeId: placeId,
		};
		const data = [];
		data["data"] = params;
		this.setState({ loading: true, contentloader: true });
		API.post("api/admin/google/place/details", data)
			.then((response) => {
				const addressComponents = response.data && response.data.result && response.data.result.address_components;
				// Initialize variables to store city, state, and country
				let address, city, state, country, zipCode;

				// Iterate through the address components to find city, state, and country
				for (const component of addressComponents) {
					for (const type of component.types) {
						if (type === 'street_number' || type === 'route') {
							address = address ? `${address} ${component.long_name}` : component.long_name;
						} else if (type === 'locality') {
							city = component.long_name;
						} else if (type === 'administrative_area_level_1') {
							state = component.long_name;
						} else if (type === 'country') {
							country = component.long_name;
						} else if (type === 'postal_code') {
							zipCode = component.long_name;
						}
					}
				}

				this.setState({
					PickupAddress: address,
					PickupCity: city,
					PickupState: state,
					PickupCountry: country,
					PickupZipCode: zipCode
				});

				this.props.form.setFieldsValue({
					pickup_city: city,
					pickup_state: state,
					pickup_zipcode: zipCode,
					pickup_country: country,
				});
				this.setState({ loading: false, contentloader: false });
			})
			.catch((error) => {
				this.setState({ loading: false, contentloader: false });
				message.error(error.message);
			});
	};

	handleChangeDeliveryAddress = DeliveryAddress => {
		this.setState({ DeliveryAddress });
	};

	handleSelectDeliveryAddress = (PickupAddress, placeId) => {
		if (placeId !== undefined && placeId !== "" && placeId !== null) {
			this.fetchPlaceDetailsDelivery(placeId);
		}
		else {
			this.setState({ DeliveryAddress: PickupAddress });
		}
	};

	fetchPlaceDetailsDelivery = async (placeId) => {
		let params = {
			placeId: placeId,
		};
		const data = [];
		data["data"] = params;
		this.setState({ loading: true, contentloader: true });
		API.post("api/admin/google/place/details", data)
			.then((response) => {
				const addressComponents = response.data && response.data.result && response.data.result.address_components;
				// Initialize variables to store city, state, and country
				let address, city, state, country, zipCode;

				// Iterate through the address components to find city, state, and country
				for (const component of addressComponents) {
					for (const type of component.types) {
						if (type === 'street_number' || type === 'route') {
							address = address ? `${address} ${component.long_name}` : component.long_name;
						} else if (type === 'locality') {
							city = component.long_name;
						} else if (type === 'administrative_area_level_1') {
							state = component.long_name;
						} else if (type === 'country') {
							country = component.long_name;
						} else if (type === 'postal_code') {
							zipCode = component.long_name;
						}
					}
				}
				this.setState({
					DeliveryAddress: address,
					DeliveryCity: city,
					DeliveryState: state,
					DeliveryCountry: country,
					DeliveryZipCode: zipCode
				});

				this.props.form.setFieldsValue({
					delivery_city: city,
					delivery_state: state,
					delivery_zipcode: zipCode,
					delivery_country: country,
				});

				this.setState({ loading: false, contentloader: false });
			})
			.catch((error) => {
				this.setState({ loading: false, contentloader: false });
				message.error(error.message);
			});
	};

	autocompleteDropdownStyle = {
		position: 'absolute',
		top: '100%',
		left: 0,
		width: '500px',
		zIndex: 1,
		backgroundColor: '#ffffff',
		border: '1px solid #ccc',
		boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
		maxHeight: '200px',
		overflowY: 'auto',
		padding: '5px',
	};

	render() {




		const { getFieldDecorator } = this.props.form;
		const { jobData, customerEmail, companyList, allCustomerList } = this.state;

		const initialWarehouse =
			jobData && jobData.warehouseId === "" ? {} : { key: jobData && jobData.warehouseId };

		const initialShipmentType =
			jobData && jobData.shipment_type_id === "" ? {} : { key: jobData && jobData.shipment_type_id };
		const initialTag =
			jobData &&
			jobData.shipment_tag.map((item, index) =>
				item.m2m_tag.tag_id === "" ? {} : { key: item.m2m_tag.tag_id }
			);

		const initialCompany =
			(jobData && jobData.company_id === "") ||
				(jobData && jobData.company_id && !this.isCompanyExist(jobData.company_id, companyList))
				? {}
				: { key: jobData && jobData.company_id };
		const initialCustomer =
			jobData && jobData.customer_id === "" ? {} : { key: jobData && jobData.customer_id };
		const initialSource = jobData && jobData.source === "" ? {} : { key: jobData && jobData.source };
		const initialEmail = customerEmail;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		function disabledDate(current) {
			return current && current < moment().endOf('day').subtract(1, 'day');
		}

		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }} >
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Edit Shipment
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent
					style={{
						margin: "0 20px",
						height: "93%",
						overflowY: "auto",
					}}>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Spin spinning={this.state.warehouseLoader} indicator={antIcon}>
								<Form {...formItemLayout} onSubmit={this.handleSubmit}>
									<Form.Item label='Company Name'>
										{getFieldDecorator("company_id", {
											initialValue: initialCompany,
											rules: [
												{
													required: true,
													message: "Please select Company name",
												},
											],
										})(
											<Select
												size={"default"}
												labelInValue
												disabled
												placeholder='Select Company Name'
												style={{ width: "100%" }}>
												{companyList
													? companyList.map((e) => (
														<Option key={e.company_name} value={e.company_id}>
															{e.company_name}
														</Option>
													))
													: null}
											</Select>
										)}
									</Form.Item>
									<Form.Item label='Shipment Type'>
										{getFieldDecorator("shipment_type", {
											initialValue: initialShipmentType,
											rules: [
												{
													required: true,
													message: "Please select shipment type",
												},
											],
										})(
											<Select
												disabled='disabled'
												size={"default"}
												labelInValue
												placeholder='Select Shipment Type'
												style={{ width: "100%" }}>
												{this.state.shipmentTypeData
													? this.state.shipmentTypeData.map((e) => (
														<Option key={e.shipment_type_id} value={e.shipment_type_id}>
															{e.name}
														</Option>
													))
													: null}
											</Select>
										)}
									</Form.Item>
									<Form.Item label='Customer Name'>
										{getFieldDecorator("customer_id", {
											initialValue: initialCustomer,
											rules: [
												{
													required: true,
													message: "Please select customer name",
												},
											],
										})(
											<Select
												disabled={this.state.isJobComplete || this.state.isFirstStage ? true : false}
												size={"default"}
												showSearch
												labelInValue
												optionFilterProp='children'
												placeholder='Select Customer Name'
												style={{ width: "100%" }}
												onSearch={this.onSearch}>
												{this.state.customerData
													? this.state.customerData.map((e) => (
														<Option
															key={e.customer_id}
															value={e.customer_id}
															onClick={this.OnCustomerChange.bind(null, e.email)}>
															{e.full_name}
														</Option>
													))
													: null}
											</Select>
										)}
									</Form.Item>
									<Form.Item label='Email'>
										{getFieldDecorator("email", {
											initialValue: initialEmail,
											rules: [
												{
													type: "email",
													message: "The input is not valid Email!",
												},
												{
													required: true,
													message: "Please input your Email!",
												},
											],
										})(<Input readOnly />)}
									</Form.Item>
									<Form.Item label='Shipment Name'>
										{getFieldDecorator("shipment_name", {
											initialValue: jobData && jobData.shipment_name,
											placeholder: "Shipment Name",
											rules: [
												{
													required: true,
													message: "Please input shipment name!",
													whitespace: true,
												},
											],
										})(<Input placeholder='Shipment Name' />)}
									</Form.Item>
									<Form.Item label='Tags'>
										{getFieldDecorator("tags", {
											initialValue: initialTag && initialTag[0] ? initialTag : [],
										})(
											<Select
												getPopupContainer={trigger => trigger.parentNode}
												mode='multiple'
												labelInValue
												allowClear
												style={{ width: "100%" }}
												placeholder='Please select tags'>
												{this.state.tags.map((item, index) => (
													<Option value={item.tag_id} key={item.tag_id}>
														<Tag color={item.color ? item.color : ""}>{item.name}</Tag>
													</Option>
												))}
											</Select>
										)}
									</Form.Item>
									{
										this.state.integrationKeyStatus ?
											<Form.Item label='Warehouses'>
												{getFieldDecorator("warehouse_id", {
													initialValue: initialWarehouse
												})(
													<Select
														disabled={this.state.iShipmentAssignToUnits}
														size={"default"}
														labelInValue
														placeholder='Select warehouse'
														style={{ width: "100%" }}>
														{this.state.warehousesList
															? this.state.warehousesList.map((e) => (
																<Option key={e.id} value={e.id}>
																	{e.name == "Sample Warehouse" ? e.name + " " + `(${e.companyName})` : e.name}
																</Option>
															))
															: null}
													</Select>
												)}
											</Form.Item>
											:
											""
									}
									<Form.Item label='Pickup Date'>
										{getFieldDecorator("pickup_date_estimated",
											{
												initialValue:
													jobData && jobData.pickup_date && moment(jobData.pickup_date, "YYYY/MM/DD HH:mm:ss"),
											})(
												<DatePicker
													disabledDate={disabledDate}
													defaultPickerValue={moment()}
													placeholder='Pickup date'
												/>
											)}
									</Form.Item>

									<Form.Item label='Estimated Delivery'>
										{getFieldDecorator("delivery_date_estimated", {
											initialValue:
												jobData && jobData.delivery_date && moment(jobData.delivery_date, "YYYY/MM/DD HH:mm:ss"),
										})(
											<DatePicker
												disabledDate={disabledDate}
												defaultPickerValue={moment()}
												placeholder='Estimated delivery date'
											/>
										)}
									</Form.Item>
									<Form.Item label='Work Order Notes'>
										{getFieldDecorator("notes", {
											initialValue: jobData && jobData.notes,
										})(<TextArea rows={4} placeholder='Work Order Notes' />)}
									</Form.Item>
									{/* 									
									<Form.Item label='Origin Line 1'>
										{getFieldDecorator("pickup_address", {
											initialValue:
												jobData &&
													jobData.pickup_address &&
													jobData.pickup_address !== "" &&
													jobData.pickup_address !== "undefined"
													? jobData.pickup_address
													: null,
										})(<Input placeholder='Origin Line 1' />)}
									</Form.Item> */}


									<PlacesAutocomplete
										value={this.state.PickupAddress}
										onChange={this.handleChangePickupAddress}
										onSelect={this.handleSelectPickupAddress}
									>
										{({ getInputProps, suggestions, getSuggestionItemProps, loading }) => (
											<div>
												<Form.Item label='Origin Line 1'>
													<div style={{ position: 'relative' }}>
														<Input
															{...getInputProps({
																placeholder: 'Origin Line 1 ...',
															})}
														/>
														{suggestions.length > 0 && ( // Check if suggestions array has items
															<div
																style={this.autocompleteDropdownStyle}
																className="autocomplete-dropdown-container"
															>
																{loading && <div>Loading...</div>}
																{suggestions.map(suggestion => {
																	const style = {
																		backgroundColor: suggestion.active ? '#f1f2f6' : '#ffffff',
																		cursor: 'pointer',
																	};
																	return (
																		<div
																			key={suggestion.id} // Add a unique key for each suggestion
																			{...getSuggestionItemProps(suggestion, {
																				style,
																			})}
																		>
																			<List.Item>
																				<List.Item.Meta
																					description={suggestion.description}
																				/>
																			</List.Item>
																		</div>
																	);
																})}
															</div>
														)}
													</div>
												</Form.Item>

											</div>
										)}
									</PlacesAutocomplete>


									<Form.Item label='Origin Line 2'>
										{getFieldDecorator("pickup_address2", {
											initialValue:
												jobData &&
													jobData.pickup_address2 &&
													jobData.pickup_address2 !== "" &&
													jobData.pickup_address2 !== "undefined"
													? jobData.pickup_address2
													: null,
										})(<Input placeholder='Origin Line 2' />)}
									</Form.Item>
									<Form.Item label='Origin City'>
										{getFieldDecorator("pickup_city", {
											initialValue: this.state.PickupCity,
										})(<Input placeholder='Origin City' />)}
									</Form.Item>
									<Form.Item label='Origin State'>
										{getFieldDecorator("pickup_state", {
											initialValue: this.state.PickupState,
										})(<Input placeholder='Origin State' />)}
									</Form.Item>
									<Form.Item label='Origin Zipcode'>
										{getFieldDecorator("pickup_zipcode", {
											initialValue: this.state.PickupZipCode,
										})(<Input placeholder='Origin Zipcode' />)}
									</Form.Item>
									<Form.Item label='Origin Country'>
										{getFieldDecorator("pickup_country", {
											initialValue: this.state.PickupCountry,
										})(<Input placeholder='Origin Country' />)}
									</Form.Item>

									{/* <Form.Item label='Destination Line 1'>
										{getFieldDecorator("delivery_address", {
											initialValue: jobData && jobData.delivery_address,
										})(<Input placeholder='Destination Line 1' />)}
									</Form.Item> */}


									<PlacesAutocomplete
										value={this.state.DeliveryAddress}
										onChange={this.handleChangeDeliveryAddress}
										onSelect={this.handleSelectDeliveryAddress}
									>
										{({ getInputProps, suggestions, getSuggestionItemProps, loading }) => (
											<div>
												<Form.Item label='Destination Line 1'>
													<div style={{ position: 'relative' }}>
														<Input
															{...getInputProps({
																placeholder: 'Destination Line 1 ...',
															})}
														/>
														{suggestions.length > 0 && ( // Check if suggestions array has items
															<div
																style={this.autocompleteDropdownStyle}
																className="autocomplete-dropdown-container"
															>
																{loading && <div>Loading...</div>}
																{suggestions.map(suggestion => {
																	const style = {
																		backgroundColor: suggestion.active ? '#f1f2f6' : '#ffffff',
																		cursor: 'pointer',
																	};
																	return (
																		<div
																			key={suggestion.id} // Add a unique key for each suggestion
																			{...getSuggestionItemProps(suggestion, {
																				style,
																			})}
																		>
																			<List.Item>
																				<List.Item.Meta
																					description={suggestion.description}
																				/>
																			</List.Item>
																		</div>
																	);
																})}
															</div>
														)}
													</div>
												</Form.Item>

											</div>
										)}
									</PlacesAutocomplete>


									<Form.Item label='Destination Line 2'>
										{getFieldDecorator("delivery_address2", {
											initialValue: jobData && jobData.delivery_address2,
										})(<Input placeholder='Destination Line 2' />)}
									</Form.Item>
									<Form.Item label='Destination City'>
										{getFieldDecorator("delivery_city", {
											initialValue: this.state.DeliveryCity,
										})(<Input placeholder='Destination City' />)}
									</Form.Item>
									<Form.Item label='Destination State'>
										{getFieldDecorator("delivery_state", {
											initialValue: this.state.DeliveryState,
										})(<Input placeholder='Destination State' />)}
									</Form.Item>
									<Form.Item label='Destination Zipcode'>
										{getFieldDecorator("delivery_zipcode", {
											initialValue: this.state.DeliveryZipCode,
										})(<Input placeholder='Destination Zipcode' />)}
									</Form.Item>
									<Form.Item label='Destination Country'>
										{getFieldDecorator("delivery_country", {
											initialValue: this.state.DeliveryCountry,
										})(<Input placeholder='Destination Country' />)}
									</Form.Item>


									<Form.Item label='Actual Delivery'>
										{getFieldDecorator("actual_delivery", {
											initialValue:
												jobData &&
												jobData.actual_delivery &&
												moment(jobData.actual_delivery, "YYYY/MM/DD HH:mm:ss"),
										})(<DatePicker placeholder='Estimated actual delivery' disabled />)}
									</Form.Item>
									<Form.Item label='Estimated Volume'>
										{getFieldDecorator("estimated_volume", {
											initialValue: jobData && jobData.estimated_volume,
											placeholder: "Estimated Volume",
										})(<Input type="number" style={{ width: "100%" }} placeholder='Estimated Volume' />)}
									</Form.Item>
									<Form.Item label='Estimated Weight'>
										{getFieldDecorator("estimated_weight", {
											initialValue: jobData && jobData.estimated_weight,
											placeholder: "Estimated Weight",
											rules: [
												{
													validator: this.minOne
												},
											],
										})(<Input
											addonAfter="lbs"
											type="number"
											style={{ width: "100%" }}
											placeholder='Estimated Weight'
										/>)}
									</Form.Item>

									{
										(this.state.integrationKeyStatus && (this.state.storage_job_id !== null && this.state.storage_job_id !== "" && this.state.storage_job_id !== undefined)) ?
											"" :
											<Form.Item label='Warehouse Name'>
												{getFieldDecorator("warehouse", {
													initialValue: jobData && jobData.warehouse,
													placeholder: "Warehouse Name",
												})(<Input placeholder='Warehouse Name' />)}
											</Form.Item>
									}

									<Form.Item label='Move Coordinator'>
										{getFieldDecorator("move_coordinator", {
											initialValue: jobData && jobData.move_coordinator,
										})(<Input placeholder='Move Coordinator' />)}
									</Form.Item>

									<Form.Item label='Source'>
										{getFieldDecorator("source", {
											initialValue: initialSource,
										})(
											<Select
												size={"default"}
												labelInValue
												placeholder='Select Source'
												style={{ width: "100%" }}>
												{this.state.sourceList.map((e) => (
													<Option key={e} value={e}>
														{e}
													</Option>
												))}
											</Select>
										)}
									</Form.Item>

									<Form.Item label='Contact Reference'>
										{getFieldDecorator("contact_reference", {
											initialValue: jobData && jobData.contact_reference,
										})(<Input placeholder='Contact Reference' />)}
									</Form.Item>

									<Form.Item label='Account Reference'>
										{getFieldDecorator("account_reference", {
											initialValue: jobData && jobData.account_reference,
										})(<Input placeholder='Account Reference' />)}
									</Form.Item>

									<Form.Item label='Opportunity Reference'>
										{getFieldDecorator("opportunity_reference", {
											initialValue: jobData && jobData.opportunity_reference,
										})(<Input placeholder='Opportunity Reference' />)}
									</Form.Item>

									<Form.Item label='WO Reference'>
										{getFieldDecorator("wo_reference", {
											initialValue: jobData && jobData.wo_reference,
										})(<Input placeholder='WO Reference' />)}
									</Form.Item>

									<Form.Item label='External Reference'>
										{getFieldDecorator("external_reference", {
											initialValue: jobData && jobData.external_reference,
											placeholder: "External Reference",
										})(<Input placeholder='External Reference' />)}
									</Form.Item>
									<Form.Item label='External Reference (optional)'>
										{getFieldDecorator("external_reference_2", {
											initialValue: jobData && jobData.external_reference_2,
											placeholder: "External Reference",
										})(<Input placeholder='External Reference' />)}
									</Form.Item>
									<Form.Item label='External Reference (optional)'>
										{getFieldDecorator("external_reference_3", {
											initialValue: jobData && jobData.external_reference_3,
											placeholder: "External Reference",
										})(<Input placeholder='External Reference' />)}
									</Form.Item>


									<Form.Item {...tailFormItemLayout}>
										<Button
											className='submitButton'
											loading={this.state.loading}
											type='primary'
											htmlType='submit'>
											Submit
										</Button>
									</Form.Item>
								</Form>
							</Spin>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditJob));
