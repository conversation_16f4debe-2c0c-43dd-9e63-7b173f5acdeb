import { Card, Col, Divider, Form, Icon, Modal, Input, message, Radio, Row, Spin, Button, Select, Switch } from "antd";
import React from "react";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import QRCode from "qrcode.react";


const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { Option } = Select;


class editJobInventory extends React.Component {
    state = {
        contentloader: false,
        contentloaderRoom: false,
        contentloaderUnit: false,
        integrationKeyStatus: false,
        integrationKey: "",
        storageShipmentId: "",
        companyId: "",
        inventoryData: {},
        roomList: [],
        unitList: [],
        userList: [],
        sourceList: ["Member", "Spouse"],
        isDisassmbledCheck: false,
        isElectronicCheck: false,
        isHighValueCheck: false,
        isProGearCheck: false,
        isFirearmsCheck: false,
        isCartonCheck: false,
        isDisassmbledByUserHandler: false,
        scanIntoStorageCheck: false,
        oldUnitId: null,
        oldStorageUnitId: null,
        userNameCheck: "",
    };

    componentDidMount() {
        this.setState({
            contentloader: true,
        });

        API.get(`api/admin/integrationKey/fetch`)
            .then((response) => {
                if (response) {
                    if (response.data.status === "active") {
                        this.setState({
                            integrationKeyStatus: true,
                            integrationKey: response.data.integration_key,
                            contentloader: false
                        });
                    } else {
                        this.setState({
                            contentloader: false,
                            integrationKeyStatus: false,
                        });
                    }
                }
            })
            .catch((error) => {
                this.setState({ contentloader: false });
            });

        const id = this.props.match.params.id;
        this.props.changeCurrent("job");

        this.setState({ contentloader: true, contentloaderRoom: true, contentloaderUnit: true, });

        API.get(`api/admin/inventory/${id}`)
            .then((response) => {
                if (response) {
                    this.setState({
                        inventoryData: response.data && response.data,
                        contentloader: false,
                        jobId: response.data && response.data.job_id,
                        isCartonCheck: response.data && response.data.is_carton === 1 ? true : false,
                        isDisassmbledCheck: response.data && response.data.is_disassembled === 1 ? true : false,
                        isElectronicCheck: response.data && response.data.is_electronics === 1 ? true : false,
                        isHighValueCheck: response.data && response.data.is_high_value === 1 ? true : false,
                        isProGearCheck: response.data && response.data.is_pro_gear === 1 ? true : false,
                        isFirearmsCheck: response.data && response.data.is_firearm === 1 ? true : false,
                        isCartonByUserCheck: response.data && response.data.is_carton === 1 && response.data.packed_by === "Carrier Packed" ? true : false,
                        isDisassmbledByUserCheck: response.data && response.data.is_disassembled === 1 && response.data.disassembled_by === "By Company" ? true : false,
                        storageShipmentId: response.data && response.data.shipment_job && response.data.shipment_job.storage_shipment_job_id,
                        companyId: response.data && response.data.shipment_job && response.data.shipment_job.company_id,
                        scanIntoStorageCheck: response.data && response.data.shipment_job && response.data.shipment_job.assign_storage_units_to_items == "yes" ? true : false,
                        oldUnitId: response.data && response.data.unit_id,
                        oldStorageUnitId: response.data && response.data.storage_unit_id,
                    });

                    this.fetchUnitListByShipmentId(response.data && response.data.job_id)
                } else {
                    message.error(response.message);
                    this.setState({ contentloader: false });
                }
            })
            .catch((error) => {
                this.setState({ contentloader: false });
            });

        this.setState({
            contentloaderRoom: true,
            contentloader: true
        });

        API.get(`api/home/<USER>
            .then((response) => {
                if (response) {
                    if (response.status === 1) {
                        this.setState({
                            roomList: response.data,
                            contentloader: false,
                            contentloaderRoom: false,
                        });
                    } else {
                        this.setState({
                            contentloader: false,
                            contentloaderRoom: false
                        });
                    }
                }
            })
            .catch((error) => {
                this.setState({ contentloaderRoom: false });
            });



        this.setState({
            contentloader: true,
        });

        API.get(`api/home/<USER>
            .then((response) => {
                if (response) {
                    if (response.status === 1) {
                        const userName = response && response.data.filter(user => user.email === localStorage.getItem("email"))[0]
                        this.setState({
                            userList: response.data,
                            userNameCheck: userName,
                            contentloader: false,
                        });
                        const otherUser = {
                            staff_id: -1,
                            first_name: "other",
                        }
                        this.setState({
                            userList: [...this.state.userList, otherUser]
                        })
                    } else {
                        this.setState({ contentloader: false });
                    }
                }
            })
            .catch((error) => {
                this.setState({ contentloader: false });
            });
    }

    fetchUnitListByShipmentId = (jobId) => {
        this.setState({
            contentloaderUnit: true,
            contentloader: true
        });
        API.get(`api/home/<USER>/${jobId}`)
            .then((response) => {
                if (response) {
                    if (response.status === 1) {
                        this.setState({
                            unitList: response.data,
                            contentloader: false,
                            contentloaderUnit: false
                        });
                    } else {
                        this.setState({ contentloader: false, contentloaderUnit: false });
                    }
                }
            })
            .catch((error) => {
                this.setState({ contentloader: false, contentloaderUnit: false });
            });
    }

    handleSubmit = (e) => {
        e.preventDefault();
        let formData = new FormData();
        this.props.form.validateFieldsAndScroll((err, values) => {
            let getStorageId = this.state.oldStorageUnitId;
            if (this.state.integrationKeyStatus && this.state.scanIntoStorageCheck && (this.state.storageShipmentId !== null && this.state.storageShipmentId !== "" && this.state.storageShipmentId !== undefined)) {
                const getStorageUnit = this.state.unitList && this.state.unitList.filter((unit) => {
                    return unit.unit_id == values.unit_id.key
                })
                getStorageId = getStorageUnit[0].storage_unit_id
            }

            const data = [];

            formData.append("inventory_id", this.props.match.params.id);
            formData.append("itemEditByCms", true);
            formData.append("company_id", this.state.companyId);
            formData.append("job_id", this.state.inventoryData && this.state.inventoryData.job_id);
            formData.append("room_id", values.shipment_room_id && values.shipment_room_id.key ? values.shipment_room_id.key : "");
            formData.append("unit_id", values.unit_id && values.unit_id.key ? values.unit_id.key : this.state.oldUnitId);
            formData.append("storage_unit_id", getStorageId);
            formData.append("qr_id", this.state.inventoryData && this.state.inventoryData.qr_id);
            formData.append("volume", this.state.inventoryData && this.state.inventoryData.volume ? this.state.inventoryData.volume : 0);
            formData.append("weight", this.state.inventoryData && this.state.inventoryData.weight ? this.state.inventoryData.weight : 0);
            formData.append("pads_used", this.state.inventoryData && this.state.inventoryData.pads_used ? this.state.inventoryData.pads_used : 0);
            formData.append("prepared_by", this.state.inventoryData && this.state.inventoryData.prepared_by ? this.state.inventoryData.prepared_by : null);
            formData.append("notes", this.state.inventoryData && this.state.inventoryData.notes ? this.state.inventoryData.notes : null);
            formData.append("isManualLabel", this.state.inventoryData && this.state.inventoryData.isManualLabel == 1 ? true : false);
            formData.append("color", this.state.inventoryData && this.state.inventoryData.color ? this.state.inventoryData.color : null);
            formData.append("label_no", this.state.inventoryData && this.state.inventoryData.label_no ? this.state.inventoryData.label_no : 0);
            formData.append("lot_no", this.state.inventoryData && this.state.inventoryData.lot_no ? this.state.inventoryData.lot_no : null);
            formData.append("isScannedFlag", this.state.inventoryData && this.state.inventoryData.isScannedFlag ? this.state.inventoryData.isScannedFlag : null);
            formData.append("item_name", values.item_name);
            formData.append("desc", values.item_name);
            formData.append("is_carton", values.is_carton);
            formData.append("packed_by", values.packed_by &&
                (values.is_carton == "true" || values.is_carton == true) &&
                values.packed_by === "packed_by_owner"
                ? "Packed by Owner"
                : "Packed by User");

            formData.append("packed_user_id", values.packed_user_id &&
                (values.is_carton == "true" || values.is_carton == true)
                ? values.packed_user_id.key
                : null);

            formData.append("is_disassembled", values.is_disassembled);
            formData.append("disassembled_by", values.disassembled_by &&
                (values.is_disassembled == "true" || values.is_disassembled == true) &&
                values.disassembled_by === "disassembled_by_owner"
                ? "Disassembled by Customer"
                : "Disassembled by User");

            formData.append("disassembled_user_id", values.disassembled_user_id &&
                (values.is_disassembled == "true" || values.is_disassembled == true)
                ? values.disassembled_user_id.key
                : null);

            formData.append("is_electronics", values.is_electronics);
            formData.append("serial_number", values.serial_number && (values.is_electronics == "true" || values.is_electronics == true)
                ? values.serial_number
                : "");

            formData.append("is_high_value", values.is_high_value);
            formData.append("seal_number", values.seal_number && (values.is_high_value == "true" || values.is_high_value == true)
                ? values.seal_number
                : "");
            formData.append("declared_value", values.declared_value &&
                (values.is_high_value == "true" || values.is_high_value == true) &&
                values.declared_value !== ""
                ? values.declared_value
                : 0,);

            formData.append("is_pro_gear", values.is_pro_gear);
            formData.append("pro_gear_weight", values.pro_gear_weight &&
                (values.is_pro_gear == "true" || values.is_pro_gear == true) &&
                values.pro_gear_weight !== ""
                ? values.pro_gear_weight
                : 0);

            formData.append("progear_name", values.progear_name && (values.is_pro_gear == "true" || values.is_pro_gear == true)
                ? values.progear_name.key
                : "");

            formData.append("is_firearm", values.is_firearm);
            formData.append("firmarm_serial_number", values.firmarm_serial_number && (values.is_firearm == "true" || values.is_firearm == true)
                ? values.firmarm_serial_number
                : "");

            if (!err) {
                data["data"] = formData;
                this.setState({ contentloader: true });
                API.post("api/home/<USER>", data)
                    .then((response) => {
                        if (response) {
                            this.setState({ contentloader: false });
                            message.success(response.message);
                            this.props.history.goBack();
                        } else {
                            this.setState({ loading: false, contentloader: false });
                            message.error(response.message);
                        }
                    })
                    .catch((error) => {
                        this.setState({ loading: false, contentloader: false });
                    });
            }
        });
    };


    isDisassmbledHandler = (event) => {
        if (event) {
            this.setState({
                isDisassmbledCheck: true
            })
        }
        else {
            this.setState({
                isDisassmbledCheck: false,
                isDisassmbledByUserCheck: false
            })
        }
    }

    isElectronicHandler = (event) => {
        if (event) {
            this.setState({
                isElectronicCheck: true
            })
        }
        else {
            this.setState({
                isElectronicCheck: false
            })
        }
    }

    isHighValueHandler = (event) => {
        if (event) {
            this.setState({
                isHighValueCheck: true
            })
        }
        else {
            this.setState({
                isHighValueCheck: false
            })
        }
    }


    isProGearHandler = (event) => {
        if (event) {
            this.setState({
                isProGearCheck: true
            })
        }
        else {
            this.setState({
                isProGearCheck: false
            })
        }
    }

    isFirearmsHandler = (event) => {
        if (event) {
            this.setState({
                isFirearmsCheck: true
            })
        }
        else {
            this.setState({
                isFirearmsCheck: false
            })
        }
    }

    isCartonHandler = (event) => {
        if (event) {
            this.setState({
                isCartonCheck: true
            })
        }
        else {
            this.setState({
                isCartonCheck: false,
                isCartonByUserCheck: false
            })
        }
    }


    isDisassmbledByUserHandler = (event) => {
        if (event) {
            this.setState({
                isDisassmbledByUserCheck: true
            })
        }
        else {
            this.setState({
                isDisassmbledByUserCheck: false
            })
        }
    }

    isDisassmbledByUserFalseHandler = (event) => {
        if (event) {
            this.setState({
                isDisassmbledByUserCheck: false
            })
        }
        else {
            this.setState({
                isDisassmbledByUserCheck: false
            })
        }
    }

    isCartonByUserHandler = (event) => {
        if (event) {
            this.setState({
                isCartonByUserCheck: true
            })
        }
        else {
            this.setState({
                isCartonByUserCheck: false
            })
        }
    }


    isCartonByUserFalseHandler = (event) => {
        if (event) {
            this.setState({
                isCartonByUserCheck: false
            })
        }
        else {
            this.setState({
                isCartonByUserCheck: false
            })
        }
    }


    minOne = (rule, value, callback) => {
        if (value && value <= 0) {
            callback("Weight must be greater then 0!");
        }
        else if (value && value > 10000000) {
            callback("Weight must be less than or equal to 10000000!");
        }
        else {
            callback();
        }
    };


    minOne2 = (rule, value, callback) => {
        if (value && value <= 0) {
            callback("Declared value must be greater then 0!");
        }
        else if (value && value > 10000000) {
            callback("Declared value must be less than or equal to 10000000!");
        }
        else {
            callback();
        }
    };

    render() {

        const { getFieldDecorator } = this.props.form;
        const { inventoryData, roomList, unitList } = this.state;

        const initialRoom =
            inventoryData && inventoryData.room_id === "" ? {} : { key: inventoryData && inventoryData.room_id };
        const initialUnit =
            inventoryData && inventoryData.unit_id === "" ? {} : { key: inventoryData && inventoryData.unit_id };

        const initialSource = inventoryData && inventoryData.progear_name === "" ? { key: "Member" } : { key: inventoryData && inventoryData.progear_name };
        const initialdDisassemble = (inventoryData.is_disassembled === 1)
            && inventoryData.disassembled_by === "By Company" ? "disassembled_by_user" : "disassembled_by_owner"

        const userDisassembleListData = this.state.userList.filter((user, i) => {
            return user.staff_id == inventoryData.disassembled_user_id
        })

        const initialdDisassembleUser = (inventoryData.is_disassembled === 1)
            && inventoryData.disassembled_by
            === "By Company" ? { key: userDisassembleListData.length > 0 ? userDisassembleListData[0].staff_id : null } : this.state && this.state.userNameCheck ? { key: this.state && this.state.userNameCheck.staff_id } : { key: -1 }

        const initialdCarton = (inventoryData.is_carton === 1)
            && inventoryData.packed_by
            === "Carrier Packed" ? "packed_by_user" : "packed_by_owner"


        const userCartonListData = this.state.userList.filter((user, i) => {
            return user.staff_id == inventoryData.packed_user_id
        })

        const initialdCartonUser = (inventoryData.is_carton === 1)
            && inventoryData.packed_by
            === "Carrier Packed" ? { key: userCartonListData.length > 0 ? userCartonListData[0].staff_id : null } : this.state && this.state.userNameCheck ? { key: this.state && this.state.userNameCheck.staff_id } : { key: -1 }

        const formItemLayout = {
            labelCol: {
                xs: {
                    span: 24,
                },
                sm: {
                    span: 5,
                },
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: {
                    span: 12,
                    offset: 1,
                },
                md: {
                    span: 12,
                    offset: 1,
                },
                lg: {
                    span: 12,
                    offset: 1,
                },
                xl: {
                    span: 10,
                    offset: 1,
                },
            },
        };

        const tailFormItemLayout = {
            wrapperCol: {
                xs: {
                    span: 24,
                    offset: 6,
                },
                sm: {
                    span: 16,
                    offset: 6,
                },
                xl: {
                    offset: 6,
                },
            },
        };

        return (
            <LayoutContentWrapper>
                <div className='add_header'>
                    <h2 style={{ marginBottom: "0" }}>
                        <i class='fas fa-edit' />
                        &emsp;Edit Item
                    </h2>
                    <div>
                        <button className='backButton' onClick={() => this.props.history.goBack()}>
                            <i className='fa fa-chevron-left' aria-hidden='true' /> Back
                        </button>
                    </div>
                </div>
                <LayoutContent
                    style={{
                        height: "93%",
                        overflowY: "auto",

                    }}>
                    <div>
                        <Spin spinning={this.state.contentloader} indicator={antIcon}>
                            <Spin spinning={this.state.contentloaderRoom} indicator={antIcon}>
                                <Spin spinning={this.state.contentloaderUnit} indicator={antIcon}>

                                    <Form {...formItemLayout} onSubmit={this.handleSubmit}>
                                        <Form.Item {...tailFormItemLayout}>
                                            <Form.Item label='Item Name'>
                                                {getFieldDecorator("item_name", {
                                                    initialValue: inventoryData && inventoryData.item_name,
                                                    placeholder: "Item Name",
                                                    rules: [
                                                        {
                                                            required: true,
                                                            message: "Please input item name!",
                                                            whitespace: true,
                                                        },
                                                    ],
                                                })(<Input placeholder='Item Name' />)}
                                            </Form.Item>
                                            <Form.Item label='Select Room'>
                                                {getFieldDecorator("shipment_room_id", {
                                                    initialValue: initialRoom,
                                                    rules: [
                                                        {
                                                            required: true,
                                                            message: "Please select room name",
                                                        },
                                                    ],
                                                })(
                                                    <Select
                                                        size={"default"}
                                                        showSearch
                                                        labelInValue
                                                        placeholder='Select Room Name'
                                                        style={{ width: "100%" }}>
                                                        {roomList
                                                            ? roomList.map((e) => (
                                                                <Option key={e.name} value={e.room_id}>
                                                                    {e.name}
                                                                </Option>
                                                            ))
                                                            : null}
                                                    </Select>
                                                )}
                                            </Form.Item>

                                            {
                                                this.state.integrationKeyStatus && this.state.scanIntoStorageCheck && (this.state.storageShipmentId !== null && this.state.storageShipmentId !== "" && this.state.storageShipmentId !== undefined) ?
                                                    <Form.Item label='Select Unit'>
                                                        {getFieldDecorator("unit_id", {
                                                            initialValue: initialUnit,
                                                            rules: [
                                                                {
                                                                    required: true,
                                                                    message: "Please select unit name",
                                                                },
                                                            ],
                                                        })(
                                                            <Select
                                                                size={"default"}
                                                                showSearch
                                                                labelInValue
                                                                placeholder='Select Unit Name'
                                                                style={{ width: "100%" }}>
                                                                {unitList
                                                                    ? unitList.map((e) => (
                                                                        <Option key={e.key} value={e.unit_id}>
                                                                            {e.name}
                                                                        </Option>
                                                                    ))
                                                                    : null}
                                                            </Select>
                                                        )}
                                                    </Form.Item>
                                                    : ""}


                                            <Form.Item label='Carton?'>
                                                {getFieldDecorator(`is_carton`, {
                                                    valuePropName: "checked",
                                                    initialValue: inventoryData && inventoryData.is_carton == 1 ? true : false,
                                                })(<Switch onChange={e => { this.isCartonHandler(e) }} />)}
                                            </Form.Item>

                                            {
                                                this.state.isCartonCheck ?
                                                    <Form.Item label='Packed By'>
                                                        {getFieldDecorator(`packed_by`, {
                                                            initialValue: initialdCarton
                                                        })(
                                                            <Radio.Group >
                                                                <Radio onChange={e => { this.isCartonByUserHandler(e) }} value="packed_by_user">Carrier Packed</Radio>
                                                                <Radio onChange={e => { this.isCartonByUserFalseHandler(e) }} value="packed_by_owner">Packed By Owner</Radio>
                                                            </Radio.Group>
                                                        )}
                                                    </Form.Item>
                                                    : ""
                                            }


                                            {
                                                this.state.isCartonByUserCheck ?
                                                    <Form.Item label='Select User'>
                                                        {getFieldDecorator("packed_user_id", {
                                                            initialValue: initialdCartonUser,
                                                        })(
                                                            <Select
                                                                size={"default"}
                                                                labelInValue
                                                                placeholder='Select User'
                                                                style={{ width: "100%" }}>
                                                                {this.state.userList.map((e) => (
                                                                    <Option key={e.staff_id} value={e.staff_id}>
                                                                        {e.first_name}
                                                                    </Option>
                                                                ))}
                                                            </Select>
                                                        )}
                                                    </Form.Item>
                                                    : ""
                                            }


                                            <Form.Item label='Disassmbled?'>
                                                {getFieldDecorator(`is_disassembled`, {
                                                    valuePropName: "checked",
                                                    initialValue: inventoryData && inventoryData.is_disassembled == 1 ? true : false,
                                                })(<Switch onChange={e => { this.isDisassmbledHandler(e) }} />)}
                                            </Form.Item>

                                            {
                                                this.state.isDisassmbledCheck ?
                                                    <Form.Item label='Disassembled By'>
                                                        {getFieldDecorator(`disassembled_by`, {
                                                            initialValue: initialdDisassemble
                                                        })(
                                                            <Radio.Group >
                                                                <Radio onChange={e => { this.isDisassmbledByUserHandler(e) }} value="disassembled_by_user">By Company</Radio>
                                                                <Radio onChange={e => { this.isDisassmbledByUserFalseHandler(e) }} value="disassembled_by_owner">By Customer</Radio>
                                                            </Radio.Group>
                                                        )}
                                                    </Form.Item>
                                                    : ""
                                            }

                                            {
                                                this.state.isDisassmbledByUserCheck ?


                                                    <Form.Item label='Select User'>
                                                        {getFieldDecorator("disassembled_user_id", {
                                                            initialValue: initialdDisassembleUser
                                                        })(
                                                            <Select
                                                                size={"default"}
                                                                labelInValue
                                                                placeholder='Select User'
                                                                style={{ width: "100%" }}>
                                                                {this.state.userList.map((e) => (
                                                                    <Option key={e.staff_id} value={e.staff_id}>
                                                                        {e.first_name}
                                                                    </Option>
                                                                ))}
                                                            </Select>
                                                        )}
                                                    </Form.Item>

                                                    : ""
                                            }

                                            <Form.Item label='Electronics?'>
                                                {getFieldDecorator(`is_electronics`, {
                                                    valuePropName: "checked",
                                                    initialValue: inventoryData && inventoryData.is_electronics == 1 ? true : false,
                                                })(<Switch onChange={e => { this.isElectronicHandler(e) }} />)}
                                            </Form.Item>

                                            {this.state.isElectronicCheck ?
                                                <Form.Item label='Serial Number'>
                                                    {getFieldDecorator("serial_number", {
                                                        initialValue: inventoryData && inventoryData.serial_number,
                                                        placeholder: "Serial Number",
                                                        rules: [
                                                            {
                                                                required: true,
                                                                message: "Please input serial number!",
                                                            },
                                                        ],
                                                    })(<Input placeholder='Serial Number' />)}
                                                </Form.Item>
                                                : ""
                                            }


                                            <Form.Item label='High Value?'>
                                                {getFieldDecorator(`is_high_value`, {
                                                    valuePropName: "checked",
                                                    initialValue: inventoryData && inventoryData.is_high_value == 1 ? true : false,
                                                })(<Switch onChange={e => { this.isHighValueHandler(e) }} />)}
                                            </Form.Item>

                                            {
                                                this.state.isHighValueCheck ?

                                                    <Form.Item label='Declared Value'>
                                                        {getFieldDecorator("declared_value", {
                                                            initialValue: inventoryData && inventoryData.declared_value > 0 ? inventoryData.declared_value : "",
                                                            rules: [
                                                                {
                                                                    required: true,
                                                                    message: "Please input declared value!",
                                                                },
                                                                {
                                                                    validator: this.minOne2
                                                                },
                                                            ],
                                                        })(<Input
                                                            addonAfter="$"
                                                            type="number"
                                                            placeholder='Declared Value' />)}
                                                    </Form.Item>

                                                    : ""
                                            }

                                            {
                                                this.state.isHighValueCheck ?
                                                    <Form.Item label='Seal Number'>
                                                        {getFieldDecorator("seal_number", {
                                                            initialValue: inventoryData && inventoryData.seal_number,
                                                            placeholder: "Seal Number",
                                                            rules: [
                                                                {
                                                                    required: true,
                                                                    message: "Please input seal number!",
                                                                },
                                                            ],
                                                        })(<Input placeholder='Seal Number' />)}
                                                    </Form.Item>
                                                    : ""
                                            }

                                            <Form.Item label='Pro Gear?'>
                                                {getFieldDecorator(`is_pro_gear`, {
                                                    valuePropName: "checked",
                                                    initialValue: inventoryData && inventoryData.is_pro_gear == 1 ? true : false,
                                                })(<Switch onChange={e => { this.isProGearHandler(e) }} />)}
                                            </Form.Item>

                                            {
                                                this.state.isProGearCheck ?


                                                    <Form.Item label='Select'>
                                                        {getFieldDecorator("progear_name", {
                                                            initialValue: initialSource,
                                                            rules: [
                                                                {
                                                                    required: true,
                                                                    message: "Please input name!",
                                                                },
                                                            ],
                                                        })(
                                                            <Select
                                                                size={"default"}
                                                                labelInValue
                                                                placeholder="Select name"
                                                                style={{ width: "100%" }}>
                                                                {this.state.sourceList.map((e) => (
                                                                    <Option key={e} value={e}>
                                                                        {e}
                                                                    </Option>
                                                                ))}
                                                            </Select>
                                                        )}
                                                    </Form.Item>
                                                    : ""
                                            }

                                            {
                                                this.state.isProGearCheck ?

                                                    <Form.Item label='Weight'>
                                                        {getFieldDecorator("pro_gear_weight", {
                                                            initialValue: inventoryData && inventoryData.pro_gear_weight > 0 ? inventoryData.pro_gear_weight : "",
                                                            rules: [
                                                                {
                                                                    required: true,
                                                                    message: "Please input weight!",
                                                                },
                                                                {
                                                                    validator: this.minOne
                                                                },
                                                            ],

                                                        })(<Input
                                                            addonAfter="lbs"
                                                            type="number"
                                                            style={{ width: "100%" }}
                                                            placeholder='Weight'
                                                        />)}
                                                    </Form.Item>
                                                    : ""
                                            }

                                            <Form.Item label='Firearms?'>
                                                {getFieldDecorator(`is_firearm`, {
                                                    valuePropName: "checked",
                                                    initialValue: inventoryData && inventoryData.is_firearm == 1 ? true : false,
                                                })(<Switch onChange={e => { this.isFirearmsHandler(e) }} />)}
                                            </Form.Item>

                                            {
                                                this.state.isFirearmsCheck ?
                                                    <Form.Item label='Firearm Serial Number'>
                                                        {getFieldDecorator("firmarm_serial_number", {
                                                            initialValue: inventoryData && inventoryData.firmarm_serial_number,
                                                            placeholder: "Firearm Serial Number",
                                                            rules: [
                                                                {
                                                                    required: true,
                                                                    message: "Please input firearm serial number!",
                                                                },
                                                            ],
                                                        })(<Input placeholder='Firearm Serial Number' />)}
                                                    </Form.Item>
                                                    : ""
                                            }


                                            <Button
                                                className='submitButton'
                                                loading={this.state.loading}
                                                type='primary'
                                                htmlType='submit'>
                                                Submit
                                            </Button>
                                        </Form.Item>

                                    </Form>
                                </Spin>
                            </Spin>
                        </Spin>


                    </div>
                </LayoutContent>
            </LayoutContentWrapper>
        );
    }
}

export default Form.create()(connect(null, { changeCurrent })(editJobInventory));
