import styled from "styled-components";

export const ImagesContainer = styled.div`
	.main-images {
		padding: 20px !important;	
		.small-img {
			height: 130px;
			width: 100%;
		}
		.inner-item {
			border: 1px solid #ccc;
			padding: 5px 10px 5px 10px;
			border-radius: 10px;
			position: relative;
		}
		.fit {
			margin-bottom: 8px;
			text-align: center;
			font-size: 13px;
			font-weight: 600;
		}
		.box-p {
			margin-top: 12px;
			margin-bottom: 0px;
			text-align: center;
			font-size: 13px;
			font-weight: 600;
		}
		.one-more {
			margin-bottom: 0px;
			text-align: center;
			color: #8ab932;
			font-size: 13px;
		}
		.image-container {
			position: relative;
		}
		.extra-image {
			background-color: #1f6ed4;
			padding: 5px 8px;
			border-radius: 50%;
			color: #ffffff;
			font-weight: bold;
			position: absolute;
			bottom: 0;
			right: 0;
		}
		.exception {
			background-color: #8ab932;
			padding: 5px 12px;
			border-radius: 50%;
			color: #ffffff;
			font-weight: bold;
			position: absolute;
			top: 0;
			right: 0;
		}
		.override {
			background-color: #de4307;
			padding: 5px 11px;
			border-radius: 50%;
			color: #ffffff;
			font-weight: bold;
			position: absolute;
			top: 0;
			left: 0;
		}
	}
`;

export const SortContainer = styled.div`
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	.sort-dropdown {
		margin: 20px;
		margin-left: 15px;
		padding: 5px;
		button {
			background: white;
			border: 1px solid #ccc;
			padding: 5px 10px;
			border-radius: 5px;
			font-size: 14px;
		}
		span {
			color: #0c7c6a;
			font-weight: 500;
			font-size: 14px;
		}
		i {
			color: #ccc;
			padding-left: 5px;
		}
	}
	.moving-sort-button-container {
		padding-left: 5px;
		button {
			margin-left: 15px;
		}
	}
	.active-btn {
		background: #13806e !important;
		color: white !important;
		border-color: #13806e !important;
		box-shadow: none !important;
		border-radius: 5px;
	}
	.normal-btn {
		background: white;
		color: black !important;
		border-color: #ccc !important;
		box-shadow: none !important;
		border-radius: 5px;
	}
`;

export const Div = styled.div`
	.origin {
		p {
			margin-bottom: 0;
		}
	}
`;

export const AdditionPhoto = styled.div`
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	column-gap: 30px;
	row-gap: 20px;
	letter-spacing: 0.6px;
	text-align: left;

	p:nth-of-type(2) {
		margin: 0;
		margin-top: 10px;
	}
`;

export const Signature = styled.div`
	.signature_history_wrapper {
		/* display: flex;
						justify-content: space-between; */
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		align-items: center;
		text-align: center;
	}
	.no_signature {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 25px;
		color: #424242;
		font-size: 24px;
		font-weight: 300;
	}
	.signature_history {
		/* display: flex;
		justify-content: space-between;
		align-items: center; */
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		align-items: center;
		justify-items: center;
		margin-top: 25px;

		& p:nth-of-type(1) {
			margin: 0;
		}
	}
`;

export const SortMenu = styled.div`
	.sort_title {
		color: green;
	}
`;
