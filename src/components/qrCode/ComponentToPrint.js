import * as React from "react";
import { Button, Checkbox, Col, Form, Icon, Input, message, Row, Spin, notification } from "antd";
import NumberFormat from "react-number-format";
import Sample_Qr from "../../static/images/sample_qr.png";
import "./qrstyle.css";




export class ComponentToPrint extends React.PureComponent {


  componentDidMount() {
    console.log("this.props", this.props)
  }

  render() {
    const { text } = this.props;
    const { shipmentData } = this.props.text;

    return (
      <div>
        {text && text.dymoLabelPrinterId == 1 ?
          <div style={{ padding: "0px 40px" }}>
            {text && text.batchAction && text.batchAction.length > 0 ?
              text.batchAction.map((data, i) => (
                <Col className="aliginItem" style={{ pageBreakInside: "avoid", paddingTop: "3px" }} sm={{ span: 12 }} >
                  <div>
                    <div>
                      {text.displayTitle && (
                        <h1 className="QR-title" style={{ fontSize: "2px", textTransform: "uppercase", marginLeft: "2px" }} >
                          <b> {text.qrTitle} ({shipmentData && shipmentData.job_number}) </b>
                        </h1>
                      )}
                    </div>
                    <div className="QR-image" style={{ display: "flex", marginTop: "0px" }}>
                      <div style={{ alignItems: "center", textAlign: "center" }}>
                        <div>
                          {text.labelNumber && (
                            <h2 style={{ fontSize: "2px" }} >
                              {" "}
                              <b> {data.label_number} </b>
                            </h2>
                          )}
                        </div>

                        <img
                          src={data.qr_image}
                          style={{ marginTop: "-3px" }}
                          width="20px"
                          height="20px"
                          alt="Mover Inventory"
                        />


                        <div>
                          {text.qrCode && (
                            <h2 style={{ fontSize: "2px" }} >
                              {" "}
                              <b> {data.random_number} </b>
                            </h2>
                          )}
                        </div>

                      </div>
                      <div style={{ marginLeft: "2px", padding: "0px", alignItems: "left", textAlign: "left", marginTop: "5px" }}>
                        {text.company && (
                          <div style={{ fontSize: "2px" }} className="QR-company-name">
                            {shipmentData &&
                              shipmentData.job_company &&
                              shipmentData.job_company.company_name}
                          </div>
                        )}
                        {text.contact && (
                          <div style={{ fontSize: "2px" }} className="QR-company-contact">
                            <NumberFormat
                              value={
                                shipmentData &&
                                shipmentData.job_company &&
                                shipmentData.job_company.phone
                              }
                              displayType={"text"}
                              format="(###) ###-####"
                            />
                          </div>
                        )}
                        {text.externalReference && (
                          <div style={{ fontSize: "2px" }} className="QR-company-name">
                            <strong>CID: </strong>
                            {shipmentData &&
                              shipmentData.external_reference
                            }
                          </div>
                        )}
                        {text.fromAdd && (
                          <div style={{ fontSize: "3px", fontWeight: "bold", marginTop: "1px", textTransform: "uppercase" }} className="QR-from-add">
                            <p>
                              <strong>From: </strong>
                              {shipmentData &&
                                shipmentData.pickup_address &&
                                shipmentData.pickup_address !== "undefined" &&
                                shipmentData.pickup_address !== "null"
                                ? shipmentData.pickup_address
                                : ""}
                              {shipmentData &&
                                shipmentData.pickup_address2 &&
                                shipmentData.pickup_address2 !== "undefined" &&
                                shipmentData.pickup_address2 !== "null"
                                ? " " + shipmentData.pickup_address2
                                : ""}
                            </p>
                            <p style={{ marginTop: "-1px" }} >
                              {shipmentData &&
                                shipmentData.pickup_city &&
                                shipmentData.pickup_city !== "undefined" &&
                                shipmentData.pickup_city !== "null"
                                ? shipmentData.pickup_city
                                : ""}
                              {shipmentData &&
                                shipmentData.pickup_state &&
                                shipmentData.pickup_state !== "undefined" &&
                                shipmentData.pickup_state !== "null"
                                ? " " + shipmentData.pickup_state
                                : ""}
                              {shipmentData &&
                                shipmentData.pickup_zipcode &&
                                shipmentData.pickup_zipcode !== "undefined" &&
                                shipmentData.pickup_zipcode !== "null"
                                ? ", " + shipmentData.pickup_zipcode
                                : ""}
                              {shipmentData &&
                                shipmentData.pickup_country &&
                                shipmentData.pickup_country !== "undefined" &&
                                shipmentData.pickup_country !== "null"
                                ? " " + shipmentData.pickup_country
                                : ""}
                            </p>
                          </div>
                        )}
                        {text.toAdd && (
                          <div style={{ fontSize: "3px", fontWeight: "bold", textTransform: "uppercase" }} className="QR-to-add">
                            <p>
                              <strong>To: </strong>
                              {shipmentData &&
                                shipmentData.delivery_address &&
                                shipmentData.delivery_address !== "undefined" &&
                                shipmentData.delivery_address !== "null"
                                ? shipmentData.delivery_address
                                : ""}
                              {shipmentData &&
                                shipmentData.delivery_address2 &&
                                shipmentData.delivery_address2 !==
                                "undefined" &&
                                shipmentData.delivery_address2 !== "null"
                                ? " " + shipmentData.delivery_address2
                                : ""}
                            </p>
                            <p style={{ marginTop: "-1px" }} >
                              {shipmentData &&
                                shipmentData.delivery_city &&
                                shipmentData.delivery_city !== "undefined" &&
                                shipmentData.delivery_city !== "null"
                                ? shipmentData.delivery_city
                                : ""}
                              {shipmentData &&
                                shipmentData.delivery_state &&
                                shipmentData.delivery_state !== "undefined" &&
                                shipmentData.delivery_state !== "null"
                                ? " " + shipmentData.delivery_state
                                : ""}
                              {shipmentData &&
                                shipmentData.delivery_zipcode &&
                                shipmentData.delivery_zipcode !== "undefined" &&
                                shipmentData.delivery_zipcode !== "null"
                                ? ", " + shipmentData.delivery_zipcode
                                : ""}
                              {shipmentData &&
                                shipmentData.delivery_country &&
                                shipmentData.delivery_country !== "undefined" &&
                                shipmentData.delivery_country !== "null"
                                ? " " + shipmentData.delivery_country
                                : ""}
                            </p>

                          </div>
                        )}
                        {text.qrOtherText && (
                          <div style={{ fontSize: "2px" }} className="QR-job-no">
                            Other: {text.qrOtherText}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </Col>
              ))
              : ""
            }
          </div>
          :
          text && text.dymoLabelPrinterId == 2 ?
            <div>
              {text && text.batchAction && text.batchAction.length > 0 ?
                text.batchAction.map((data, i) => (
                  <div className="aliginItem" style={{ pageBreakInside: "avoid", paddingTop: "10px" }} >
                    <div>
                      <div>
                        {text.displayTitle && (
                          <h1 className="QR-title" style={{ fontSize: "12px", textTransform: "uppercase" }} >
                            <b> {text.qrTitle} ({shipmentData && shipmentData.job_number}) </b>
                          </h1>
                        )}
                      </div>
                      <div className="QR-image" style={{ display: "flex", marginTop: "0px" }}>
                        <div>
                          <div>
                            {text.labelNumber && (
                              <h2 style={{ fontSize: "10px", marginLeft: "6px" }} >
                                {" "}
                                <b> {data.label_number} </b>
                              </h2>
                            )}
                          </div>

                          <img
                            src={data.qr_image}
                            style={{ marginTop: "-3px" }}
                            width="80px"
                            height="80px"
                            alt="Mover Inventory"
                          />

                          <div>
                            {text.qrCode && (
                              <h2 style={{ fontSize: "10px", marginLeft: "2px" }} >
                                {" "}
                                <b> {data.random_number} </b>
                              </h2>
                            )}
                          </div>

                        </div>
                        <div style={{ marginLeft: "9px", padding: "0px", alignItems: "left", textAlign: "left", marginTop: "16px" }}>
                          {text.company && (
                            <div style={{ fontSize: "6px" }} className="QR-company-name">
                              {shipmentData &&
                                shipmentData.job_company &&
                                shipmentData.job_company.company_name}
                            </div>
                          )}
                          {text.contact && (
                            <div style={{ fontSize: "6px" }} className="QR-company-contact">
                              <NumberFormat
                                value={
                                  shipmentData &&
                                  shipmentData.job_company &&
                                  shipmentData.job_company.phone
                                }
                                displayType={"text"}
                                format="(###) ###-####"
                              />
                            </div>
                          )}
                          {text.fromAdd && (
                            <div style={{ fontSize: "7px", fontWeight: "bold", marginTop: "4px", textTransform: "uppercase" }} className="QR-from-add">
                              <p>
                                <strong>From: </strong>
                                {shipmentData &&
                                  shipmentData.pickup_address &&
                                  shipmentData.pickup_address !== "undefined" &&
                                  shipmentData.pickup_address !== "null"
                                  ? shipmentData.pickup_address
                                  : ""}
                                {shipmentData &&
                                  shipmentData.pickup_address2 &&
                                  shipmentData.pickup_address2 !== "undefined" &&
                                  shipmentData.pickup_address2 !== "null"
                                  ? " " + shipmentData.pickup_address2
                                  : ""}
                              </p>
                              <p>
                                {shipmentData &&
                                  shipmentData.pickup_city &&
                                  shipmentData.pickup_city !== "undefined" &&
                                  shipmentData.pickup_city !== "null"
                                  ? shipmentData.pickup_city
                                  : ""}
                                {shipmentData &&
                                  shipmentData.pickup_state &&
                                  shipmentData.pickup_state !== "undefined" &&
                                  shipmentData.pickup_state !== "null"
                                  ? " " + shipmentData.pickup_state
                                  : ""}
                                {shipmentData &&
                                  shipmentData.pickup_zipcode &&
                                  shipmentData.pickup_zipcode !== "undefined" &&
                                  shipmentData.pickup_zipcode !== "null"
                                  ? ", " + shipmentData.pickup_zipcode
                                  : ""}
                              </p>
                              <p>
                                {shipmentData &&
                                  shipmentData.pickup_country &&
                                  shipmentData.pickup_country !== "undefined" &&
                                  shipmentData.pickup_country !== "null"
                                  ? shipmentData.pickup_country
                                  : ""}
                              </p>
                            </div>
                          )}
                          {text.toAdd && (
                            <div style={{ fontSize: "7px", fontWeight: "bold", textTransform: "uppercase" }} className="QR-to-add">
                              <p>
                                <strong>To: </strong>
                                {shipmentData &&
                                  shipmentData.delivery_address &&
                                  shipmentData.delivery_address !== "undefined" &&
                                  shipmentData.delivery_address !== "null"
                                  ? shipmentData.delivery_address
                                  : ""}
                                {shipmentData &&
                                  shipmentData.delivery_address2 &&
                                  shipmentData.delivery_address2 !==
                                  "undefined" &&
                                  shipmentData.delivery_address2 !== "null"
                                  ? " " + shipmentData.delivery_address2
                                  : ""}
                              </p>
                              <p>
                                {shipmentData &&
                                  shipmentData.delivery_city &&
                                  shipmentData.delivery_city !== "undefined" &&
                                  shipmentData.delivery_city !== "null"
                                  ? shipmentData.delivery_city
                                  : ""}
                                {shipmentData &&
                                  shipmentData.delivery_state &&
                                  shipmentData.delivery_state !== "undefined" &&
                                  shipmentData.delivery_state !== "null"
                                  ? " " + shipmentData.delivery_state
                                  : ""}
                                {shipmentData &&
                                  shipmentData.delivery_zipcode &&
                                  shipmentData.delivery_zipcode !== "undefined" &&
                                  shipmentData.delivery_zipcode !== "null"
                                  ? ", " + shipmentData.delivery_zipcode
                                  : ""}
                              </p>
                              <p>
                                {shipmentData &&
                                  shipmentData.delivery_country &&
                                  shipmentData.delivery_country !== "undefined" &&
                                  shipmentData.delivery_country !== "null"
                                  ? shipmentData.delivery_country
                                  : ""}
                              </p>
                            </div>
                          )}
                          {text.qrOtherText && (
                            <div style={{ fontSize: "6px" }} className="QR-job-no">
                              Other: {text.qrOtherText}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
                : ""
              }
            </div>
            :
            <div>
              {text && text.batchAction && text.batchAction.length > 0 ?
                text.batchAction.map((data, i) => (
                  <div className="aliginItem" style={{ pageBreakInside: "avoid", paddingTop: "2px", marginLeft: "20px" }} >
                    <div>
                      <div>
                        {text.displayTitle && (
                          <h1 className="QR-title" style={{ fontSize: "8px", textTransform: "uppercase", marginTop: "2px", marginLeft: "5px" }} >
                            <b> {text.qrTitle} ({shipmentData && shipmentData.job_number}) </b>
                          </h1>
                        )}
                      </div>
                      <div className="QR-image" style={{ display: "flex", marginTop: "2px" }}>
                        <div>
                          <div>
                            {text.labelNumber && (
                              <h2 style={{ fontSize: "6px", marginLeft: "2px" }} >
                                <b> {data.label_number} </b>
                              </h2>
                            )}
                          </div>

                          <img
                            src={data.qr_image}
                            style={{ marginTop: "-1px" }}
                            width="45px"  // Adjust the width
                            height="45px" // Adjust the height
                            alt="Mover Inventory"
                          />

                          <div>
                            {text.qrCode && (
                              <h2 style={{ fontSize: "6px", marginLeft: "2px" }} >
                                <b> {data.random_number} </b>
                              </h2>
                            )}
                          </div>
                        </div>

                        <div style={{ marginLeft: "5px", padding: "0px", textAlign: "left", marginTop: "2px" }}>
                          {text.company && (
                            <div style={{ fontSize: "6px", fontWeight: "bold" }} className="QR-company-name">
                              {shipmentData && shipmentData.job_company && shipmentData.job_company.company_name}
                            </div>
                          )}
                          {text.contact && (
                            <div style={{ fontSize: "6px" }} className="QR-company-contact">
                              <NumberFormat
                                value={shipmentData && shipmentData.job_company && shipmentData.job_company.phone}
                                displayType={"text"}
                                format="(###) ###-####"
                              />
                            </div>
                          )}
                          {text.fromAdd && (
                            <div style={{ fontSize: "6px", fontWeight: "bold", marginTop: "2px", textTransform: "uppercase" }} className="QR-from-add">
                              <p>
                                <strong>From: </strong>
                                {shipmentData && shipmentData.pickup_address}
                              </p>
                              <p style={{ marginTop: "-1px" }}>
                                {shipmentData && shipmentData.pickup_city} {shipmentData && shipmentData.pickup_state} {shipmentData && shipmentData.pickup_zipcode}

                              </p>
                            </div>
                          )}
                          {text.toAdd && (
                            <div style={{ fontSize: "6px", fontWeight: "bold", textTransform: "uppercase" }} className="QR-to-add">
                              <p>
                                <strong>To: </strong>
                                {shipmentData && shipmentData.delivery_address}
                              </p>
                              <p style={{ marginTop: "-1px" }}>
                                {shipmentData && shipmentData.delivery_city} {shipmentData && shipmentData.delivery_state} {shipmentData && shipmentData.delivery_zipcode}

                              </p>
                            </div>
                          )}
                          {text.qrOtherText && (
                            <div style={{ fontSize: "6px", fontWeight: "bold", textTransform: "uppercase" }} className="QR-job-no">
                              Other: {text.qrOtherText}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))

                : ""
              }
            </div>
        }
      </div>
    );
  }
}

export const FunctionalComponentToPrint = React.forwardRef((props, ref) => {
  // eslint-disable-line max-len
  return <ComponentToPrint ref={ref} text={props.text} />;
});
