import { Button, Col, Icon, Input, message, Modal, Select, Checkbox, Row, Spin, Table, Tooltip, notification } from "antd";
import React from "react";
import { saveAs } from "file-saver";

// import ModalImage from "react-modal-image";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import Api from "../../api/api-handler";
import "../../static/css/common.css";
import "../../static/css/userManagement.css";
import user from "../../static/images/user1.png";
import appActions from "../../redux/app/actions";
import scrollTo from "../scrollTo";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import QRCode from "qrcode.react";
import ReactToPrint from "react-to-print";
import { ComponentToPrint } from "./ComponentToPrintSingle";


const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
let timeoutVar;
let qrCodeId;

const { currentQrJob } = appActions;

const { Option } = Select;


class qrCodeManagement extends React.Component {
	componentRef = null;

	state = {
		apiParam: {},
		qrCodeList: [],
		pagination: {},
		visible: false,
		viewPageData: "",
		pageloading: false,
		search: null,
		deleteModal: false,
		loading: false,
		confirmLoading: false,
		items: [],
		batchAction: [],
		batchActionImages: [],
		printerList: [],
		printerList2: [],
		QRModal: false,
		QRValue: "",
		currentQrJobId: null,
		previewButton: false,
		printButton: false
	};
	componentDidMount() {

		let printers = window.dymo.label.framework.getPrinters()
		this.setState({
			companyCheckId: (localStorage.getItem("companyID") !== "" || localStorage.getItem("companyID") !== null || localStorage.getItem("companyID") !== undefined) ?
				localStorage.getItem("companyID")
				: null,
			printerList: printers
		})

		let params = {
			search: "",
			page_no: 1,
			page_size: 25,
		};
		this.setState({ apiParam: params, currentQrJobId: this.props.app.currentQrJob }, () =>
			this.fetchQrCodeList()
		);
	}
	fetchQrCodeList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		const { search, page_no, page_size } = this.state.apiParam;
		this.setState({ pageloading: true });
		API.get(
			`api/admin/qr_code/${this.props.match.params.id}/list?page_no=${page_no}&search=${search}&page_size=${page_size}`,
			data
		)
			.then((response) => {
				const pagination = { ...this.state.pagination };
				if (response) {

					if (response.status === 1) {
						pagination.total = response.data.count;

						this.setState({
							qrCodeList: response.data.rows,
							pagination,
							pageloading: false,
						});
					} else {
						this.setState({
							qrCodeList: response.data.rows,
							pagination,
							pageloading: false,
						});
						// message.error(res.message)
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	};
	handleChange = (pagination, filters, sorter) => {
		const pager = { ...this.state.pagination };
		pager.current = pagination.current;
		let params = {
			...this.state.apiParam,
			page_no: pagination.current,
			page_size: pagination.pageSize,
		};

		this.setState({
			pagination: pager,
			apiParam: params,
		});
		this.setState(
			{
				pagination: pager,
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchQrCodeList();
			}
		);
		scrollTo();
	};
	generatePdf() {
		const data = [];
		API.post("api/admin/qr_code/4/print/", data)
			.then((response) => {
				const pagination = { ...this.state.pagination };
				if (response) {
					if (response.status === 1) {
						pagination.total = response.data.count;
						this.setState({
							qrCodeList: response.data.result,
							pagination,
							pageloading: false,
						});
					} else {
						this.setState({
							qrCodeList: response.data,
							pagination,
							pageloading: false,
						});
						// message.error(res.message)
					}
				}
			})
			.catch((error) => {
				this.setState({ pageloading: false });
			});
	}
	addQrCode() {
		this.props.history.push({
			pathname: `${this.props.match.url}/add`,
			state: {
				name: this.props.location.state.name,
				shipment: this.props.location.state.shipment,
				jobNumber: this.props.location.state.jobNumber,
			},
		});
	}
	printQrCode(id) {
		this.props.history.push(`${this.props.match.url}/print`);
	}


	printSingalQr = (qr_code_id) => {
		const id = qr_code_id;
		this.setState({ loading: true, confirmLoading: true });
		let params = {
			place: "QrCode Info",
			title_flag: true,
			company_name_flag: true,
			company_contact_flag: true,
			from_address_flag: true,
			to_address_flag: true,
			job_number_flag: true,
			qr_code_label_flag: true,
			sequenced_label_flag: true,
			external_reference_flag: true,
			jobId: this.props.match.params.id,
			responseType: "blob",
		};

		const data = [];
		data["data"] = params;

		API.post(`api/admin/qr_code/${id}/singalQrPrint/`, data)
			.then((response) => {
				const pdfBlob = new Blob([response], {
					type: "application/pdf",
				});
				saveAs(pdfBlob, "qrcode.pdf");
				if (response) {
					this.setState({ loading: false, confirmLoading: false });
					notification.success({
						message: "Qr Pdf",
						description: "Pdf generated successfully.",
						duration: 4.5,
					});
				} else {
					this.setState({ loading: false, confirmLoading: false });
				}

			})
			.catch((error) => {
				this.setState({
					confirmLoading: false,
					deleteModal: false,
				});
			});
	}

	delete(id) {
		this.setState({ deleteModal: true });
		qrCodeId = id;
	}
	edit(id) {
		this.props.history.push(`${this.props.match.url}/edit/${id}`);
	}

	handleCancel = () => {
		this.setState({
			deleteModal: false,
			confirmLoading: false,
		});
	};
	handleQrCancel = () => {
		this.setState({
			QRModal: !this.state.QRModal,
		});
	};

	handleOk = () => {
		const id = qrCodeId;
		this.setState({ confirmLoading: true });
		const deleteData = [];
		deleteData["data"] = { qr_code_id: id };
		API.delete("api/admin/qr_code/" + id, deleteData)
			.then((response) => {
				if (response) {
					this.fetchQrCodeList();
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.success(response.message);
				} else {
					this.setState({
						deleteModal: false,
						confirmLoading: false,
					});
					message.error(response.message);
				}
			})
			.catch((error) => {
				this.setState({
					confirmLoading: false,
					deleteModal: false,
				});
			});
	};



	handleStatusChange = (qr_code_id) => {
		const statusData = [];
		statusData["data"] = { qr_code_id: qr_code_id };
		API.post("api/admin/qr_code/change-qr-code-status", statusData).then((response) => {
			if (response) {
				this.fetchQrCodeList({
					page: this.state.pagination.current ? this.state.pagination.current : 1,
				});
			} else {
				message.error(response.message);
			}
		});
	};
	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchQrCodeList, 1000);
	};
	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e.target.value,
				},
				search: e.target.value,
			},
			this.callback
		);
	};

	printSingalDymoQr(qrData) {
		this.setState({ showModal: true });
		this.setState(prevState => ({
			batchAction: [...prevState.batchAction, qrData]
		}))
	}

	printDymoQrCode() {
		this.props.history.push(`${this.props.match.url}/dymo-print`);
	}

	setComponentRef = (ref) => {
		this.componentRef = ref;
	};

	handleOnBeforeGetContent = (qrData) => {
		return new Promise((resolve) => {
			setTimeout(() => {
				this.setState(
					{ batchAction: [qrData] },
					resolve
				);
			}, 2000);
		});
	};


	reactToPrintContent = () => {
		return this.componentRef;
	};

	reactToPrintTrigger = () => {
		return <Button
			type='primary'
			className='c-btn c-round c-success'
			icon='printer'
		></Button>
	};

	render() {
		const columns = [
			{
				title: "Label Number",
				dataIndex: "label_number",
				key: "label_number",
				// sorter: true,
			},
			{
				title: "Qr Code",
				dataIndex: "random_number",
				key: "random_number",
				align: "center",
				// sorter: true,
				render: (text) => (
					<p style={{ margin: 0, letterSpacing: "1.25px", fontWeight: "500" }}>{text}</p>
				),
			},

			{
				title: "Qr Image",
				dataIndex: "qr_image",
				key: "qr_image",
				align: "center",
				render: (record, text) => {
					return (
						<div
							style={{
								display: "flex",
								justifyContent: "center",
								alignItems: "center",
							}}>
							{text.qr_image && text.qr_image.trim() !== "" ? (
								<QRCode
									onClick={() => this.setState({ QRModal: true, QRValue: text.random_number.toString() })}
									value={text.random_number.toString()}
									size={40}
								/>
							) : (
								user
							)}

						</div>
					);
				},
			},
			{
				title: "Item",
				key: "item",
				align: "center",
				render: (record, text) => {
					return (
						<div>
							{text.item_id ?
								(
									<>
										{
											text.deletedAt !== null ?
												<text style={{ letterSpacing: "0.5px", color: "red" }}>
													ITEM DELETED
												</text>
												:
												<Link style={{ letterSpacing: "0.5px" }} to={`/job/view-inventory/${text.item_id}`}>
													VIEW
												</Link>
										}

									</>
								) : (
									" - "
								)
							}
						</div>
					);
				},
			},
			{
				title: "Action",
				key: "action",
				align: "center",
				// fixed: "right",
				width: "170px",
				render: (record, text) => {
					return (
						<div style={{ display: "flex", flexDirection: "row", justifyContent: "space-between", padding: "0px 20px" }}>
							{/* {this.state.currentQrJobId !== text.job_id.toString() ? ( */}
							<div className='icons'>
								{!text.item_id ? (
									<Tooltip title='Delete'>
										<Button
											type='primary'
											className={!this.state.currentQrJobId && "c-btn c-round c-danger"}
											icon='delete'
											// disabled={this.state.currentQrJobId === text.job_id.toString()}
											disabled={this.state.currentQrJobId}
											onClick={() => this.delete(text.qr_code_id)}></Button>
									</Tooltip>
								) : (
									" - "
								)}
							</div>
							<div className='icons'>
								<Tooltip title='Print'>
									<Button
										type='primary'
										className={"c-btn c-round c-primary"}
										icon='printer'
										onClick={() => this.printSingalQr(text.qr_code_id)}></Button>
								</Tooltip>
							</div>
							<div className='icons'>
								<Tooltip title='Print Dymo Label'>

									<ReactToPrint
										content={this.reactToPrintContent}
										onBeforeGetContent={() => this.handleOnBeforeGetContent(text)}
										documentTitle="MoverInventory"
										removeAfterPrint
										trigger={this.reactToPrintTrigger}
									/>
								</Tooltip>
							</div>
						</div>
					);
				},
			},
		];
		return (
			<LayoutContentWrapper>
				<div className='top_header' style={{ height: "100%" }}>
					<Row>
						<h3
							style={{
								display: "flex",
								textTransform: "capitalize",
								marginLeft: "5px",
								padding: "10px 0",
							}}>
							{this.props.location.state.name}&nbsp;&nbsp;
							{this.props.location.state.shipment}&nbsp; #{this.props.location.state.jobNumber}
						</h3>
						<Col sm={8}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='user' /> &emsp;Label Management
							</h2>
						</Col>
						<Col sm={16}>
							<Row>
								<Col sm={8} style={{ textAlign: "right" }}>
									<Search
										placeholder='Search Label/Qr'
										onChange={this.handleSearch}
										value={this.state.search}
										style={{ width: 200 }}
										disabled={this.state.currentQrJobId}
									/>
								</Col>
								<Col sm={12}>
									<Button
										className='addButton'
										style={{ marginTop: "0" }}
										type='primary'
										disabled={this.state.currentQrJobId}
										onClick={() => this.addQrCode()}>
										+ Add QR Codes
									</Button>

									<Button
										className='addButton'
										style={{ marginTop: "0" }}
										type='primary'
										disabled={this.state.qrCodeList.length > 0 ? false : true}
										onClick={() => this.printQrCode(this.props.match.params.id)}>
										Print A4 Labels
									</Button>

									<Button
										className="addButton"
										style={{ marginTop: "0" }}
										// disabled={this.state.batchAction.length > 0 ? false : true}

										type="primary"
										onClick={() => this.printDymoQrCode()}
									>
										Print Labels with Dymo/Zebra
									</Button>

								</Col>

								<Col sm={4} style={{ textAlign: "right" }}>
									<button className='backButton' onClick={() => this.props.history.goBack()}>
										<i className='fa fa-chevron-left' aria-hidden='true' /> Back
									</button>
								</Col>
							</Row>
						</Col>
					</Row>
				</div>
				<Spin spinning={this.state.pageloading} indicator={antIcon}>
					<LayoutContent>
						<div style={{ marginTop: "-1.5rem" }}>
							{this.state.qrCodeList.length > 0 ? (
								<Table
									bordered={true}
									columns={columns}
									pagination={{
										total: this.state.pagination.total,
										showSizeChanger: true,
										defaultPageSize: 25,
										pageSizeOptions: ["25", "50"],
									}}
									dataSource={this.state.qrCodeList}
									onChange={this.handleChange}
								/>
							) : (
								<h4
									style={{
										fontSize: "30px",
										textAlign: "center",
										color: "#999",
										marginTop: "1rem",
									}}>
									No QR available.
								</h4>
							)}
						</div>
					</LayoutContent>
				</Spin>
				{
					<Modal
						title='Are You Sure?'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Yes'
						cancelText='No'
						centered
						maskClosable={false}
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Are you sure you want to delete Qr Code?</p>
					</Modal>
				}
				{
					<Modal
						bodyStyle={{ textAlign: "center" }}
						// maskStyle={{background:"#000"}}
						title={null}
						visible={this.state.QRModal}
						cancelText='Close'
						centered
						maskClosable={false}
						onOk={this.handleQrCancel}
						onCancel={this.handleQrCancel}>
						<QRCode value={this.state.QRValue} size={150} />
					</Modal>
				}

				<Modal
					width={540}
					title='Dymo Label Printer'
					visible={this.state.showModal}
					onOk={this.handleSubmit}
					okText='Print'
					cancelText='Preview'
					centered
					maskClosable={false}
					onCancel={this.onCancelModal}

					footer={[
						<Button key="back" type="danger" onClick={this.onCancelModal}>
							Cancel
						</Button>,
						<Button
							disabled={this.state.previewButton ? true : false}
							key="preview" type="primary" onClick={this.printDymoPreview} >
							Preview
						</Button>,
						<Button
							disabled={this.state.printButton ? true : false}
							key="submit" type="primary" onClick={this.handleSubmit}>
							Print
						</Button>,
					]}
				>
					<div>
						<p><b>Select printer</b></p>

						<Select
							placeholder="Select printer"
							style={{ width: "100%" }}
							onChange={(e) => this.changeValue(e)}>
							{this.state.printerList.map((result, i) =>
							(
								<Option value={result.name} key={i}>
									{result.name}
								</Option>
							))}
						</Select>

						{this.state.batchActionImages.length > 0 ?
							this.state.batchActionImages.map((img, i) => (
								<div key={i} style={{ marginTop: "20px" }} >
									<h3>Label Preview {i + 1}</h3>
									<img
										src={"data:image/png;base64," + img}
										alt='..'
										className='small-img'
									/>
								</div>
							))
							: ""
						}

					</div>
				</Modal>
				<div style={{ display: "none" }}>
					<ComponentToPrint ref={this.setComponentRef} text={this.state} />
				</div>
			</LayoutContentWrapper>
		);
	}
}

export default connect(
	(state) => ({
		app: state.App,
		height: state.App.height,
	}),
	{ currentQrJob }
)(qrCodeManagement);
