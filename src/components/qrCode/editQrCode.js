import "../../static/css/add.css";

import { Button, Form, Icon, Input, Spin, message, Upload, Select, DatePicker } from "antd";
import Api from "../../api/api-handler";

import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import React from "react";
import appActions from "../../redux/app/actions";
import { connect } from "react-redux";
import moment from "moment";

const { changeCurrent } = appActions;

const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
const API = new Api({});
const { TextArea } = Input;
const { Option } = Select;

class EditCompany extends React.Component {
	state = {
		companyData: null,
		loading: false,
		contentloader: false,
		previewVisible: false,
		previewImage: "",
		fileList: [],
	};
	componentDidMount() {
		const id = this.props.match.params.id;
		this.props.changeCurrent("company");
		this.setState({ contentloader: true });

		const data = [];
		data["data"] = { company_id: id };
		API.post("api/admin/company/view-company", data)
			.then((response) => {
				if (response) {
					this.setState(
						{
							companyData: response.data && response.data,
							fileList: [{ uid: -1, status: "done", url: response.data.company_logo }],
							previewImage: response.data.company_logo,
							previewVisible: true,
							contentloader: false,
						},
						() => this.props.form.setFieldsValue({})
					);
				} else {
					message.error(response.message);
					this.setState({ contentloader: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}
	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {
		// you store them in state, so that you can make a http req with them later
		this.setState({ fileList });
	};
	handleSubmit = (e) => {
		e.preventDefault();
		let formData = new FormData();
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];

				formData.append("company_id", this.props.match.params.id);
				formData.append("company_name", values.company_name);
				formData.append("address", values.address);
				formData.append("phone", values.phone);
				formData.append("country_code", values.country_code);
				formData.append("company_identity", values.company_identity);
				formData.append("email", values.email);
				formData.append("photo", values.logo !== "" ? this.state.fileList[0].originFileObj : "");
				formData.append("start_date", values.start_date);
				formData.append("end_date", values.end_date);

				data["data"] = formData;
				this.setState({ loading: true });

				API.post("api/admin/company/edit-company", data)
					.then((response) => {
						if (response) {
							this.setState({ loading: false });
							message.success(response.message);
							this.props.history.goBack();
						} else {
							this.setState({ loading: false });
							message.error(response.message);
						}
					})
					.catch((error) => {
						//console.log("error:", error);
						this.setState({ loading: false });
					});
			}
		});
	};
	render() {
		const { getFieldDecorator } = this.props.form;
		const { companyData } = this.state;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		const countryCodeSelector = getFieldDecorator("country_code", {
			initialValue: companyData && companyData.country_code,
		})(
			<Select style={{ width: 70 }}>
				<Option value='86'>+86</Option>
				<Option value='87'>+87</Option>
				<Option value='91'>+91</Option>
			</Select>
		);
		return (
			<LayoutContentWrapper>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-edit' />
						&emsp;Edit Company
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true' /> Back
					</button>
				</div>
				<LayoutContent>
					<div>
						<Spin spinning={this.state.contentloader} indicator={antIcon}>
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Company name'>
									{getFieldDecorator("company_name", {
										initialValue: companyData && companyData.company_name,
										rules: [
											{
												required: true,
												message: "Please input company name!",
												whitespace: true,
											},
										],
									})(<Input />)}
								</Form.Item>

								<Form.Item label='Logo'>
									{getFieldDecorator("logo")(
										<Upload
											listType='picture-card'
											fileList={this.state.fileList}
											multiple={false}
											onPreview={this.handlePreview}
											onChange={this.handleUpload}
											beforeUpload={() => false} // return false so that antd doesn't upload the picture right away
										>
											<Button
												disabled={
													this.state.fileList && this.state.fileList[0] && this.state.fileList[0].url
														? true
														: false
												}>
												<Icon type='upload' />{" "}
												{this.state.fileList && this.state.fileList[0] && this.state.fileList[0].url
													? "Click to Upload"
													: "Click to Upload"}
											</Button>
										</Upload>
									)}
								</Form.Item>

								<Form.Item label='Address'>
									{getFieldDecorator("address", {
										initialValue: companyData && companyData.address,
										rules: [
											{
												required: true,
												message: "Please input company address!",
												whitespace: true,
											},
										],
									})(<TextArea rows={4} placeholder='Company Address' />)}
								</Form.Item>

								<Form.Item label='Phone Number'>
									{getFieldDecorator("phone", {
										initialValue: companyData && companyData.phone,
										rules: [
											{
												required: true,
												message: "Please input your phone number!",
											},
										],
									})(
										<Input
											addonBefore={countryCodeSelector}
											style={{ width: "100%" }}
											placeholder='Phone Number'
										/>
									)}
								</Form.Item>

								<Form.Item label='Company Identity'>
									{getFieldDecorator("company_identity", {
										initialValue: companyData && companyData.company_identity,
										placeholder: "Company Identity",
										rules: [
											{
												required: true,
												message: "Please input company identity!",
												whitespace: true,
											},
										],
									})(<Input placeholder='Company Identity' />)}
								</Form.Item>

								<Form.Item label='Email'>
									{getFieldDecorator("email", {
										initialValue: companyData && companyData.email,
										rules: [
											{
												type: "email",
												message: "The input is not valid Email!",
											},
											{
												required: true,
												message: "Please input your Email!",
											},
										],
									})(<Input />)}
								</Form.Item>

								<Form.Item label='Contract start date'>
									{getFieldDecorator("start_date", {
										initialValue: companyData && moment(companyData.start_date, "YYYY/MM/DD"),
										rules: [
											{
												type: "object",
												required: true,
												message: "Please select start date!",
											},
										],
									})(<DatePicker placeholder='Contract start date' />)}
								</Form.Item>

								<Form.Item label='Contract end date'>
									{getFieldDecorator("end_date", {
										initialValue: companyData && moment(companyData.end_date, "YYYY/MM/DD"),
										rules: [
											{
												type: "object",
												required: true,
												message: "Please select end date!",
											},
										],
									})(<DatePicker placeholder='Contract end date' />)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Submit
									</Button>
								</Form.Item>
							</Form>
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

export default Form.create()(connect(null, { changeCurrent })(EditCompany));
