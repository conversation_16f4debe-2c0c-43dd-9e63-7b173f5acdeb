import * as React from "react";
import { Button, Checkbox, Col, Form, Icon, Input, message, Row, Spin, notification } from "antd";
import NumberFormat from "react-number-format";
import Sample_Qr from "../../static/images/sample_qr.png";
import "./qrstyle.css";



export class ComponentToPrint extends React.PureComponent {


  componentDidMount() {
    console.log("this.props", this.props)
  }

  render() {
    const { text, shipmentData } = this.props;
    return (
      <div>
        {text && text.batchAction && text.batchAction.length > 0 ?
          text.batchAction.map((data, i) => (
            <div className="aliginItem" style={{ pageBreakInside: "avoid", paddingTop: "20px" }} >
              <div>
                <div>
                  <h1 className="QR-title" style={{ fontSize: "14px", textTransform: "uppercase" }} >
                    <b> {data.shipment_job && data.shipment_job.shipment_name} </b>
                  </h1>
                </div>

                <div className="QR-image" style={{ display: "flex", marginTop: "0px" }}>
                  <div style={{ alignItems: "center", textAlign: "center" }}>
                    <div>
                      <h2 style={{ fontSize: "12px" }} >
                        {" "}
                        <b> {data.label_number} </b>
                      </h2>
                    </div>

                    <img
                      src={data.qr_image}
                      style={{ marginTop: "-3px" }}
                      width="85px"
                      height="85px"
                      alt="Mover Inventory"
                    />
                    <div>
                      <h2 style={{ fontSize: "12px" }} >
                        {" "}
                        <b> {data.random_number} </b>
                      </h2>
                    </div>
                  </div>
                  <div style={{ marginLeft: "10px", padding: "0px", alignItems: "left", textAlign: "left", marginTop: "18px" }}>

                    <div style={{ fontSize: "8px" }} className="QR-job-no">
                      Job No :  {data.shipment_job && data.shipment_job.job_number}
                    </div>

                    <div style={{ fontSize: "8px" }} className="QR-company-name">
                      {data.shipment_job &&
                        data.shipment_job.job_company &&
                        data.shipment_job.job_company.company_name}
                    </div>

                    <div style={{ fontSize: "8px" }} className="QR-company-contact">
                      <NumberFormat
                        value={
                          data.shipment_job &&
                          data.shipment_job.job_company &&
                          data.shipment_job.job_company.phone
                        }
                        displayType={"text"}
                        format="(###) ###-####"
                      />
                    </div>

                    <div style={{ fontSize: "8px" }} className="QR-company-name">
                      <strong>CID: </strong>
                      {data.shipment_job &&
                        data.shipment_job.external_reference
                      }
                    </div>

                    <div style={{ fontSize: "9px", fontWeight: "bold", marginTop: "5px", textTransform: "uppercase" }} className="QR-from-add">


                      {/* <p>
                        <strong>From: </strong>
                        {data.shipment_job &&
                          data.shipment_job.pickup_address &&
                          data.shipment_job.pickup_address !== "undefined" &&
                          data.shipment_job.pickup_address !== "null"
                          ? data.shipment_job.pickup_address
                          : ""}
                        {data.shipment_job &&
                          data.shipment_job.pickup_address2 &&
                          data.shipment_job.pickup_address2 !== "undefined" &&
                          data.shipment_job.pickup_address2 !== "null"
                          ? " " + data.shipment_job.pickup_address2
                          : ""}
                      </p> */}
                      <p>
                        <strong>From: </strong>

                        {data.shipment_job &&
                          data.shipment_job.pickup_city &&
                          data.shipment_job.pickup_city !== "undefined" &&
                          data.shipment_job.pickup_city !== "null"
                          ? data.shipment_job.pickup_city
                          : ""}
                        {data.shipment_job &&
                          data.shipment_job.pickup_state &&
                          data.shipment_job.pickup_state !== "undefined" &&
                          data.shipment_job.pickup_state !== "null"
                          ? " " + data.shipment_job.pickup_state
                          : ""}
                        {data.shipment_job &&
                          data.shipment_job.pickup_zipcode &&
                          data.shipment_job.pickup_zipcode !== "undefined" &&
                          data.shipment_job.pickup_zipcode !== "null"
                          ? ", " + data.shipment_job.pickup_zipcode
                          : ""}
                      </p>
                      <p>
                        {data.shipment_job &&
                          data.shipment_job.pickup_country &&
                          data.shipment_job.pickup_country !== "undefined" &&
                          data.shipment_job.pickup_country !== "null"
                          ? data.shipment_job.pickup_country
                          : ""}
                      </p>
                    </div>
                    <div style={{ fontSize: "9px", fontWeight: "bold", textTransform: "uppercase" }} className="QR-to-add">

                      {/* <p>
                        <strong>To: </strong>
                        {data.shipment_job &&
                          data.shipment_job.delivery_address &&
                          data.shipment_job.delivery_address !== "undefined" && 
                          data.shipment_job.delivery_address !== "null"
                          ? data.shipment_job.delivery_address
                          : ""}
                        {data.shipment_job &&
                          data.shipment_job.delivery_address2 &&
                          data.shipment_job.delivery_address2 !==
                          "undefined" &&
                          data.shipment_job.delivery_address2 !== "null"
                          ? " " + data.shipment_job.delivery_address2
                          : ""}
                      </p> */}
                      <p>
                        <strong>To: </strong>
                        {data.shipment_job &&
                          data.shipment_job.delivery_city &&
                          data.shipment_job.delivery_city !== "undefined" &&
                          data.shipment_job.delivery_city !== "null"
                          ? data.shipment_job.delivery_city
                          : ""}
                        {data.shipment_job &&
                          data.shipment_job.delivery_state &&
                          data.shipment_job.delivery_state !== "undefined" &&
                          data.shipment_job.delivery_state !== "null"
                          ? " " + data.shipment_job.delivery_state
                          : ""}
                        {data.shipment_job &&
                          data.shipment_job.delivery_zipcode &&
                          data.shipment_job.delivery_zipcode !== "undefined" &&
                          data.shipment_job.delivery_zipcode !== "null"
                          ? ", " + data.shipment_job.delivery_zipcode
                          : ""}
                      </p>
                      <p>
                        {data.shipment_job &&
                          data.shipment_job.delivery_country &&
                          data.shipment_job.delivery_country !== "undefined" &&
                          data.shipment_job.delivery_country !== "null"
                          ? data.shipment_job.delivery_country
                          : ""}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
          : ""
        }
      </div>
    );
  }
}

export const FunctionalComponentToPrint = React.forwardRef((props, ref) => {
  // eslint-disable-line max-len
  return <ComponentToPrint ref={ref} text={props.text} />;
});
