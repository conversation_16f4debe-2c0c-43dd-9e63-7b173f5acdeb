import {
  Button,
  Checkbox,
  Col,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Spin,
  Select,
  notification,
  Switch
} from "antd";
import { saveAs } from "file-saver";
import React, { forwardRef } from 'react';
import NumberFormat from "react-number-format";
import { connect } from "react-redux";
import Api from "../../api/api-handler";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
//import { SketchPicker } from "react-color";
import Sample_Qr from "../../static/images/sample_qr.png";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import "./qrstyle.css";
import ReactToPrint from "react-to-print";
import { ComponentToPrint } from "./ComponentToPrint";



// alert('hi');
const { changeCurrent } = appActions;
const { Option } = Select;
const API = new Api({});
const antIcon = <Icon type="loading" style={{ fontSize: 24 }} spin />;

class AddQrCode extends React.Component {
  componentRef = null;

  state = {
    isDone: false,
    loading: false,
    saving: false,
    contentloader: false,
    previewVisible: false,
    previewImage: "",
    fileList: [],
    displayColorPicker: false,
    color: "#000",
    displayTitle: true,
    job: true,
    company: true,
    contact: true,
    fromAdd: true,
    toAdd: true,
    labelNumber: true,
    qrCode: true,
    qrTitle: "",
    qrOtherText: "",
    shipmentData: null,
    jobItemLength: 0,
    preSetting: {},
    initialTitle: "",
    printerList: [],
    printerName: "",
    showModal: false,
    printButton: false,
    previewButton: false,
    labelImages: "",
    batchAction: [],
    batchActionImages: [],
    sourceList: [{ name: `Ten Labels/Page (8.5 x 11)`, id: 1 }, { name: `Label Printer (4" x 1.5")`, id: 2 }, { name: `Label Printer (1 1/8" x 3 1/2")`, id: 3 }],
    dymoLabelPrinterId: 1,
    externalReference: true,
    isQrRangeCheck: false,
    jobQrStartNumber: 0,
    jobQrEndNumber: 0,
    startNumberToDownload: 0,
    endNumberToDownload: 0,
    isAllowToPrint: false,
    isMakeOtherTextEnable: false,
    otherTextLength: 0
  };

  componentDidMount() {
    let printers = window.dymo.label.framework.getPrinters();

    this.setState({
      companyCheckId:
        localStorage.getItem("companyID") !== "" ||
          localStorage.getItem("companyID") !== null ||
          localStorage.getItem("companyID") !== undefined
          ? localStorage.getItem("companyID")
          : null,
      printerList: printers,
    });

    const id = this.props.match.params.id;
    this.props.changeCurrent("job");
    this.setState({ contentloader: true });
    API.get(`api/admin/qr_code/${id}/get_qr_first_and_last_label`)
      .then((res) => {
        this.setState({
          jobQrStartNumber: res.data.findFirstLabel,
          jobQrEndNumber: res.data.findLastLabel,
        });
        this.setState({ contentloader: false });
      })
      .catch((err) => {
        this.setState({ contentloader: false });
      });
    API.get(`api/admin/qr_code/${id}/get_setting`)
      .then(async (res) => {
        await this.setState({
          preSetting: res.data,
          displayTitle: res.data.title_flag === "yes" ? true : false,
          company: res.data.company_name_flag === "yes" ? true : false,
          contact: res.data.company_contact_flag === "yes" ? true : false,
          fromAdd: res.data.from_address_flag === "yes" ? true : false,
          toAdd: res.data.to_address_flag === "yes" ? true : false,
          qrCode: res.data.qr_code_label_flag === "yes" ? true : false,
          labelNumber: res.data.sequenced_label_flag === "yes" ? true : false,
          job: res.data.job_number_flag === "yes" ? true : false,
          externalReference: res.data.external_reference_flag === "yes" ? true : false,
          initialTitle:
            (res.data && res.data.place !== null) || res.data.place !== ""
              ? res.data.place
              : "",
        });
        this.setState({ contentloader: false });
      })
      .catch((err) => {
        this.setState({ contentloader: false });
      });
    // const data1 = [];
    API.get(`api/admin/shipment/${id}`)
      .then(async (response) => {
        if (response) {
          await this.setState({
            shipmentData: response.data && response.data,
            qrTitle:
              this.state.initialTitle !== ""
                ? this.state.initialTitle
                : response.data.shipment_name,
            jobItemLength: response.data.job_items.length,
          });
        } else {
          message.error(response.message);
          this.setState({ contentloader: false });
        }
        this.setState({ contentloader: false });
      })
      .catch((error) => {
        this.setState({ contentloader: false });
      });
    setTimeout(() => {
      this.setState({ loading: false });
    }, 120000);

    API.post(`api/admin/qr_code/${id}/dymo-list`)
      .then(async (response) => {
        if (response) {
          this.setState({
            batchAction: response.data.rows,
            contentloader: false,
            loading: false,
          });
        } else {
          message.error(response.message);
          this.setState({ contentloader: false });
        }
        this.setState({ contentloader: false });
      })
      .catch((error) => {
        this.setState({ contentloader: false });
      });
    setTimeout(() => {
      this.setState({ loading: false });
    }, 120000);
  }

  handlePreview = (file) => {
    this.setState({
      previewImage: file.thumbUrl,
      previewVisible: true,
    });
  };
  handleUpload = ({ fileList }) => {
    // you store them in state, so that you can make a http req with them later
    this.setState({ fileList });
  };
  printDymoQrCode = (e) => {
    e.preventDefault();

    this.props.form.validateFieldsAndScroll((err, values) => {
      let params = {
        place: values.place,
        title_flag: values.display_title,
        company_name_flag: values.display_company_name,
        company_contact_flag: values.display_company_no,
        from_address_flag: values.display_from_add,
        to_address_flag: values.display_to_add,
        job_number_flag: values.display_job_number,
        qr_code_label_flag: values.display_qr_code,
        sequenced_label_flag: values.display_label_no,
        external_reference_flag: values.display_external_reference,
        responseType: "blob",
      };
      if (!err) {
        const data = [];
        this.setState({ showModal: true });
      }
    });
  };

  onCancelModal = () => {
    this.setState({
      showModal: false,
      printButton: false,
      previewButton: false,
      labelImages: "",
      batchActionImages: [],
    });
  };

  changeValue = (value) => {
    this.setState({ printerName: value });
  };

  toDataURL = async (src, callback, outputFormat) => {
    return new Promise((resolve, reject) => {
      let image = new Image();
      image.crossOrigin = "Anonymous";
      image.src = src;
      image.onload = async function () {
        let canvas = document.createElement("canvas");
        let ctx = canvas.getContext("2d");
        canvas.height = this.naturalHeight;
        canvas.width = this.naturalWidth;
        ctx.drawImage(this, 0, 0);
        let result;
        result = canvas.toDataURL(outputFormat);
        resolve(result);
      };
    });
  };


  DymohandleChange = (e) => {
    this.setState({ dymoLabelPrinterId: e.id });
  };

  handleSaveSettings = (e) => {
    e.preventDefault();
    this.props.form.validateFieldsAndScroll((err, values) => {
      let params = {
        place: values.place,
        title_flag: values.display_title ? "yes" : "no",
        company_name_flag: values.display_company_name ? "yes" : "no",
        company_contact_flag: values.display_company_no ? "yes" : "no",
        from_address_flag: values.display_from_add ? "yes" : "no",
        to_address_flag: values.display_to_add ? "yes" : "no",
        job_number_flag: values.display_job_number ? "yes" : "no",
        qr_code_label_flag: values.display_qr_code ? "yes" : "no",
        sequenced_label_flag: values.display_label_no ? "yes" : "no",
        external_reference_flag: values.display_external_reference ? "yes" : "no",
      };
      if (!err) {
        const data = [];

        data["data"] = params;
        this.setState({ saving: true });
        // /qr_code/:jobId/save_setting
        API.post(
          "api/admin/qr_code/" + this.props.match.params.id + "/save_setting",
          data
        )
          .then((response) => {
            if (response) {
              this.setState({ saving: false });
              message.success(response.message);
            } else {
              this.setState({ saving: false });
              message.error(response.message);
            }
          })
          .catch((error) => {
            this.setState({ saving: false });
          });
      } else {
        //console.log(err);
      }
    });
  };
  normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  handleClick = () => {
    this.setState({ displayColorPicker: !this.state.displayColorPicker });
  };

  handleClose = () => {
    this.setState({ displayColorPicker: false });
  };

  handleChange = (color) => {
    this.setState({ color: color.hex });
  };

  handleQRTitleChange = (e) => {
    this.setState({ qrTitle: e.target.value });
  };

  handleOtherTextAdd = (e) => {
    this.setState({ qrOtherText: e.target.value, otherTextLength: e.target.value.length });
  };

  handleQRLableChange = (label) => {
    if (label === "title") {
      this.setState({ displayTitle: !this.state.displayTitle });
    } else if (label === "job") {
      this.setState({ job: !this.state.job });
    } else if (label === "company") {
      this.setState({ company: !this.state.company });
    } else if (label === "contact") {
      this.setState({ contact: !this.state.contact });
    } else if (label === "from") {
      this.setState({ fromAdd: !this.state.fromAdd });
    } else if (label === "to") {
      this.setState({ toAdd: !this.state.toAdd });
    } else if (label === "label") {
      this.setState({ labelNumber: !this.state.labelNumber });
    } else if (label === "qr") {
      this.setState({ qrCode: !this.state.qrCode });
    } else if (label === "external_reference") {
      this.setState({ externalReference: !this.state.externalReference });
    }
  };

  padLeadingZeros = (num, size) => {
    let s = num + "";
    while (s.length < size) s = "0" + s;
    return s;
  };


  setComponentRef = (ref) => {
    this.componentRef = ref;
  };

  pageStyle = "@page{ size: 3.25in 2in }"

  onBeforeGetContent = async () => {
    if (this.state.isQrRangeCheck) {
      this.setState({ contentloader: true });
      const fromLableNumber = this.state.startNumberToDownload
      const toLableNumber = this.state.endNumberToDownload

      if (parseFloat(fromLableNumber) < this.state.jobQrStartNumber || isNaN(fromLableNumber) || fromLableNumber === "" || fromLableNumber === null || fromLableNumber === undefined) {
        message.error(`From Label number must be greater than or equal to ${this.state.jobQrStartNumber}`)
        this.setState({ contentloader: false, isAllowToPrint: false, });
      }
      else if (parseFloat(toLableNumber) < this.state.jobQrStartNumber || isNaN(toLableNumber) || toLableNumber === "" || toLableNumber === null || toLableNumber === undefined) {
        message.error(`To Label number must be greater than or equal to ${this.state.jobQrStartNumber}`)
        this.setState({ contentloader: false, isAllowToPrint: false, });
      }
      else if (parseFloat(fromLableNumber) > parseFloat(toLableNumber)) {
        message.error("From Label number must be less than or equal to To Label Number")
        this.setState({ contentloader: false, isAllowToPrint: false, });
      }
      else if (toLableNumber > this.state.jobQrEndNumber) {
        message.error(`To Label number must be less than or equal to ${this.state.jobQrEndNumber}`)
        this.setState({ contentloader: false, isAllowToPrint: false, });
      }
      else {
        let params = {
          is_QrRange_select: this.state.isQrRangeCheck,
          from_label: fromLableNumber,
          to_label: toLableNumber,
        };
        const id = this.props.match.params.id;
        const data = [];
        data["data"] = params;
        this.setState({ contentloader: true });
        await API.post(`api/admin/qr_code/${id}/dymo-list`, data)
          .then(async (response) => {
            if (response) {
              this.setState({
                batchAction: response.data.rows,
                isAllowToPrint: true,
                contentloader: false
              });
              return response
            } else {
              this.setState({ contentloader: false, isAllowToPrint: false, });
            }
            this.setState({ contentloader: false, isAllowToPrint: false });
          })
          .catch((error) => {
            this.setState({ contentloader: false, isAllowToPrint: false });
          });
      }
    }
    else {
      this.setState({
        isAllowToPrint: true,
      });
    }
  }

  reactToPrintContent = () => {
    return this.componentRef;
  };


  reactToPrintTrigger = () => {
    return <Button
      className="submitButton"
      type="primary"
    >
      Print
    </Button>
  };

  isDateRangeSelectHandler = async (event) => {
    if (event) {
      this.setState({
        isQrRangeCheck: true,
      })
    }
    else {
      this.setState({
        isQrRangeCheck: false,
      })
      let params = {
        is_QrRange_select: false,
        from_label: 0,
        to_label: 0,
      };
      const id = this.props.match.params.id;
      const data = [];
      data["data"] = params;
      this.setState({ contentloader: true });
      await API.post(`api/admin/qr_code/${id}/dymo-list`, data)
        .then(async (response) => {
          if (response) {
            this.setState({
              batchAction: response.data.rows,
              isAllowToPrint: false,
              contentloader: false
            });
          } else {
            this.setState({ contentloader: false });
          }
          this.setState({ contentloader: false });
        })
        .catch((error) => {
          this.setState({ contentloader: false });
        });
    }
  }

  fromNumberChangeHandler = (value) => {
    this.setState({
      startNumberToDownload: value
    });
  }

  toNumberChangeHandler = (value) => {
    this.setState({
      endNumberToDownload: value
    });
  }

  checkBoxHandler = (event) => {
    const { checked } = event.target;
    if (checked) {
      this.setState({
        isMakeOtherTextEnable: checked,
      });
    }
    else {
      this.setState({
        isMakeOtherTextEnable: checked,
        qrOtherText: "",
        otherTextLength: 0
      });
    }
  }



  render() {
    const { getFieldDecorator } = this.props.form;
    const { shipmentData, jobItemLength, preSetting } = this.state;
    const initialLableNumber = this.padLeadingZeros(jobItemLength, 4);
    const formItemLayout = {
      labelCol: {
        xs: {
          span: 24,
        },
        sm: {
          span: 5,
        },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: {
          span: 24,
        },
        md: {
          span: 24,
        },
        lg: {
          span: 24,
        },
        xl: {
          span: 24,
        },
      },
    };
    const tailFormItemLayout = {
      wrapperCol: {
        xs: {
          span: 24,
          offset: 6,
        },
        sm: {
          span: 16,
          offset: 6,
        },
        xl: {
          offset: 6,
        },
      },
    };

    return (
      <LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
        <div className="add_header">
          <h2 style={{ marginBottom: "0", marginLeft: "10px" }}>
            <i className="fas fa-qrcode"></i> Print Dymo/Zebra QR Code
          </h2>
          {/* <p>{!this.state.isDone && "Last "  }</p> */}
          <button
            className="backButton"
            onClick={() => this.props.history.goBack()}
          >
            <i className="fa fa-chevron-left" aria-hidden="true"></i> Back
          </button>
        </div>
        <LayoutContent
          style={{
            margin: "0 20px",
            height: "93%",
            overflowY: "auto",
          }}
        >
          <div className="print-qr">
            <Spin
              spinning={this.state.loading}
              indicator={antIcon}
              tip={
                <div>
                  <p style={{ margin: "0" }}>
                    Please wait, pdf labels are getting created. This process
                    may take sometime, so you can click on the back button and
                    continue with other work. You will be notified once Labels
                    are generated.
                  </p>
                </div>
              }
            >
              <Spin spinning={this.state.contentloader} indicator={antIcon}>
                <Row>
                  <Col sm={12} style={{ position: "relative" }}>
                    <Form {...formItemLayout} onSubmit={this.printDymoQrCode}>
                      <Row>
                        <Col sm={12}>
                          <Form.Item>
                            {getFieldDecorator("place", {
                              initialValue:
                                preSetting && preSetting.place
                                  ? preSetting.place
                                  : shipmentData && shipmentData.shipment_name,
                              rules: [
                                {
                                  required: true,
                                  message: "Please input name!",
                                },
                              ],
                            })(
                              <Input
                                onChange={(e) => this.handleQRTitleChange(e)}
                                maxLength={90}
                                placeholder="Title"
                              />
                            )}
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={12}>
                          <Form.Item label="">
                            {getFieldDecorator("display_title", {
                              valuePropName: "checked",
                              initialValue: this.state.displayTitle,
                            })(
                              <Checkbox
                                onChange={() =>
                                  this.handleQRLableChange("title")
                                }
                              >
                                Display title
                              </Checkbox>
                            )}
                          </Form.Item>
                        </Col>
                        <Col sm={12}>
                          <Form.Item label="">
                            {getFieldDecorator("display_job_number", {
                              valuePropName: "checked",
                              initialValue: this.state.job,
                            })(
                              <Checkbox
                                onChange={() => this.handleQRLableChange("job")}
                              >
                                Display Job Number
                              </Checkbox>
                            )}
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={12}>
                          <Form.Item label="">
                            {getFieldDecorator("display_company_name", {
                              valuePropName: "checked",
                              initialValue: this.state.company,
                            })(
                              <Checkbox
                                onChange={() =>
                                  this.handleQRLableChange("company")
                                }
                              >
                                Display Company Name
                              </Checkbox>
                            )}
                          </Form.Item>
                        </Col>
                        <Col sm={12}>
                          <Form.Item label="">
                            {getFieldDecorator("display_company_no", {
                              valuePropName: "checked",
                              initialValue: this.state.contact,
                            })(
                              <Checkbox
                                onChange={() =>
                                  this.handleQRLableChange("contact")
                                }
                              >
                                Display Company Phone
                              </Checkbox>
                            )}
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={12}>
                          <Form.Item label="">
                            {getFieldDecorator("display_from_add", {
                              valuePropName: "checked",
                              initialValue: this.state.fromAdd,
                            })(
                              <Checkbox
                                onChange={() =>
                                  this.handleQRLableChange("from")
                                }
                              >
                                Display Origin Address
                              </Checkbox>
                            )}
                          </Form.Item>
                        </Col>
                        <Col sm={12}>
                          <Form.Item label="">
                            {getFieldDecorator("display_to_add", {
                              valuePropName: "checked",
                              initialValue: this.state.toAdd,
                            })(
                              <Checkbox
                                onChange={() => this.handleQRLableChange("to")}
                              >
                                Display Destination Address
                              </Checkbox>
                            )}
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={12}>
                          <Form.Item label="">
                            {getFieldDecorator("display_label_no", {
                              valuePropName: "checked",
                              initialValue: this.state.labelNumber,
                            })(
                              <Checkbox
                                onChange={() =>
                                  this.handleQRLableChange("label")
                                }
                              >
                                Display Label Number
                              </Checkbox>
                            )}
                          </Form.Item>
                        </Col>
                        <Col sm={12}>
                          <Form.Item label="">
                            {getFieldDecorator("display_qr_code", {
                              valuePropName: "checked",
                              initialValue: this.state.qrCode,
                            })(
                              <Checkbox
                                onChange={() => this.handleQRLableChange("qr")}
                              >
                                Display QR Code
                              </Checkbox>
                            )}
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={12}>
                          <Form.Item label=''>
                            {getFieldDecorator("display_external_reference", {
                              valuePropName: "checked",
                              initialValue: this.state.externalReference,
                            })(
                              <Checkbox onChange={() => this.handleQRLableChange("external_reference")}>
                                Display External Reference
                              </Checkbox>
                            )}
                          </Form.Item>
                        </Col>
                        <Col sm={12}>
                          <Form.Item>
                            {getFieldDecorator(`Other`, {
                              valuePropName: "checked",
                            })(
                              <Checkbox
                                onChange={(e) => { this.checkBoxHandler(e) }}
                              >
                                Other
                              </Checkbox>)}
                          </Form.Item>
                          {this.state.isMakeOtherTextEnable && (
                            <Form.Item>
                              {getFieldDecorator("otherText", {
                                rules: [
                                  {
                                    required: true,
                                    message: "Please input text!",
                                  },
                                ],
                              })(
                                <div style={{ display: "flex", alignItems: "center" }}>
                                  <Input
                                    onChange={(e) => this.handleOtherTextAdd(e)}
                                    maxLength={90}
                                    placeholder="Other"
                                    style={{ flex: 1 }}
                                  />
                                  <span style={{ marginLeft: "8px", whiteSpace: "nowrap" }}>
                                    {this.state.otherTextLength}/90
                                  </span>
                                </div>
                              )}
                            </Form.Item>

                          )}
                        </Col>
                      </Row>
                      <Row style={{ marginBottom: "10px", marginTop: "-30px" }} >
                        <Form.Item style={{ padding: "0px", margin: "0px" }} label='Paper/Sticker :'>
                          <Select
                            size={"default"}
                            labelInValue
                            placeholder="Ten Labels/Page (8.5 x 11)"
                            style={{ width: "70%" }}
                          >

                            {this.state.sourceList.map((e) => (
                              <Option
                                key={e.id}
                                value={e.id}
                                onClick={this.DymohandleChange.bind(null, e)}
                              >
                                {e.name}
                              </Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Row>
                      <Row>
                        <Form.Item label='Select Label No'>
                          {getFieldDecorator(`is_DateRange_select`, {
                          })(<Switch onChange={e => { this.isDateRangeSelectHandler(e) }} />)}
                        </Form.Item>
                      </Row>
                      <Row>


                        {
                          this.state.isQrRangeCheck ?
                            <>
                              <Col sm={12}>
                                <Form.Item label='FromNo'>
                                  {getFieldDecorator("from_label", {
                                    rules: [
                                      {
                                        validator: (rule, value, callback) => {
                                          const toLabelValue = parseFloat(this.props.form.getFieldValue("to_label"));
                                          const fromLabelValue = parseFloat(value);
                                          if (fromLabelValue <= 0) {
                                            callback("From Label number must be greater than 0");
                                          } else {
                                            callback();
                                          }
                                        },
                                      },
                                      {
                                        required: true,
                                        message: "Please select from label Number",
                                      },
                                    ],
                                  })(<Input
                                    min={this.state.jobQrStartNumber}
                                    max={this.state.jobQrEndNumber - 1}
                                    type="number"
                                    style={{ width: "90%" }}
                                    placeholder='From label number'
                                    onChange={(e) => {
                                      const value = e.target.value;
                                      this.fromNumberChangeHandler(value);
                                    }}
                                  />)}
                                </Form.Item>
                              </Col>
                              <Col sm={12}>
                                <Form.Item label='ToNo'>
                                  {getFieldDecorator("to_label", {
                                    rules: [
                                      {
                                        validator: (rule, value, callback) => {
                                          const fromLabelValue = parseFloat(this.props.form.getFieldValue("from_label"));
                                          const toLabelValue = parseFloat(value);
                                          if (toLabelValue <= 0) {
                                            callback("To Label number must be greater than 0");
                                          }
                                          else if (toLabelValue < fromLabelValue) {
                                            callback("To Label number must be greater than and equal to From Label Number");
                                          }
                                          else if (toLabelValue > this.state.jobQrEndNumber) {
                                            callback(`To Label number must be less than or equal to ${this.state.jobQrEndNumber}`);
                                          }
                                          else {
                                            callback();
                                          }
                                        },
                                      },
                                      {
                                        required: true,
                                        message: "Please select To Label Number",
                                      },
                                    ],
                                  })
                                    (<Input
                                      min={this.state.jobQrStartNumber}
                                      max={this.state.jobQrEndNumber}
                                      type="number"
                                      style={{ width: "90%" }}
                                      placeholder='To label number'
                                      onChange={(e) => {
                                        const value = e.target.value;
                                        this.toNumberChangeHandler(value);
                                      }}
                                    />
                                    )
                                  }
                                </Form.Item>
                              </Col>
                            </>
                            : ""
                        }
                      </Row>



                      <Form.Item
                        {...tailFormItemLayout}
                        style={{
                          marginLeft: "0",
                          marginRight: "0",
                          transform: "translatex(-25%)",
                        }}
                      >
                        {/* <Button
                          className="submitButton"
                          loading={this.state.loading}
                          type="primary"
                          htmlType="submit"
                        >
                          Print
                        </Button> */}
                        <ReactToPrint
                          // pageStyle={this.pageStyle}
                          onBeforeGetContent={this.onBeforeGetContent}
                          content={this.state.isAllowToPrint ? this.reactToPrintContent : null}
                          documentTitle="MoverInventory"
                          removeAfterPrint
                          trigger={this.reactToPrintTrigger}
                        />
                      </Form.Item>
                    </Form>

                    <Form
                      style={{
                        position: "absolute",
                        bottom: "0",
                        right: "0",
                        transform: "translatex(-100%)",
                      }}
                      onSubmit={this.handleSaveSettings}
                    >
                      <Form.Item {...tailFormItemLayout}>
                        <Button
                          className="submitButton"
                          loading={this.state.saving}
                          type="primary"
                          htmlType="submit"
                        >
                          Save Settings
                        </Button>
                      </Form.Item>
                    </Form>
                  </Col>
                  <Col sm={{ span: 10, offset: 1 }}>
                    {this.state.displayTitle && (
                      <div
                        className="QR-title"
                        style={{
                          marginLeft: "15px",
                          fontSize: "20px",
                          fontWeight: "bold",
                        }}
                      >
                        {this.state.qrTitle} ({shipmentData && shipmentData.job_number})
                      </div>
                    )}
                    <div className="isoLogoWrapper">
                      <div
                        className="QR-image"
                        style={{
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <div
                          style={{ display: "flex", flexDirection: "column" }}
                        >
                          <b
                            style={
                              !this.state.labelNumber
                                ? { visibility: "hidden" }
                                : { visibility: "visible" }
                            }
                          >
                            <h2 style={{ margin: "0px 0px 0px 30px" }}>
                              {" "}
                              {initialLableNumber
                                ? initialLableNumber
                                : "0000"}{" "}
                            </h2>
                          </b>
                          <img
                            src={Sample_Qr}
                            style={{ marginTop: "-5px" }}
                            width="135px"
                            alt="Mover Inventory"
                          />
                          <div style={{ margin: " -15px 0px 0px 18px" }}>
                            <h2>
                              {" "}
                              <b> {this.state.qrCode && "78QE8901"} </b>
                            </h2>
                          </div>
                        </div>
                        <div>
                          {this.state.company && (
                            <div className="QR-company-name">
                              {shipmentData &&
                                shipmentData.job_company &&
                                shipmentData.job_company.company_name}
                            </div>
                          )}
                          {this.state.contact && (
                            <div className="QR-company-contact">
                              <NumberFormat
                                value={
                                  shipmentData &&
                                  shipmentData.job_company &&
                                  shipmentData.job_company.phone
                                }
                                displayType={"text"}
                                format="(###) ###-####"
                              />
                            </div>
                          )}
                          {this.state.externalReference && (
                            <div className='QR-external_reference'>
                              <strong>CID: </strong>
                              {shipmentData && shipmentData.external_reference}
                            </div>
                          )}
                          {this.state.fromAdd && (
                            <div className="QR-from-add">
                              <p>
                                <strong>From: </strong>
                                {shipmentData &&
                                  shipmentData.pickup_address &&
                                  shipmentData.pickup_address !== "undefined" &&
                                  shipmentData.pickup_address !== "null"
                                  ? shipmentData.pickup_address
                                  : ""}
                                {shipmentData &&
                                  shipmentData.pickup_address2 &&
                                  shipmentData.pickup_address2 !== "undefined" &&
                                  shipmentData.pickup_address2 !== "null"
                                  ? " " + shipmentData.pickup_address2
                                  : ""}
                              </p>
                              <p>
                                {shipmentData &&
                                  shipmentData.pickup_city &&
                                  shipmentData.pickup_city !== "undefined" &&
                                  shipmentData.pickup_city !== "null"
                                  ? shipmentData.pickup_city
                                  : ""}
                                {shipmentData &&
                                  shipmentData.pickup_state &&
                                  shipmentData.pickup_state !== "undefined" &&
                                  shipmentData.pickup_state !== "null"
                                  ? " " + shipmentData.pickup_state
                                  : ""}
                                {shipmentData &&
                                  shipmentData.pickup_zipcode &&
                                  shipmentData.pickup_zipcode !== "undefined" &&
                                  shipmentData.pickup_zipcode !== "null"
                                  ? ", " + shipmentData.pickup_zipcode
                                  : ""}
                              </p>
                              <p>
                                {shipmentData &&
                                  shipmentData.pickup_country &&
                                  shipmentData.pickup_country !== "undefined" &&
                                  shipmentData.pickup_country !== "null"
                                  ? shipmentData.pickup_country
                                  : ""}
                              </p>
                            </div>
                          )}
                          {this.state.toAdd && (
                            <div className="QR-to-add">
                              <p>
                                <strong>To: </strong>
                                {shipmentData &&
                                  shipmentData.delivery_address &&
                                  shipmentData.delivery_address !== "undefined" &&
                                  shipmentData.delivery_address !== "null"
                                  ? shipmentData.delivery_address
                                  : ""}
                                {shipmentData &&
                                  shipmentData.delivery_address2 &&
                                  shipmentData.delivery_address2 !==
                                  "undefined" &&
                                  shipmentData.delivery_address2 !== "null"
                                  ? " " + shipmentData.delivery_address2
                                  : ""}
                              </p>
                              <p>
                                {shipmentData &&
                                  shipmentData.delivery_city &&
                                  shipmentData.delivery_city !== "undefined" &&
                                  shipmentData.delivery_city !== "null"
                                  ? shipmentData.delivery_city
                                  : ""}
                                {shipmentData &&
                                  shipmentData.delivery_state &&
                                  shipmentData.delivery_state !== "undefined" &&
                                  shipmentData.delivery_state !== "null"
                                  ? " " + shipmentData.delivery_state
                                  : ""}
                                {shipmentData &&
                                  shipmentData.delivery_zipcode &&
                                  shipmentData.delivery_zipcode !== "undefined" &&
                                  shipmentData.delivery_zipcode !== "null"
                                  ? ", " + shipmentData.delivery_zipcode
                                  : ""}
                              </p>
                              <p>
                                {shipmentData &&
                                  shipmentData.delivery_country &&
                                  shipmentData.delivery_country !== "undefined" &&
                                  shipmentData.delivery_country !== "null"
                                  ? shipmentData.delivery_country
                                  : ""}
                              </p>
                            </div>
                          )}
                          {this.state.qrOtherText && (
                            <div className="QR-job-no">
                              <strong>Other: </strong>
                              {this.state.qrOtherText.length > 45 ? (
                                <>
                                  {this.state.qrOtherText.substring(0, 45)}<br />
                                  {this.state.qrOtherText.substring(45)}
                                </>
                              ) : (
                                this.state.qrOtherText
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                      <div
                        className="QR-details"
                        style={{
                          marginLeft: "15px",
                          marginTop: "12px",
                          width: "60%",
                        }}
                      ></div>
                    </div>
                  </Col>
                </Row>
              </Spin>
            </Spin>
          </div>
        </LayoutContent>
        <Modal
          width={540}
          title="Dymo Label Printer"
          visible={this.state.showModal}
          onOk={this.handleSubmit}
          okText="Print"
          cancelText="Preview"
          centered
          maskClosable={false}
          onCancel={this.onCancelModal}
          footer={[
            <Button key="back" type="danger" onClick={this.onCancelModal}>
              Cancel
            </Button>,
            <Button
              disabled={this.state.previewButton ? true : false}
              key="preview"
              type="primary"
              onClick={this.printDymoPreview}
            >
              Preview
            </Button>,
            <Button
              disabled={this.state.printButton ? true : false}
              key="submit"
              type="primary"
              onClick={this.handleSubmit}
            >
              Print
            </Button>,
          ]}
        >
          <div>
            <p>
              <b>Select printer</b>
            </p>

            <Select
              placeholder="Select printer"
              style={{ width: "100%" }}
              onChange={(e) => this.changeValue(e)}
            >
              {this.state.printerList.map((result, i) => (
                <Option value={result.name} key={i}>
                  {result.name}
                </Option>
              ))}
            </Select>

            {this.state.batchActionImages.length > 0
              ? this.state.batchActionImages.map((img, i) => (
                <div key={i} style={{ marginTop: "20px" }}>
                  <h3>Label Preview {i + 1}</h3>
                  <img
                    src={"data:image/png;base64," + img}
                    alt=".."
                    className="small-img"
                  />
                </div>
              ))
              : ""}
          </div>
        </Modal>
        <div style={{ display: "none" }}>
          <ComponentToPrint ref={this.setComponentRef} text={this.state} />
        </div>
      </LayoutContentWrapper>
    );
  }
}

export default Form.create()(connect(null, { changeCurrent })(AddQrCode));
