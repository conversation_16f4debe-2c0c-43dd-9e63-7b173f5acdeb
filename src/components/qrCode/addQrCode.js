import React from "react";
import { connect } from "react-redux";
import { Button, Form, Icon, InputNumber, Spin } from "antd";
import LayoutContent from "../utility/layoutContent";
import LayoutContentWrapper from "../utility/layoutWrapper";
import { toast } from "react-toastify";
import appActions from "../../redux/app/actions";
import "../../static/css/add.css";
import Api from "../../api/api-handler";
import "./qrstyle.css";
// alert('hi');
const { changeCurrent, currentQrJob } = appActions;
const API = new Api({});
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
class AddQrCode extends React.Component {
	state = {
		loading: false,
		contentloader: true,
		previewVisible: false,
		previewImage: "",
		fileList: [],
		notificationTime: 0,
	};
	componentDidMount() {
		setTimeout(() => {
			this.setState({ loading: false });
		}, 120000);
	}
	// `Your QR code labels are generated successfully. Click here to see labels ${}`
	handlePreview = (file) => {
		this.setState({
			previewImage: file.thumbUrl,
			previewVisible: true,
		});
	};
	handleUpload = ({ fileList }) => {
		this.setState({ fileList });
	};
	redirectToQr = () => {
		this.props.history.push({
			pathname: `/qr-code/${this.props.match.params.id}`,
			state: {
				name: this.props.location.state.name,
				shipment: this.props.location.state.shipment,
				jobNumber: this.props.location.state.jobNumber,
			},
		});
		this.setState({ notificationTime: 0.5 });
	};
	handleSubmit = (e) => {
		e.preventDefault();
		this.props.currentQrJob(this.props.match.params.id);
		this.props.form.validateFieldsAndScroll((err, values) => {
			let params = {
				type: 1,
				jobId: this.props.match.params.id,
				quantity: values.quantity,
			};
			if (!err) {
				const data = [];
				data["data"] = params;
				this.setState({ loading: true });
				const resolveAfterPromise = new Promise((resolve, reject) => {
					API.post("api/admin/qr_code/generate", data)
						.then((response) => {
							if (response.status === 1) {
								this.setState({ loading: false });
								this.props.currentQrJob(null);
								resolve();
							} else {
								this.setState({ loading: false });
								this.props.currentQrJob(null);
								reject();
							}
						})
						.catch(() => {
							this.setState({ loading: false });
							this.props.currentQrJob(null);
							reject();
						});
				});
				const clickMsg = (
					<div onClick={this.redirectToQr}>
						Your labels have been generated for job{" "}
						<strong>
							{this.props.location.state.jobNumber} {this.props.location.state.name} -{" "}
							{this.props.location.state.shipment}!{" "}
						</strong>
						<span style={{ color: "blue", cursor: "pointer", display: "block" }}>Click here</span>
					</div>
				);
				toast.promise(resolveAfterPromise, {
					pending:
						"Please wait, Your QR Codes are being created. Please avoid any qr related action like delete!",
					success: {
						render({ data }) {
							return clickMsg;
						},
					},
					error: "Error while generating qr codes!",
				});
			}
		});
	};

	normFile = (e) => {
		if (Array.isArray(e)) {
			return e;
		}
		return e && e.fileList;
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		const formItemLayout = {
			labelCol: {
				xs: {
					span: 24,
				},
				sm: {
					span: 5,
				},
			},
			wrapperCol: {
				xs: { span: 24 },
				sm: {
					span: 12,
					offset: 1,
				},
				md: {
					span: 12,
					offset: 1,
				},
				lg: {
					span: 12,
					offset: 1,
				},
				xl: {
					span: 10,
					offset: 1,
				},
			},
		};
		const tailFormItemLayout = {
			wrapperCol: {
				xs: {
					span: 24,
					offset: 6,
				},
				sm: {
					span: 16,
					offset: 6,
				},
				xl: {
					offset: 6,
				},
			},
		};

		return (
			<LayoutContentWrapper style={{ height: "75vh", padding: "0px" }}>
				<div className='add_header'>
					<h2 style={{ marginBottom: "0" }}>
						<i class='fas fa-plus'></i>&emsp;Add QR Code
					</h2>
					<button className='backButton' onClick={() => this.props.history.goBack()}>
						<i className='fa fa-chevron-left' aria-hidden='true'></i> Back
					</button>
				</div>
				<LayoutContent style={{ margin: "0 20px", height: "93%", overflowY: "auto" }}>
					<div className='print-qr'>
						<Spin
							spinning={this.state.loading}
							indicator={antIcon}
							tip={
								<div>
									<p style={{ margin: "0" }}>
										Please wait, Your QR Codes are being created, this could take several minutes depending on
										the number of labels. You may continue with other work and you will be notified when your
										labels have been generated.
									</p>
								</div>
							}>
							{/* <Spin spinning={this.state.contentloader} indicator={antIcon}> */}
							<Form {...formItemLayout} onSubmit={this.handleSubmit}>
								<Form.Item label='Quantity'>
									{getFieldDecorator("quantity", {
										placeholder: "Quantity",
										rules: [
											
											{
												required: true,
												message: "Please input quantity!",
											},

											{
												min: 1,
												type: "number",
												message: "Quantity must be numeric & greater than 0!",
											},

											{
												max: 500,
												type: "number",
												message: "Quantity must be numeric & less than or equal to 500!"
											}
										],
									})(<InputNumber placeholder='Quantity' style={{ width: "100%" }} />)}
								</Form.Item>

								<Form.Item {...tailFormItemLayout}>
									<Button
										className='submitButton'
										loading={this.state.loading}
										type='primary'
										htmlType='submit'>
										Add Qr Code
									</Button>
								</Form.Item>
							</Form>
							{/* </Spin> */}
						</Spin>
					</div>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

const mapStateToProps = (state) => {
	return { jobId: state.App.currentQrJob };
};

export default Form.create()(connect(mapStateToProps, { changeCurrent, currentQrJob })(AddQrCode));
