
export const sortFunction = (a,b,key) => {
    if (a[key] !== undefined && b[key] !== undefined) {
        if (a[key].toString().toLowerCase() < b[key].toString().toLowerCase())
        return -1;
        if (a[key].toString().toLowerCase() > b[key].toString().toLowerCase())
        return 1;
    }
    return 0;
}

export const onFilterMethod = (value, record, key) => {
        if (record[`${key}`] !== 'undefined' && record[`${key}`] !== undefined) {
        return (
            String(record[`${key}`]).toLowerCase().includes(value.toLowerCase())
        )
    } else {
        return ''
    }
}

export function convertToArray (obj) {
    let array = []
    for(let key in obj) {
        if(obj.hasOwnProperty(key) && obj[key].length > 0)
            array.push({
                field   : key,
                keyword : obj[key][0]
            })
    }
    return array;
}
  
