import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class StaffRoutes extends React.Component {
  render() {
    const { url } = this.props.match;
    return (
      <Switch>
        <Route
          exact
          path={`${url}/edit/:id`}
          component={asyncComponent(() =>
            import("../../components/job/editJob")
          )}
        />
        <Route
          exact
          path={`${url}/view-inventory/:id/`}
          component={asyncComponent(() =>
            import("../../components/job/viewJobInventory")
          )}
        />
        <Route
          exact
          path={`${url}/edit-inventory/:id/`}
          component={asyncComponent(() =>
            import("../../components/job/editJobInventory")
          )}
        />
        <Route
          exact
          path={`${url}/view/:id`}
          component={asyncComponent(() =>
            import("../../components/job/viewJob")
          )}
        />
        <Route
          exact
          path={`${url}/add/shipment-type-stage`}
          component={asyncComponent(() =>
            import("../../components/job/addShipmentTypeStage")
          )}
        />

        <Route
          exact
          path={`${url}/edit/shipment-type-stage/:id`}
          component={asyncComponent(() =>
            import("../../components/job/editShipmentTypeStage")
          )}
        />


        <Route
          exact
          path={`${url}/view/shipment-type-stage/:id`}
          component={asyncComponent(() =>
            import("../../components/job/viewShipmentTypeStage")
          )}
        />

        <Route
          exact
          path={`${url}/add`}
          component={asyncComponent(() =>
            import("../../components/job/addJob")
          )}
        />
        <Route
          exact
          path={`${url}`}
          component={asyncComponent(() =>
            import("../../components/job/jobManagement")
          )}
        />
      </Switch>
    );
  }
}

export default StaffRoutes;
