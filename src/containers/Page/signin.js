import { Button, Form, Input, message, notification, Select } from "antd";
import React, { Component } from "react";
import { connect } from "react-redux";
import { Link } from "react-router-dom";

// import { Redirect } from "react-router-dom";
import Api from "../../api/api-handler";
import IntlMessages from "../../components/utility/intlMessages";
// import Input from "../../components/uielements/input";
// import Button from "../../components/uielements/button";
import authAction from "../../redux/auth/actions";
import logo from "../../static/images/logo.png";
import SignInStyleWrapper from "./signin.style";
import { SUPER_ADMIN_COMPANYID } from "../../static/data/constants";




//** SuperAdmin Company ID */
// const SUPERADMINCOMPNAYID  = SUPER_ADMIN_COMPANYID
const SUPERADMINCOMPNAYID = -1

const { login } = authAction;
const { Option } = Select;

const API = new Api({});
class SignIn extends Component {
	state = {
		redirectToReferrer: false,
		loading: false,
		messageClose: true,
		timeDuration: 3,
		customerSearch: "",
		companyData: [],
		companyDataFromField: null,


	};
	componentDidMount() {
		if (this.props.isLoggedIn) {
			this.props.history.push("/dashboard");
			// this.setState({ redirectToReferrer: true });
		}
		this.setState({ contentloader: true, loading: true })
		API.get(`api/admin/companyID/basic/?search=${this.state.customerSearch}`)
			.then((response) => {
				if (response) {
					this.setState({
						companyData: response.data,
						contentloader: false,
						loading: false
					});
				} else {
					this.setState({ contentloader: false, loading: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false });
			});
	}

	notificationHandleLeter = () => {
	}


	fetchCompanyDropdownList = (serchProps) => {
		API.get(`api/admin/companyID/basic/?search=${serchProps.searchFieldValue}`)
			.then((response) => {
				if (response) {
					this.setState({
						companyData: response.data && response.data,
						contentloader: false,
						loading: false
					});
				} else {
					this.setState({ contentloader: false, loading: false });
				}
			})
			.catch((error) => {
				this.setState({ contentloader: false, loading: false });
			});
	};


	onSearch = (name, value) => {
		const data = {
			searchFieldName: name,
			searchFieldValue: value,
		};
		this.fetchCompanyDropdownList(data);
	};

	OnCompanyIDChange = (values) => {
		this.setState({ companyDataFromField: values });
	};

	notificationHandleDismiss = () => {
		this.setState({ loading: true });
		API.post("api/admin/notificationStatusChange")
			.then((response) => {
				if (response) {
					this.setState({ loading: false });
				}
				else {
					this.setState({ loading: false });
				}
			})
			.catch(() => this.setState({ loading: false }));
	}

	handleLogin = (e) => {
		e.preventDefault();
		this.setState({ loading: true });
		const { login } = this.props;
		this.props.form.validateFieldsAndScroll((err, values) => {
			if (!err) {
				const data = [];
				data["data"] = values;
				API.post("api/admin/signin", data)
					.then((response) => {
						if (response) {
							if (response.data && response.data.roles && (response.data.roles == "COMPANYSUPERADMIN" || response.data.roles == "MAINADMIN")) {
								const data = response.data
								this.props.history.push({ pathname: "/signin-with-company", state: data });
							} else if (response.data.userType === 1 && response.data.admin_id !== null && response.data.admin_id !== "") {
								localStorage.setItem("email", response.data.email);
								localStorage.setItem("userType", response.data.userType);
								localStorage.setItem("apiKeyStatus", 0);
								localStorage.setItem("editShipmentTypeFlagCheck", 1);
								localStorage.setItem("isLoginWithCompanyId", false)

								login({
									access_token: response.data.access_token,
									adminid: response.data.admin_id ? response.data.admin_id : "",
									fname: response.data.first_name ? response.data.first_name : "Admin",
									lname: response.data.last_name ? response.data.last_name : "",
									company: response.data.company_name
										? response.data.company_name
										: response.data["staff_company.company_name"]
											? response.data["staff_company.company_name"]
											: "",
									companyID: response.data.company_id ? response.data.company_id : SUPERADMINCOMPNAYID,
									staffId: response.data.staff_id ? response.data.staff_id : "",
									pdf_time_stamp_checked: response.data.pdf_time_stamp_checked
										? response.data.pdf_time_stamp_checked
										: response.data["staff_company.pdf_time_stamp_checked"]
											? response.data["staff_company.pdf_time_stamp_checked"]
											: "",
								});
								this.setState({ loading: false });
								if (response.data.notificationStatus === 0) {
									localStorage.setItem("firstTimeLogin", 1);
									this.props.history.push({ pathname: "/dashboard", state: { notificationStatus: response.data.notificationStatus } });
									message.success("Welcome Back!");

								}
								else {
									localStorage.setItem("firstTimeLogin", 0);
									this.props.history.push({ pathname: "/dashboard", state: { notificationStatus: response.data.notificationStatus } });
									message.success("Welcome Back!");
								}
							}

							else {
								if (response.data && response.data.api_status) {
									localStorage.setItem("apiKeyStatus", response.data.api_status);
								}

								localStorage.setItem("email", response.data.email);
								localStorage.setItem("userType", response.data.userType);
								localStorage.setItem("editShipmentTypeFlagCheck", response.data.staff_id ? response.data.is_admin_can_edit_shipment_type : 1);
								localStorage.setItem("companyEmailForStaff", response.data.staff_id ? response.data["staff_company.email"] : null);
								localStorage.setItem("isLoginWithCompanyId", false)

								login({
									access_token: response.data.access_token,
									adminid: response.data.admin_id ? response.data.admin_id : "",
									fname: response.data.first_name ? response.data.first_name : "Admin",
									lname: response.data.last_name ? response.data.last_name : "",
									company: response.data.company_name
										? response.data.company_name
										: response.data["staff_company.company_name"]
											? response.data["staff_company.company_name"]
											: "",
									companyID: response.data.company_id ? response.data.company_id : "",
									staffId: response.data.staff_id ? response.data.staff_id : "",
									pdf_time_stamp_checked: response.data.pdf_time_stamp_checked
										? response.data.pdf_time_stamp_checked
										: response.data["staff_company.pdf_time_stamp_checked"]
											? response.data["staff_company.pdf_time_stamp_checked"]
											: "",
								});

								this.setState({ loading: false });
								if (response.data.notificationStatus == 0) {
									localStorage.setItem("firstTimeLogin", 1);
									this.props.history.push({ pathname: "/dashboard", state: { notificationStatus: response.data.notificationStatus } });
									message.success("Welcome Back!");
								}
								else {
									localStorage.setItem("firstTimeLogin", 0);
									this.props.history.push({ pathname: "/dashboard", state: { notificationStatus: response.data.notificationStatus } });
									message.success("Welcome Back!");
								}
							}

						} else {
							this.setState({ loading: false });
						}
					})
					.catch(() => this.setState({ loading: false }));
			} else {
				this.setState({ loading: false });
			}
		});
	};
	render() {
		// const from = { pathname: "/dashboard" };
		// const { redirectToReferrer } = this.state;
		const { getFieldDecorator } = this.props.form;
		// if (this.state.redirectToReferrer) {
		// 	return <Redirect to={{ ...from }} />;
		// }
		return (
			<SignInStyleWrapper className='isoSignInPage'>
				<div className='isoLoginContentWrapper' style={{ boxShadow: "2px 4px 14px rgba(0,0,0,1)" }}>
					<div className='isoLoginContent'>
						<div className='isoLogoWrapper'>
							<img src={logo} width='100px' alt='Mover Inventory' />
						</div>
						{/* <h4 style={{ textAlign: "center" }}>Mover Inventory CMS</h4> */}
						<Form onSubmit={this.handleLogin} className='isoSignInForm'>

							<Form.Item
								style={{ marginBottom: "0" }}
								className='isoInputWrapper'
								label='Email'
								hasFeedback>
								{getFieldDecorator("email", {
									rules: [
										{
											type: "email",
											message: "Please enter valid email!",
										},
										{
											required: true,
											message: "Please enter your email!",
										},
									],
								})(<Input placeholder='Enter Email' />)}
							</Form.Item>

							<Form.Item
								className='isoInputWrapper'
								label='Password'
								style={{ marginBottom: "0" }}
								hasFeedback>
								{getFieldDecorator("password", {
									rules: [{ required: true, message: "Please enter your password!" }],
								})(<Input.Password placeholder='Enter Password' />)}
							</Form.Item>

							<Form.Item style={{ margin: "25px 0 0 0" }}>
								<div className='isoInputWrapper isoLeftRightComponent'>
									<Button loading={this.state.loading} type='primary' htmlType='submit'>
										<IntlMessages id='page.signInButton' />
									</Button>
								</div>
							</Form.Item>
						</Form>
						<br />
						<Link
							to={"/forgot-password"}
						>
							<span className='nav-text-child'>
								Forgot Password?
							</span>
						</Link>
					</div>
				</div>
			</SignInStyleWrapper>
		);
	}
}

const mapStateToProps = (state) => ({
	isLoggedIn: state.Auth.isLogin,
});

export default Form.create()(connect(mapStateToProps, { login })(SignIn));
