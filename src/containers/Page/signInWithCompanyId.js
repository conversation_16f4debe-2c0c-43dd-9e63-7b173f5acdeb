import { Button, Form, Input, message, notification, Select } from "antd";
import React, { Component } from "react";
import { connect } from "react-redux";
import { Link } from "react-router-dom";

// import { Redirect } from "react-router-dom";
import Api from "../../api/api-handler";
import IntlMessages from "../../components/utility/intlMessages";
// import Input from "../../components/uielements/input";
// import Button from "../../components/uielements/button";
import authAction from "../../redux/auth/actions";
import logo from "../../static/images/logo.png";
import SignInStyleWrapper from "./signin.style";
import { SUPER_ADMIN_COMPANYID } from "../../static/data/constants";




//** SuperAdmin Company ID */
// const SUPERADMINCOMPNAYID  = SUPER_ADMIN_COMPANYID
const SUPERADMINCOMPNAYID = -1

const { login } = authAction;
const { Option } = Select;

const API = new Api({});
class SignInWithCompany extends Component {
    state = {
        redirectToReferrer: false,
        loading: false,
        messageClose: true,
        timeDuration: 3,
        customerSearch: "",
        companyData: [],
        companyDataFromField: null,

    };
    componentDidMount() {
        if (this.props.isLoggedIn) {
            this.props.history.push("/dashboard");
        }
        const roles = this.props && this.props.location && this.props.location.state && this.props.location.state.roles

        if (roles === "MAINADMIN") {
            this.setState({ contentloader: true, loading: true })
            API.post("api/admin/super-admin-company-list")
                .then((response) => {
                    if (response) {
                        this.setState({
                            companyData: response.data.rows,
                            contentloader: false,
                            loading: false
                        });
                    } else {
                        this.setState({ contentloader: false, loading: false });
                    }
                })
                .catch((error) => {
                    this.setState({ contentloader: false });
                });
        }
        else {
            const groupId = this.props.location.state.group_id
            const data = [];
            data["data"] = { group_id: groupId };
            this.setState({ contentloader: true, loading: true })
            API.post(`api/admin/group-company-list`, data)
                .then((response) => {
                    if (response) {
                        this.setState({
                            companyData: response.data.rows,
                            contentloader: false,
                            loading: false
                        });
                    } else {
                        this.setState({ contentloader: false, loading: false });
                    }
                })
                .catch((error) => {
                    this.setState({ contentloader: false });
                });
        }
    }


    OnCompanyIDChange = (values) => {
        this.setState({ companyDataFromField: values });
    };


    handleSubmit = (e) => {
        e.preventDefault();
        this.setState({ loading: true });
        const { login } = this.props;
        this.props.form.validateFieldsAndScroll((err, values) => {
            if (!err) {
                let params = {
                    company_id: values.company_id.key
                };
                const data = [];
                data["data"] = params;
                API.post("api/admin/signin-with-companyId", data)
                    .then((response) => {
                        if (response.data && response.data.api_status) {
                            localStorage.setItem("apiKeyStatus", response.data.api_status);
                        }

                        localStorage.setItem("email", response.data.email);
                        localStorage.setItem("userType", response.data.userType);
                        localStorage.setItem("editShipmentTypeFlagCheck", response.data.staff_id ? response.data.is_admin_can_edit_shipment_type : 1);
                        localStorage.setItem("companyEmailForStaff", response.data.staff_id ? response.data["staff_company.email"] : null);
                        localStorage.setItem("isLoginWithCompanyId", true)
                        localStorage.setItem("groupId", response.data.group_id)
                        localStorage.setItem("loginRoles", this.props && this.props.location && this.props.location.state && this.props.location.state.roles)

                        login({
                            access_token: response.data.access_token,
                            adminid: response.data.admin_id ? response.data.admin_id : "",
                            fname: response.data.first_name ? response.data.first_name : "Admin",
                            lname: response.data.last_name ? response.data.last_name : "",
                            company: response.data.company_name
                                ? response.data.company_name
                                : response.data["staff_company.company_name"]
                                    ? response.data["staff_company.company_name"]
                                    : "",
                            companyID: response.data.company_id ? response.data.company_id : "",
                            staffId: response.data.staff_id ? response.data.staff_id : "",
                            pdf_time_stamp_checked: response.data.pdf_time_stamp_checked
                                ? response.data.pdf_time_stamp_checked
                                : response.data["staff_company.pdf_time_stamp_checked"]
                                    ? response.data["staff_company.pdf_time_stamp_checked"]
                                    : "",
                        });

                        this.setState({ loading: false });
                        if (response.data.notificationStatus == 0) {
                            localStorage.setItem("firstTimeLogin", 1);
                            this.props.history.push({ pathname: "/dashboard", state: { notificationStatus: response.data.notificationStatus } });
                            message.success("Welcome Back!");
                        }
                        else {
                            localStorage.setItem("firstTimeLogin", 0);
                            this.props.history.push({ pathname: "/dashboard", state: { notificationStatus: response.data.notificationStatus } });
                            message.success("Welcome Back!");
                        }

                    })
                    .catch(() => this.setState({ loading: false }));
            } else {
                this.setState({ loading: false });
            }
        });
    };
    render() {
        const { getFieldDecorator } = this.props.form;

        return (
            <SignInStyleWrapper className='isoSignInPage'>
                <div className='isoLoginContentWrapper' style={{ boxShadow: "2px 4px 14px rgba(0,0,0,1)" }}>
                    <div className='isoLoginContent'>
                        <div className='isoLogoWrapper'>
                            <img src={logo} width='100px' alt='Mover Inventory' />
                        </div>
                        <Form onSubmit={this.handleSubmit} className='isoSignInForm'>

                            <Form.Item label='Select Company'>
                                {getFieldDecorator("company_id", {
                                })(
                                    <Select
                                        className='isoInputWrapper'
                                        size={"default"}
                                        showSearch
                                        labelInValue
                                        optionFilterProp='children'
                                        placeholder='Select Company'
                                        style={{ width: "100%", marginBottom: "0" }}
                                    >
                                        {this.state.companyData
                                            ? this.state.companyData.map((e) => (
                                                <Option
                                                    key={e.company_id}
                                                    value={e.company_id}
                                                    onClick={this.OnCompanyIDChange.bind(null, e)}>
                                                    {e.company_name}
                                                </Option>
                                            ))
                                            : null}
                                    </Select>
                                )}
                            </Form.Item>

                            <Form.Item style={{ margin: "25px 0 0 0" }}>
                                <div className='isoInputWrapper isoLeftRightComponent'>
                                    <Button loading={this.state.loading} type='primary' htmlType='submit'>
                                        Submit
                                    </Button>
                                </div>
                            </Form.Item>
                        </Form>
                    </div>
                </div>
            </SignInStyleWrapper>
        );
    }
}

const mapStateToProps = (state) => ({
    isLoggedIn: state.Auth.isLogin,
});

export default Form.create()(connect(mapStateToProps, { login })(SignInWithCompany));
