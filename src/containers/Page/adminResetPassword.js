import React from "react";
import { Form, Input, Button, message } from "antd";
import Api from "../../api/api-handler";
import SignInStyleWrapper from "./signin.style";

const API = new Api({});

class AdminResetPassword extends React.Component {
  state = {
    loading: false,
    adminId: "",
    companyId: "",
    staffId: "",
  };


  componentDidMount() {

    const checkData = this.props.location.state;
    

    this.setState({
      adminId: checkData.admin_id ? checkData.admin_id : "",
      staffId: checkData.staff_id ? checkData.staff_id : "",
      companyId: checkData.company_id ? checkData.company_id : "",
    })
  }



  compareToFirstPassword = (rule, value, callback) => {
    const form = this.props.form;
    if (value && value !== form.getFieldValue("password")) {
      callback("Two passwords that you enter is inconsistent!");
    } else {
      callback();
    }
  };

  validateToNextPassword = (rule, value, callback) => {
    const form = this.props.form;
    if (value && this.state.confirmDirty) {
      form.validateFields(["confirm"], { force: true });
    }
    callback();
  };
  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFieldsAndScroll((err, values) => {
      let params = {
        new_password: values.password,
        code: values.resetpasswordcode,
        confirm_password: values.confirm,
        adminId: this.state.adminId,
        companyId: this.state.companyId,
        staffId: this.state.staffId
      };

      if (!err) {
        const data = [];
        data["data"] = params;
        this.setState({ loading: true });
        API.post("api/admin/reset-password", data).then((response) => {
          if (response.status) {
            message.success(response.message);
            this.props.history.push("/signin");
          } else {
            message.error(response.message);
            this.setState({ loading: false });
          }
        });
      }
    });
  };
  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <SignInStyleWrapper className="isoSignInPage">
        <div className="isoLoginContentWrapper">
          <div className="isoLoginContent">
            <Form onSubmit={this.handleSubmit}>
              <Form.Item label="OTP">
                {getFieldDecorator("resetpasswordcode", {
                  rules: [
                    {
                      required: true,
                      message: "Please input your reset code!",
                    },
                  ],
                })(<Input placeholder="Enter Reset Code" />)}
              </Form.Item>
              <Form.Item label="Password">
                {getFieldDecorator("password", {
                  rules: [
                    {
                      required: true,
                      message: "Please input your password!",
                    },
                    {
                      validator: this.validateToNextPassword,
                    },
                  ],
                })(<Input type="password" />)}
              </Form.Item>
              <Form.Item label="Confirm">
                {getFieldDecorator("confirm", {
                  rules: [
                    {
                      required: true,
                      message: "Please input your confirm password!",
                    },
                    {
                      validator: this.compareToFirstPassword,
                    },
                  ],
                })(<Input type="password" />)}
              </Form.Item>
              <Form.Item>
                <center>
                  <Button
                    loading={this.state.loading}
                    className="submitButton"
                    type="primary"
                    htmlType="submit"
                  >
                    Submit
                  </Button>
                </center>
              </Form.Item>
            </Form>
          </div>
        </div>
      </SignInStyleWrapper>
    );
  }
}

export default Form.create()(AdminResetPassword);
