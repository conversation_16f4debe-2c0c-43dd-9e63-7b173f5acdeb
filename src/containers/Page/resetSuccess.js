import React from "react";
import SignInStyleWrapper from "./signin.style";

class ResetSuccess extends React.Component {
  state = {
    loading: false,
  };
  
  render() {
    return (
      <SignInStyleWrapper className="isoSignInPage">
        <div className="isoLoginContentWrapper">
          <div className="isoLoginContent">
            <h1 style={{ color: "green", textAlign: "center" }}>Password Reset Successfully!</h1>
          </div>
        </div>
      </SignInStyleWrapper>
    );
  }
}

export default ResetSuccess;
