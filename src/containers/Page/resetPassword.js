import React from "react";
import { Form, Input, Button, message } from "antd";
import Api from "../../api/api-handler";
import SignInStyleWrapper from "./signin.style";

const API = new Api({});

class ResetPassword extends React.Component {
  state = {
    loading: false,
  };

 
  compareToFirstPassword = (rule, value, callback) => {
    const form = this.props.form;
    if (value && value !== form.getFieldValue("password")) {
      callback("Two passwords that you enter is inconsistent!");
    } else {
      callback();
    }
  };

  validateToNextPassword = (rule, value, callback) => {
    const form = this.props.form;
    if (value && this.state.confirmDirty) {
      form.validateFields(["confirm"], { force: true });
    }
    callback();
  };
  handleSubmit = (e) => {
    e.preventDefault();
    this.props.form.validateFieldsAndScroll((err, values) => {
      let params = {
        new_password: values.password,
        code: values.resetpasswordcode,
        confirm_password: values.confirm,
      };
      if (!err) {
        const data = [];
        data["data"] = params;
        this.setState({ loading: true });
        API.post("api/admin/cms/reset-password", data).then((response) => {
          if (response.status) {
            message.success(response.message);
            this.props.history.push("/reset-success");
          } else {
            message.error(response.message);
            this.setState({ loading: false });
          }
        });
      }
    });
  };
  render() {
    const { getFieldDecorator } = this.props.form;
    return (
      <SignInStyleWrapper className="isoSignInPage">
        <div className="isoLoginContentWrapper">
          <div className="isoLoginContent">
            <Form onSubmit={this.handleSubmit}>
              <Form.Item label="OTP">
                {getFieldDecorator("resetpasswordcode", {
                  rules: [
                    {
                      required: true,
                      message: "Please input your reset code!",
                    },
                  ],
                })(<Input placeholder="Enter Reset Code" />)}
              </Form.Item>
              <Form.Item label="Password">
                {getFieldDecorator("password", {
                  rules: [
                    {
                      required: true,
                      message: "Please input your password!",
                    },
                    {
                      validator: this.validateToNextPassword,
                    },
                  ],
                })(<Input type="password" />)}
              </Form.Item>
              <Form.Item label="Confirm">
                {getFieldDecorator("confirm", {
                  rules: [
                    {
                      required: true,
                      message: "Please input your confirm password!",
                    },
                    {
                      validator: this.compareToFirstPassword,
                    },
                  ],
                })(<Input type="password" />)}
              </Form.Item>
              <Form.Item>
                <center>
                  <Button
                    loading={this.state.loading}
                    className="submitButton"
                    type="primary"
                    htmlType="submit"
                  >
                    Submit
                  </Button>
                </center>
              </Form.Item>
            </Form>
          </div>
        </div>
      </SignInStyleWrapper>
    );
  }
}

export default Form.create()(ResetPassword);
