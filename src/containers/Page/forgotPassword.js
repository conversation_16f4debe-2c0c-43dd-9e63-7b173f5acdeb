import React from "react";
import { Form, Input, Button, message } from "antd";
import Api from "../../api/api-handler";
import SignInStyleWrapper from "./signin.style";

const API = new Api({});

class forgotPassword extends React.Component {
    state = {
        loading: false,
    };

    handleSubmit = (e) => {
        e.preventDefault();
        this.props.form.validateFieldsAndScroll((err, values) => {
            let params = {
                email: values.email,
            };
            if (!err) {
                const data = [];
                data["data"] = params;
                this.setState({ loading: true });
                API.post("api/admin/forgot-password", data).then((response) => {
                    if (response.status) {
                        const data = response.data
                        message.success(response.message);
                        this.props.history.push({ pathname: "/admin-reset-password", state: data });
                    } else {
                        message.error(response.message);
                        this.setState({ loading: false });
                    }
                });
            }
        });
    };
    render() {
        const { getFieldDecorator } = this.props.form;
        return (
            <SignInStyleWrapper className="isoSignInPage">
                <div className="isoLoginContentWrapper">
                    <div className="isoLoginContent">
                        <Form onSubmit={this.handleSubmit} className='isoSignInForm'>
                            <Form.Item
                                style={{ marginBottom: "0" }}
                                className='isoInputWrapper'
                                label='Email'
                                hasFeedback>
                                {getFieldDecorator("email", {
                                    rules: [
                                        {
                                            type: "email",
                                            message: "Please enter valid email!",
                                        },
                                        {
                                            required: true,
                                            message: "Please enter your email!",
                                        },
                                    ],
                                })(<Input placeholder='Enter Email' />)}
                            </Form.Item>

                            <Form.Item style={{ margin: "25px 0 0 0" }}>
                                <div className='isoInputWrapper isoLeftRightComponent'>
                                    <Button loading={this.state.loading} type='primary' htmlType='submit'>
                                        Submit
                                    </Button>
                                </div>
                            </Form.Item>
                        </Form>
                    </div>
                </div>
            </SignInStyleWrapper>
        );
    }
}

export default Form.create()(forgotPassword);
