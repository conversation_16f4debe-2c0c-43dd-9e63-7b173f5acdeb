import React from "react";
import { Form, Input } from "antd";
import LayoutContentWrapper from "../../components/utility/layoutWrapper";
import LayoutContent from "../../components/utility/layoutContent";
import PageHeader from "../../components/utility/pageHeader";
import { SettingsContainer } from "./SettingsContainer.style";
import AntButton from "../../components/uielements/button";

class Settings extends React.Component {
	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			if (!err) {
				//console.log('Received values of form: ', values);
			}
		});
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		return (
			<LayoutContentWrapper style={{ height: "100vh" }}>
				<LayoutContent>
					<div className='title-container'>
						<PageHeader>Settings</PageHeader>
					</div>
					<SettingsContainer>
						<div className='module-container'>
							<Form onSubmit={this.handleSubmit}>
								<Form.Item>
									{getFieldDecorator("current_password", {
										rules: [{ required: true, message: "Please enter." }],
									})(<Input placeholder='Update information' />)}
								</Form.Item>
								<Form.Item>
									<AntButton type='primary' htmlType='submit' className='login-form-button'>
										Update
									</AntButton>
								</Form.Item>
							</Form>
						</div>
					</SettingsContainer>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

const WrappedSettings = Form.create()(Settings);
export default WrappedSettings;
