import { Col, Icon, Row, Skeleton, notification, Input, DatePicker, But<PERSON>, Modal, Table, Divider, Spin, } from "antd";
import React, { Component } from "react";
import { connect } from "react-redux";
import { Link } from "react-router-dom";
import Api from "../api/api-handler";
import LayoutContent from "../components/utility/layoutContent";
import LayoutContentWrapper from "../components/utility/layoutWrapper";
import appActions from "../redux/app/actions";
import classes from "./dashboard.module.css";
import scrollTo from "../components/scrollTo";
import { Column as Chart } from '@ant-design/plots';
import { CSVLink } from "react-csv";
import moment from "moment";

const API = new Api({});
const { changeCurrent } = appActions;
const Search = Input.Search;
const antIcon = <Icon type='loading' style={{ fontSize: 24 }} spin />;
let timeoutVar;
const { RangePicker } = DatePicker;


class DashBoard extends Component {
	state = {
		dashboard: {},
		exportCSV: [],
		exportCSVShipment: [],
		apiParam: {},
		user: localStorage.getItem("userType") ? localStorage.getItem("userType") : "",
		pageloading: true,
		deleteModal: false,
		search: null,
		data: [
			{
				date: '1',
				count: 1,
			},
			{
				date: '2',
				count: 2,
			},
			{
				date: '3',
				count: 3,
			},
			{
				date: '4',
				count: 4,
			},
			{
				date: '5',
				count: 5,
			},
		]
	};



	componentDidMount() {
		this.props.changeCurrent("dashboard");
		const companyId = localStorage.getItem("companyID") ? localStorage.getItem("companyID") : "";
		let params = {
			search: "",
			orderBy: "created_at",
			pageNo: "1",
			orderSequence: "DESC",
			pageSize: 25,
			dateFilter: ["2020-01-01", moment().format('YYYY-MM-DD')],
			company_id: companyId
		};
		this.setState({ apiParam: params }, () => this.fetchCompanyList());


		const firstTimeLogin = localStorage.getItem("firstTimeLogin");

		// if (firstTimeLogin == 1) {
		// 	if (this.props.location.state.notificationStatus == 0) {
		// 		this.setState({
		// 			deleteModal: true
		// 		})
		// 	}
		// }


	}



	fetchCompanyList = () => {
		const data = [];
		data["data"] = this.state.apiParam;
		this.setState({ pageloading: true });
		API.post("api/admin/dashboard", data).then((response) => {
			this.setState({
				dashboard: response.data,
				data: response.data && response.data.companiesListChart,
				exportCSV: response.data && response.data.companiesList,
				exportCSVShipment: response.data && response.data.shipmentList,
				pageloading: false,
			});
		});
	};

	handleOk = () => {
		this.setState({ loading: true });
		API.post("api/admin/notificationStatusChange")
			.then((response) => {
				if (response) {
					this.setState({
						loading: false,
						deleteModal: false
					});
					localStorage.setItem("firstTimeLogin", 0);
				}
				else {
					this.setState({
						loading: false,
						deleteModal: false
					});
					localStorage.setItem("firstTimeLogin", 0);
				}
			})
			.catch(() => this.setState({
				loading: false,
				deleteModal: false
			}));
	}


	handleCancel = () => {
		localStorage.setItem("firstTimeLogin", 0);
		this.setState({ deleteModal: false, confirmLoading: false });
	};

	handleChange = (pagination, filters, sorter) => {
		let sortData = Array.isArray(sorter.field) ? sorter.field[0] : sorter.field
		scrollTo();
		let params = {
			...this.state.apiParam,
			orderBy: sorter.field === "createdAt" ? "created_at" : sortData,
			orderSequence: sorter.order === "ascend" ? "ASC" : "DESC",
		};
		this.setState(
			{
				apiParam: params,
			},
			() => {
				this.state.search ? this.handleSearch(this.state.search) : this.fetchCompanyList();
			}
		);
	};

	callback = () => {
		if (timeoutVar) {
			clearTimeout(timeoutVar);
			timeoutVar = null;
		}
		timeoutVar = setTimeout(this.fetchCompanyList, 1000);
	};

	handleSearch = (e) => {
		this.setState(
			{
				apiParam: {
					...this.state.apiParam,
					search: e,
				},
				search: e,
			},
			this.callback
		);
	};


	handleChangeDatePicker = (date, dateString) => {
		if (date.length > 0) {
			scrollTo();
			let params = {
				...this.state.apiParam,
				dateFilter: dateString
			};
			this.setState(
				{
					apiParam: params,
				},
				() => {
					this.state.search ? this.handleSearch(this.state.search) : this.fetchCompanyList();
				}
			);
		}

		else {
			scrollTo();
			let params = {
				...this.state.apiParam,
				dateFilter: ["2020-01-01", moment().format('YYYY-MM-DD')]
			};
			this.setState(
				{
					apiParam: params,
				},
				() => {
					this.state.search ? this.handleSearch(this.state.search) : this.fetchCompanyList();
				}
			);
		}

	}


	render() {
		const { dashboard, data } = this.state;
		// let shipmentDelivered = `${dashboard.shipmentActive -
		// 	dashboard.shipmentInactive}`;
		// let shipmentNotDeliverd = `${dashboard.shipmentActive - shipmentDelivered}`;

		const headers = [
			{ label: "CompanyName", key: "company_name" },
			{ label: "CompanyID", key: "company_identity" },
			{ label: "CompanyEmail", key: "email" },
			{ label: "Phone", key: "phone" },
			{ label: "Total Customers", key: "total_customers" },
			{ label: "Total Shipments", key: "total_shipments" },
			{ label: "New Shipments Of This Month", key: "total_shipments_current_month" },
			{ label: "New Shipments Of Last Month", key: "total_shipments_last_month" }

		];

		const headersShipment = [
			{ label: "CompanyName", key: "company_name" },
			{ label: "ShipmentName", key: "shipment_name" },
			{ label: "ShipmentId", key: "shipment_job_id" },
			{ label: "CreatedDate", key: "created_at" },
			{ label: "ShipmentComplete", key: "is_job_complete_flag" },
		];



		const brandColor = '#5B8FF9';
		const config = {
			data,
			xField: 'date',
			yField: 'count',
			seriesField: '',
			color: brandColor,
			legend: false,
			xAxis: {
				label: {
					autoHide: true,
					autoRotate: false,
				},
			},
		};

		const columns = [
			{
				title: "CompanyName",
				dataIndex: "company_name",
				key: "company_name",
				sorter: true,
				align: "left",
			},
			{
				title: "CompanyID",
				dataIndex: "company_identity",
				key: "company_identity",
				sorter: true,
				align: "center",
			},
			{
				title: "CompanyEmail",
				dataIndex: "email",
				key: "email",
				sorter: true,
				align: "left",
			},
			{
				title: "Phone",
				dataIndex: "phone",
				minWidth: "15%",
				key: "phone",
				align: "left",
				render: (record, text) => {
					return record === "undefined" || record === "" ? "" : record
				},
			},
			{
				title: "Total Customers",
				dataIndex: "total_customers",
				key: "total_customers",
				// minWidth: "12%",
				align: "center",
			},
			{
				title: "Total Shipments",
				dataIndex: "total_shipments",
				key: "total_shipments",
				align: "center",
			},
			{
				title: "New Shipments Of This Month",
				dataIndex: "total_shipments_current_month",
				key: "total_shipments_current_month",
				align: "center",
			},
			{
				title: "New Shipments Of Last Month",
				dataIndex: "total_shipments_last_month",
				key: "total_shipments_last_month",
				align: "center",
			},
		];

		return (
			<LayoutContentWrapper>
				<div className='top_header'>
					<Row>
						<Col sm={12}>
							<h2 style={{ marginBottom: "0" }}>
								<Icon type='dashboard' />
								&emsp;Dashboard
							</h2>
						</Col>
						<Col sm={12} />
					</Row>
				</div>
				{this.state.user === "1" ? (
					<LayoutContent>
						<Spin
							spinning={this.state.pageloading}
							indicator={antIcon}
							tip={
								<div>
									<p style={{ margin: "0" }}>
									</p>
								</div>
							}
						>
							<div style={{ marginBottom: "10px" }} >
								<Row>
									<Link to='/company' onClick={() => this.props.changeCurrent("company")}>
										<Col sm={6}>
											<Skeleton loading={this.state.pageloading} active>
												<div
													className={classes.container}
												>
													<h2>
														<i class='fas fa-users'></i>Companies
													</h2>
													<div className={classes.sub_container}>
														<h4>Total</h4> <p>{dashboard.companiesTotal}</p>
													</div>
													<div className={classes.sub_container}>
														<h4>Active</h4> <p>{dashboard.companiesActive}</p>
													</div>
													<div className={classes.sub_container}>
														<h4>Inactive</h4> <p>{dashboard.companiesInactive}</p>
													</div>
												</div>
											</Skeleton>
										</Col>
									</Link>
									<Col sm={3} />
									<Col sm={6}>
										<Skeleton loading={this.state.pageloading} active>
											<div
												className={classes.container}
											// style={{ backgroundColor: "#82b437" }}
											>
												<h2>
													<i class='fas fa-user-tie'></i>Customers
												</h2>
												<div className={classes.sub_container}>
													<h4>Total</h4> <p>{dashboard.customersTotal}</p>
												</div>
												<div className={classes.sub_container}>
													<h4>Active</h4> <p>{dashboard.customersActive}</p>
												</div>
												<div className={classes.sub_container}>
													<h4>Inactive</h4> <p>{dashboard.customersInactive}</p>
												</div>
											</div>
										</Skeleton>
									</Col>
									<Col sm={3} />
									<Link to='/job' onClick={() => this.props.changeCurrent("job")}>
										<Col sm={6}>
											<Skeleton loading={this.state.pageloading} active>
												<div
													className={classes.container}
												// style={{ backgroundColor: "#188264" }}
												>
													<h2>
														<i class='fas fa-truck-loading'></i>Shipments
													</h2>
													<div className={classes.sub_container}>
														<h4>Total</h4> <p>{dashboard.shipmentTotal}</p>
													</div>
													<div className={classes.sub_container}>
														<h4>In-Progress</h4> <p>{dashboard.shipmentActive}</p>
													</div>
													<div className={classes.sub_container}>
														<h4>Complete</h4>
														<p>{dashboard.shipmentInactive}</p>
													</div>
												</div>
											</Skeleton>
										</Col>
									</Link>
								</Row>
							</div>
							<Divider orientation='center' type='horizontal'>
								<div className='display-flex'>
									<label className='fs-16 medium-text'>
										<h2>Companies Lists </h2>
									</label>
								</div>
							</Divider>
							<div style={{ margin: "10px 20px" }}>
								<Row justify="space-between" >
									<Col sm={5}>
										<Search
											placeholder='Search Company'
											onChange={(e) => this.handleSearch(e.target.value)}
											value={this.state.search}
											style={{ width: 200 }}
										/>
									</Col>
									<Col sm={8} >
										<RangePicker allowClear={true} onChange={this.handleChangeDatePicker} />
									</Col>
									<Col sm={5} >
										<CSVLink data={this.state.exportCSVShipment} headers={headersShipment} filename={"data.csv"} target="_blank">
											<Button style={{ marginLeft: "100px" }} type='primary' icon='printer' >
												Shipment CSV Export
											</Button>
										</CSVLink>
									</Col>
									<Col sm={4} >
										<CSVLink data={this.state.exportCSV} headers={headers} filename={"data.csv"} target="_blank">
											<Button style={{ marginLeft: "100px" }} type='primary' icon='printer' >
												Company CSV Export
											</Button>
										</CSVLink>
									</Col>
								</Row>
							</div>
							<div style={{ margin: "10px 0px", overflowX: "auto" }}>
								<Table
									bordered={true}
									columns={columns}
									pagination={{
										showSizeChanger: true,
										defaultPageSize: 100,
										pageSizeOptions: ["10", "50", "100", "200"],
									}}
									dataSource={dashboard.companiesList}
									onChange={this.handleChange}
								/>
							</div>
							<Divider orientation='center' type='horizontal'>
								<div className='display-flex'>
									<label className='fs-16 medium-text'>
										<h2>Companies Created Per Week </h2>
									</label>
								</div>
							</Divider>
							<div style={{ marginTop: "10px", overflowX: "auto" }}>
								<Chart {...config} />;
							</div>
						</Spin>

					</LayoutContent>
				) : (
					<LayoutContent>
						<Spin
							spinning={this.state.pageloading}
							indicator={antIcon}
							tip={
								<div>
									<p style={{ margin: "0" }}>
									</p>
								</div>
							}
						>
							<Row>
								<Link to='/staff' onClick={() => this.props.changeCurrent("staff")}>
									<Col sm={6}>
										<Skeleton loading={this.state.pageloading} active>
											<div
												className={classes.container}
											// style={{ backgroundColor: "#82b437" }}
											>
												<h2>
													<i className='fas fa-users'></i>Users
												</h2>
												<div style={{ padding: "10px" }}>
													<table className={classes.table}>
														<thead>
															<td>
																<th>Role</th>
															</td>
															<td>
																<th>Total</th>
															</td>
															<td>
																<th>Active</th>
															</td>
															{/* <td>
															<th>Inactive</th>
														</td> */}
														</thead>
														<tbody>
															<tr>
																<td>Total</td>
																<td>{dashboard.staffsTotal}</td>
																<td>{dashboard.staffsActive}</td>
																{/* <td>{dashboard.staffsInactive}</td> */}
															</tr>
															<tr></tr>
															<tr>
																<td>Admins</td>
																<td>{dashboard.staffAdminTotal}</td>
																<td>{dashboard.staffAdminActive}</td>
																{/* <td>{dashboard.staffAdminInactive}</td> */}
															</tr>
															<tr>
																<td>Workers</td>
																<td>{dashboard.staffWorkerTotal}</td>
																<td>{dashboard.staffWorkerActive}</td>
																{/* <td>{dashboard.staffWorkerInactive}</td> */}
															</tr>
														</tbody>
													</table>
												</div>
											</div>
										</Skeleton>
									</Col>
								</Link>
								<Col sm={3} />
								<Link to='/customer' onClick={() => this.props.changeCurrent("customer")}>
									<Col sm={6}>
										<Skeleton loading={this.state.pageloading} active>
											<div
												className={classes.container}
											// style={{ backgroundColor: "#82b437" }}
											>
												<h2>
													<i class='fas fa-user-tie'></i>Customers
												</h2>
												<div className={classes.sub_container}>
													<h4>Total</h4> <p>{dashboard.customersTotal}</p>
												</div>
												<div className={classes.sub_container}>
													<h4>Active</h4> <p>{dashboard.customersActive}</p>
												</div>
												<div className={classes.sub_container}>
													<h4>Inactive</h4> <p>{dashboard.customersInactive}</p>
												</div>
											</div>
										</Skeleton>
									</Col>
								</Link>
								<Col sm={3} />
								<Link to='/job' onClick={() => this.props.changeCurrent("job")}>
									<Col sm={6}>
										<Skeleton loading={this.state.pageloading} active>
											<div
												className={classes.container}
											// style={{ backgroundColor: "#509b4d" }}
											>
												<h2>
													<i class='fas fa-truck-loading'></i>Shipments
												</h2>
												<div className={classes.sub_container}>
													<h4>Total</h4> <p>{dashboard.shipmentTotal}</p>
												</div>
												<div className={classes.sub_container}>
													<h4>In-Progress</h4> <p>{dashboard.shipmentActive}</p>
												</div>
												<div className={classes.sub_container}>
													<h4>Complete</h4> <p>{dashboard.shipmentInactive}</p>
												</div>
											</div>
										</Skeleton>
									</Col>
								</Link>
							</Row>
						</Spin>
					</LayoutContent>
				)}

				{
					<Modal
						style={{ position: "relative", top: "20px", left: "380px" }}
						title='Info'
						visible={this.state.deleteModal}
						onOk={this.handleOk}
						okText='Dismiss'
						cancelText='Show me leter'
						confirmLoading={this.state.confirmLoading}
						onCancel={this.handleCancel}>
						<p>Hey there, We have released the new app on android's play store and Apple App Store which contains bug fixes and performance improvements.  Please update immediately to avoid any technical glitch.</p>
					</Modal>
				}

			</LayoutContentWrapper>
		);
	}
}

export default connect(null, { changeCurrent })(DashBoard);
