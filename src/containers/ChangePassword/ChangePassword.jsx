import React from "react";
import { Form, Input, message } from "antd";
import LayoutContentWrapper from "../../components/utility/layoutWrapper";
import LayoutContent from "../../components/utility/layoutContent";
import PageHeader from "../../components/utility/pageHeader";
import { ChangePasswordContainer } from "./ChangePasswordContainer.style";
import AntButton from "../../components/uielements/button";
import Api from "../../api/api-handler";
const API = new Api({});



class ChangePassword extends React.Component {

	state = {
		myEmail: "",
		loading: false,

	};

	componentDidMount() {
		this.setState({
			myEmail: localStorage.getItem("email")
		})
	}

	handleSubmit = (e) => {
		e.preventDefault();
		this.props.form.validateFields((err, values) => {
			values.email = this.state.myEmail
			if (!err) {
				const data = [];
				data["data"] = values;
				API.post(`api/admin/change-password`, data)
					.then((response) => {
						message.success(response.message);
						this.props.history.push("/signin");

					})
					.catch(() => this.setState({ loading: false }));
			}
			else {
				this.setState({ loading: false });
			}
		});
	};

	render() {
		const { getFieldDecorator } = this.props.form;
		return (
			<LayoutContentWrapper style={{ height: "100vh" }}>
				<LayoutContent>
					<div className='title-container'>
						<PageHeader>Change Password</PageHeader>
					</div>
					<ChangePasswordContainer>
						<div className='module-container'>
							<Form onSubmit={this.handleSubmit}>
								<Form.Item>
									{getFieldDecorator("old_password", {
										rules: [{ required: true, message: "Please enter current password." }],
									})(<Input type='password' placeholder='Current Password' />)}
								</Form.Item>
								<Form.Item>
									{getFieldDecorator("new_password", {
										rules: [{ required: true, message: "Please enter new password." }],
									})(<Input type='password' placeholder='Password' />)}
								</Form.Item>
								<Form.Item>
									{getFieldDecorator("confirm_password", {
										rules: [{ required: true, message: "Please enter confirm enter password." }],
									})(<Input type='password' placeholder='Confirm Password' />)}
								</Form.Item>
								<Form.Item>
									<AntButton type='primary' htmlType='submit' className='login-form-button'>
										Change
									</AntButton>
								</Form.Item>
							</Form>
						</div>
					</ChangePasswordContainer>
				</LayoutContent>
			</LayoutContentWrapper>
		);
	}
}

const WrappedChangePassword = Form.create()(ChangePassword);
export default WrappedChangePassword;
