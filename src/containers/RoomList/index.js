import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class RoomListRoutes extends React.Component {
	render() {
		const { url } = this.props.match;
		return (
			<Switch>
				<Route
					exact
					path={`${url}/edit/:id`}
					component={asyncComponent(() =>
						import("../../components/RoomList/editRoomList")
					)}
				/>
				<Route
					exact
					path={`${url}/add`}
					component={asyncComponent(() =>
						import("../../components/RoomList/addRoomList")
					)}
				/>
				<Route
					exact
					path={`${url}`}
					component={asyncComponent(() =>
						import("../../components/RoomList/roomListManagement")
					)}
				/>
			</Switch>
		);
	}
}

export default RoomListRoutes;
