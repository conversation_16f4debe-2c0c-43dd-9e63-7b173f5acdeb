import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class CompanyRoutes extends React.Component {
  render() {
    const { url } = this.props.match;
    return (
      <Switch>
        <Route
          exact
          path={`${url}/edit/:id`}
          component={asyncComponent(() =>
            import("../../components/qrCode/editQrCode")
          )}
        />
        <Route
          exact
          path={`${url}/:id/add`}
          component={asyncComponent(() =>
            import("../../components/qrCode/addQrCode")
          )}
        />
        <Route
          exact
          path={`${url}/:id/print`}
          component={asyncComponent(() =>
            import("../../components/qrCode/printQrCode")
          )}
        />
        <Route
          exact
          path={`${url}/:id/dymo-print`}
          component={asyncComponent(() =>
            import("../../components/qrCode/printDymoQrCode")
          )}
        />
        <Route
          exact
          path={`${url}/:id`}
          component={asyncComponent(() =>
            import("../../components/qrCode/qrCodeManagement")
          )}
        />
      </Switch>
    );
  }
}

export default CompanyRoutes;
