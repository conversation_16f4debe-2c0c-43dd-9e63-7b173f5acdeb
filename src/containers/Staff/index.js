import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class StaffRoutes extends React.Component {
  render() {
    const { url } = this.props.match;
    return (
      <Switch>
        <Route
          exact
          path={`${url}/edit/:id`}
          component={asyncComponent(() =>
            import("../../components/staff/editStaff")
          )}
        />
        <Route
          exact
          path={`${url}/add`}
          component={asyncComponent(() =>
            import("../../components/staff/addStaff")
          )}
        />
        <Route
          exact
          path={`${url}`}
          component={asyncComponent(() =>
            import("../../components/staff/staffManagement")
          )}
        />
      </Switch>
    );
  }
}

export default StaffRoutes;
