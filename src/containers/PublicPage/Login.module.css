.login__main {
	height: 100vh !important;
	/* background-color: #5cad44; */
	background: transparent
		linear-gradient(
			180deg,
			#60930c 0%,
			#5d920f 4%,
			#589115 10%,
			#26834c 69%,
			#1f8154 77%,
			#187f5c 86%,
			#137e62 92%,
			#107d66 96%,
			#0c7c6a 100%
		)
		0% 0% no-repeat padding-box;
	/* display: flex;
	justify-content: center;
	align-items: center; */
}
.login__main a {
	color: rgb(66, 66, 66) !important;
}
.login__main a:hover {
	color: rgb(215, 178, 150) !important;
}
.login_container {
	display: flex;
	flex-direction: column;
}
.login_wrapper {
	display: flex;
	justify-content: space-around;
	align-items: center;
}
.login_wrapper img {
	width: 16px;
	object-fit: contain;
	margin-right: 10px;
}
.login_wrapper div:nth-of-type(2) img {
	width: 8px;
}
.login_wrapper div {
	display: flex;
	align-items: center;
}
.form__control {
	/* padding: 0 30px; */
	margin-top: 30px;
}

.ant-radio-wrapper .ant-radio-inner,
.ant-radio-checked .ant-radio-inner,
.ant-radio-checked::after {
	color: #2b2f32 !important;
}
.footer {
	width: 100%;
	padding: 25px;
	background-color: #333;
	color: #fefefe;
	position: fixed;
	bottom: 0;
	text-align: center;
}
.footer p {
	margin: 0;
}
.footer img {
	width: 120px;
	object-fit: cover;
	margin: 0;
	padding: 0;
}
@media (max-width: 470px) {
	.login_wrapper {
		flex-direction: column;
		align-items: flex-start;
	}
	.login_wrapper div {
		padding: 10px;
		align-self: flex-start;
	}
}
