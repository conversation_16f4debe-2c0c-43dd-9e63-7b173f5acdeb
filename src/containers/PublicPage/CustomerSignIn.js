import { <PERSON><PERSON>, Card, Col, Form, Input, message, Row } from "antd";
import React, { useState, useEffect } from "react";
import Api from "../../api/api-handler";
import footerLogo from "../../static/images/public/logo.png";
import classes from "./Login.module.css";


const API = new Api({});
const CustomerSignIn = ({ history, location, props }) => {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [isPassExist, setIsPassExist] = useState(false);
	const [loading, setLoading] = useState(false);
	const [emailChecked, setEmailChecked] = useState(false);


	const handleSubmit = (e) => {
		e.preventDefault();
		setLoading(true);
		const data = [];
		data["data"] = { email: email };
		API.post("api/general/customer/check_pass_validity", data)
			.then((response) => {
				const { sign_up_flag } = response.data;
				setIsPassExist(!sign_up_flag);
				setEmailChecked(true);
				setLoading(false);
				if (!isPassExist) {
					// message.success("You already have password set!");
				} else {
					message.success("Set password and login to continue!");
				}
			})
			.catch((err) => {
				// message.error("Please Enter valid email!");
				setLoading(false);
			});
	};
	const handleSubmitLogin = (e) => {
		e.preventDefault();
		setLoading(true);
		const data = [];
		data["data"] = { email: email, password: password };
		API.post("api/general/customer/sign_in", data)
			.then((response) => {
				window.localStorage.setItem("customerLoggedIn", "true");
				setLoading(false);
				// const userData = window.localStorage.getItem("customerLoggedIn");
				if (response.data.length === 0) {
					message.error("You have no job!");
				}
				history.push({
					pathname: `/customer-portal/customer-ship-detail`,
					state: { shipment: response.data },
				});
			})
			.catch((err, data) => {
				// message.error(err);
				setLoading(false);
			});
	};
	const handleSubmitCreatePassword = (e) => {
		e.preventDefault();
		setLoading(true);
		const data = [];
		data["data"] = { email: email, password: password };
		API.post("api/general/customer/set_password", data)
			.then((response) => {
				setLoading(false);
				setIsPassExist(true);
				setPassword("");
				message.success("Successfully Created Password. Login to proceed!");
			})
			.catch((err) => {
				// message.error(err.message);
				setLoading(false);
			});
	};

	const handleForgetPassword = () => {
		history.push("/customer-portal/reset_password");
	};

	useEffect(() => {
		const params = new URLSearchParams(location.search);
		const emailValue = params.get('email');
		if(emailValue !== null && emailValue !== "" && emailValue !== undefined){
			setEmail(emailValue)
		}
	}, []);

	return (
		<Row align='middle' className={classes.login__main}>
			<div
				style={{
					display: "flex",
					justifyContent: "center",
					alignItems: "center",
					padding: "10px",
				}}>
				<a href='https://moverinventory-cms.movegistics.com/'>
					<img src={footerLogo} alt='logo' />
				</a>
			</div>
			<Col
				span={24}
				xl={{ span: 8, offset: 8 }}
				lg={{ span: 10, offset: 7 }}
				md={{ span: 14, offset: 5 }}
				sm={{ span: 18, offset: 3 }}
				xs={{ span: 22, offset: 1 }}
				style={{
					boxShadow: "2px 5px 10px rgba(0, 0, 0, 0.25)",
					marginTop: "30px",
				}}>
				<Card
					title='Customer Portal Login'
					headStyle={{
						fontSize: "24px",
						textAlign: "center",
						margin: "10px 0",
						fontFamily: "Poppins",
					}}>
					<div className={classes.login_container}>
						<div className={classes.form__control}>
							<Col
								lg={{ span: 22, offset: 1 }}
								md={{ span: 20, offset: 2 }}
								sm={{ span: 18, offset: 3 }}
								xs={{ span: 24 }}>
								{!emailChecked ? (
									<Form layout='vertical' name='basic' onSubmit={handleSubmit}>
										<Form.Item
											label='Email'
											name='email'
											hasFeedback
											rules={
												[
													{
														type: "email",
														message: "The input is not valid E-mail!",
													},
													{
														required: true,
														message: "Please input your E-mail!",
													},
													{
														pattern: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
														message: "Please enter valid email address!",
													},
												]}>
											<Input value={email} onChange={(e) => setEmail(e.target.value)} />
										</Form.Item>

										<Form.Item style={{ textAlign: "center" }}>
											<Button
												// style={{ backgroundColor: "#333" }}
												type='primary'
												block
												htmlType='submit'
												size='large'
												loading={loading}>
												Submit
											</Button>
										</Form.Item>
									</Form>
								) : emailChecked && isPassExist ? (
									<Form layout='vertical' name='basic' onSubmit={handleSubmitLogin}>
										<Form.Item
											label='Email'
											name='Email'
											rules={[
												{
													required: true,
													message: "Please input your Email!",
												},
												{
													pattern: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
													message: "Please valid email address!",
												},
											]}>
											<Input value={email} onChange={(e) => setEmail(e.target.value)} />
										</Form.Item>

										<Form.Item
											style={{ marginBottom: "0" }}
											label='Password'
											name='password'
											rules={[
												{
													required: true,
													message: "Please input your password!",
												},
											]}>
											<Input.Password value={password} onChange={(e) => setPassword(e.target.value)} />
										</Form.Item>

										<Row style={{ marginBottom: "25px" }}>
											<Col span={24} style={{ textAlign: "end" }}>
												<span style={{ cursor: "pointer" }} onClick={handleForgetPassword}>
													forgot password?
												</span>
											</Col>
										</Row>

										<Form.Item style={{ textAlign: "center" }}>
											<Button
												// style={{ backgroundColor: "#333" }}
												type='primary'
												block
												htmlType='submit'
												size='large'
												loading={loading}>
												Submit
											</Button>
										</Form.Item>
									</Form>
								) : (
									<Form layout='vertical' name='basic' onSubmit={handleSubmitCreatePassword}>
										<Form.Item
											label='Email'
											name='Email'
											rules={[
												{
													required: true,
													message: "Please input your Email!",
												},
												{
													pattern: /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/,
													message: "Please valid email address!",
												},
											]}>
											<Input value={email} onChange={(e) => setEmail(e.target.value)} />
										</Form.Item>

										<Form.Item
											label='Create Password'
											name='Create password'
											rules={[
												{
													required: true,
													message: "Please input your password!",
												},
											]}>
											<Input.Password value={password} onChange={(e) => setPassword(e.target.value)} />
										</Form.Item>

										<Form.Item style={{ textAlign: "center" }}>
											<Button
												// style={{ backgroundColor: "#333" }}
												type='primary'
												block
												htmlType='submit'
												size='large'
												loading={loading}>
												Submit
											</Button>
										</Form.Item>
									</Form>
								)}
							</Col>
						</div>
					</div>
				</Card>
			</Col>
			<footer className={classes.footer}>
				<p>Copyright &copy; 2021. All rights reserved</p>
				<a href='https://moverinventory-cms.movegistics.com/'>
					<img src={footerLogo} alt='logo' />
				</a>
			</footer>
		</Row>
	);
};

export default Form.create()(CustomerSignIn);
