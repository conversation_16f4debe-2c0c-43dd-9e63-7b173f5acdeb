import styled from "styled-components";

export const Container = styled.div`
	.header {
		padding: 10px 5% 10px 5%;
		display: flex;
		background: transparent
			linear-gradient(
				180deg,
				#60930c 0%,
				#5d920f 4%,
				#589115 10%,
				#26834c 69%,
				#1f8154 77%,
				#187f5c 86%,
				#137e62 92%,
				#107d66 96%,
				#0c7c6a 100%
			)
			0% 0% no-repeat padding-box;
		box-shadow: 0px 3px 6px #00000029;
		opacity: 1;
		/* height: 100px; */

		.main-logo {
			/* width: 243px;
			height: 73px;
			opacity: 1;
			margin-top: 10px; */
			/* padding: 10px; */
		}

		.contact {
			text-align: right;
			margin-top: 25px;
			text-align: right;
			font: normal normal medium 21px/24px Helvetica Neue;
			letter-spacing: 0px;
			color: #ffffff;
			opacity: 1;
			font-size: 16px;
		}
	}
	.inner-header-container {
		margin: 2% 5% 2% 5%;
		h1 {
			margin-bottom: 0.8em;
		}
		.phone-icon {
			color: #8ab932;
			margin-right: 10px;
		}
		.mail-icon {
			color: #8ab932;
			margin-right: 10px;
		}
		.date {
			text-align: end;
			color: #ccc;
		}
		.ship-label {
			color: #393939;
		}
		.text-label {
			color: "#333";
		}
	}

	.ship-img {
		height: 60px;
		margin: -7px;
	}
	.ship-img::after {
		content: " ";
		display: block;
		position: absolute;
		height: 3px;
		background: #47a547;
		width: 101px;
		left: 100%;
		top: calc(50% - 3px);
	}
	.img-right-border {
		border-top: 1px solid #ccc;
		display: block;
		margin-top: 22px;
		width: 50px;
	}
	.absolute-dispatch {
		position: absolute;
		color: #0c7c6a;
		margin: 15px 0px;
		font-size: 13px;
	}
	.home-img {
		height: 48px;
		margin: 0px;
	}
	.active-image {
		height: 58px;
		margin-top: -5px;
	}
	.main-ship-icons {
		display: flex;
		padding-left: 20px;
	}
	.top-border {
		border-top: 1px solid #ccc;
		margin-top: 60px;
	}
	.job-container {
		margin: 2% 5% 2% 5%;
		border: 1px solid #ccc;
		border-radius: 10px;
	}
	.job-delivery {
		padding: 3% 3% 0px 3%;
		.job-header {
			padding-left: 20px;
		}
	}
	.job-status {
		padding: 0px 3% 2% 3%;

		.pickup {
			padding-left: 20px;
			.title {
				font-weight: 600;
				font-size: 15px;
			}
		}
		.pick-up-line {
			display: flex;
			padding-top: 20px;
			padding-bottom: 20px;
		}

		.pickup-header {
			margin-top: 30px;
		}
		.address {
			display: flex;
			.address-1 {
				width: 44%;
			}
			.address-2 {
				padding-left: 17px;
			}
			p {
				margin-bottom: 5px;
			}
		}
		.border-span {
			width: 100%;
			border-top: 1px dashed #ccc;
			margin-top: 10px;
		}
		.display-flex {
			display: flex;
		}
		.dot-h1 {
			color: green;
			font-size: 30px;
			font-weight: bold;
			margin-top: -18px;
			padding-left: 3px;
			padding-right: 3px;
		}
		.dot-h2 {
			color: #8ab932;
			font-size: 30px;
			font-weight: bold;
			margin-top: -18px;
			padding-left: 3px;
			padding-right: 3px;
		}
		.pick-up-first {
			width: 44%;
		}
	}

	.moving-container {
		margin-left: 5%;
		margin-right: 5%;
		border: 1px solid #ccc;
		padding: 3%;
		border-radius: 10px;
	}
	.moving-header {
		padding-left: 20px;
	}
	.total-count {
		padding-left: 20px;
		padding-bottom: 20px;
	}
	.moving-sort-button-container {
		padding-left: 5px;
		button {
			margin-left: 15px;
		}
	}
	.signature-container {
		margin: 2% 5%;
		border: 1px solid #ccc;
		padding: 3%;
		border-radius: 10px;
	}
	.signature-header {
		padding-left: 20px;
	}
	.main-images {
		padding: 20px !important;
		.small-img {
			height: 130px;
			width: 100%;
		}
		.inner-item {
			border: 1px solid #ccc;
			padding: 5px 10px 5px 10px;
			border-radius: 10px;
			position: relative;
		}
		.fit {
			margin-bottom: 8px;
			text-align: center;
			font-size: 13px;
			font-weight: 600;
		}
		.box-p {
			margin-top: 12px;
			margin-bottom: 0px;
			text-align: center;
			font-size: 13px;
			font-weight: 600;
		}
		.one-more {
			margin-bottom: 0px;
			text-align: center;
			color: #8ab932;
			font-size: 13px;
		}
		.image-container {
			position: relative;
		}
		.extra-image {
			background-color: #1f6ed4;
			padding: 5px 8px;
			border-radius: 50%;
			color: #ffffff;
			font-weight: bold;
			position: absolute;
			bottom: 0;
			right: 0;
		}
		.exception {
			background-color: #8ab932;
			padding: 5px 12px;
			border-radius: 50%;
			color: #ffffff;
			font-weight: bold;
			position: absolute;
			top: 0;
			right: 0;
		}
		.override {
			background-color: #de4307;
			padding: 5px 11px;
			border-radius: 50%;
			color: #ffffff;
			font-weight: bold;
			position: absolute;
			top: 0;
			left: 0;
		}
	}
	.sort-dropdown {
		margin: 20px;
		margin-left: 15px;
		padding: 5px;
		button {
			background: white;
			border: 1px solid #ccc;
			padding: 5px 10px;
			border-radius: 5px;
			font-size: 14px;
		}
		span {
			color: #0c7c6a;
			font-weight: 500;
			font-size: 14px;
		}
		i {
			color: #ccc;
			padding-left: 5px;
		}
	}
	.active-btn {
		background: #13806e !important;
		color: white !important;
		border-color: #13806e !important;
		box-shadow: none !important;
	}
	.normal-btn {
		background: white;
		color: black !important;
		border-color: #ccc !important;
		box-shadow: none !important;
	}
`;

export const SortContainer = styled.div`
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	.sort-dropdown {
		margin: 20px;
		margin-left: 15px;
		padding: 5px;
		button {
			background: white;
			border: 1px solid #ccc;
			padding: 5px 10px;
			border-radius: 5px;
			font-size: 14px;
		}
		span {
			color: #0c7c6a;
			font-weight: 500;
			font-size: 14px;
		}
		i {
			color: #ccc;
			padding-left: 5px;
		}
	}
	.moving-sort-button-container {
		padding-left: 5px;
		button {
			margin-left: 15px;
		}
	}
	.active-btn {
		background: #13806e !important;
		color: white !important;
		border-color: #13806e !important;
		box-shadow: none !important;
		border-radius: 5px;
	}
	.normal-btn {
		background: white;
		color: black !important;
		border-color: #ccc !important;
		box-shadow: none !important;
		border-radius: 5px;
	}
`;
export const Signature = styled.div`
	.signature_history_wrapper {
		/* display: flex;
						justify-content: space-between; */
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		align-items: center;
		text-align: center;
	}
	.no_signature {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 25px;
		color: #424242;
		font-size: 24px;
		font-weight: 300;
	}
	.signature_history {
		/* display: flex;
		justify-content: space-between;
		align-items: center; */
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		align-items: center;
		justify-items: center;
		margin-top: 25px;

		& p:nth-of-type(1) {
			margin: 0;
		}
	}
`;
