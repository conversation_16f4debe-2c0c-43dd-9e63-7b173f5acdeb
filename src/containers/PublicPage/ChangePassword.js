import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Col, Form, Input, Row } from "antd";
import crypto from "crypto-js";
import React, { useState } from "react";
import Api from "../../api/api-handler";
import footerLogo from "../../static/images/public/logo.png";
import classes from "./Login.module.css";

const API = new Api({});
const ChangePassword = ({ match, history }) => {

	// const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [alertOpen, setAlertOpen] = useState(false);
	const [alertMsg, setAlertMsg] = useState("");
	const [loading, setLoading] = useState(false);
	const param =
		match.params.id2 && match.params.id3
			? match.params.id + "/" + match.params.id2 + "/" + match.params.id3
			: match.params.id2
			? match.params.id + "/" + match.params.id2
			: match.params.id;
	const bytes = crypto.AES.decrypt(param, "Openxcell");
	const originalText = bytes.toString(crypto.enc.Utf8);

	const handleSubmit = (e) => {
		e.preventDefault();
		setAlertOpen(false);
		setLoading(true);
		const data = [];
		data["data"] = { email: originalText, password: password };
		API.post("api/general/customer/forgot_password", data)
			.then((response) => {
				setLoading(false);
				setAlertOpen(true);
				setAlertMsg(response.message);
				setPassword("");
				setTimeout(() => {
					history.push("/customer-portal/sign_in");
				}, 5000);
			})
			.catch((err) => {
				setLoading(false);
			});
	};

	return (
		<Row align='middle' className={classes.login__main}>
			<div
				style={{
					display: "flex",
					justifyContent: "center",
					alignItems: "center",
					padding: "10px",
				}}>
				<a href='https://moverinventory-cms.movegistics.com/'>
					<img src={footerLogo} alt='logo' />
				</a>
			</div>
			<Col
				span={24}
				xl={{ span: 8, offset: 8 }}
				lg={{ span: 10, offset: 7 }}
				md={{ span: 14, offset: 5 }}
				sm={{ span: 18, offset: 3 }}
				xs={{ span: 22, offset: 1 }}
				style={{
					boxShadow: "2px 5px 10px rgba(0, 0, 0, 0.25)",
					marginTop: "30px",
				}}>
				<Card
					title='Change Password'
					headStyle={{
						fontSize: "24px",
						textAlign: "center",
						margin: "10px 0",
						fontFamily: "Poppins",
					}}>
					<div className={classes.login_container}>
						<div className={classes.form__control}>
							<Col
								lg={{ span: 22, offset: 1 }}
								md={{ span: 20, offset: 2 }}
								sm={{ span: 18, offset: 3 }}
								xs={{ span: 24 }}>
								<Form layout='vertical' name='basic' onSubmit={handleSubmit}>
									<Form.Item label='Password' name='password' hasFeedback>
										<Input
											value={password}
											onChange={(e) => {
												setPassword(e.target.value);
												setAlertOpen(false);
												setAlertMsg("");
											}}
										/>
									</Form.Item>

									<Form.Item style={{ textAlign: "center" }}>
										<Button
											// style={{ backgroundColor: "#333" }}
											type='primary'
											block
											htmlType='submit'
											size='large'
											loading={loading}>
											Submit
										</Button>
									</Form.Item>
								</Form>
								{alertOpen ? (
									<Alert
										message='Successfully Changed!'
										description={alertMsg}
										type='success'
										showIcon
										closable
										style={{ marginTop: "15px" }}
									/>
								) : (
									""
								)}
							</Col>
						</div>
					</div>
				</Card>
			</Col>
			<footer className={classes.footer}>
				<p>Copyright &copy; 2021. All rights reserved</p>
				<a href='https://moverinventory-cms.movegistics.com/'>
					<img src={footerLogo} alt='logo' />
				</a>
			</footer>
		</Row>
	);
};

export default Form.create()(ChangePassword);
