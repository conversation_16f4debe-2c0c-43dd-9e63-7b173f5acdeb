import {
  <PERSON><PERSON>,
  Col,
  Icon,
  Row,
  Input,
  Select,
  Spin,
  Pagination,
  message,
  Popover,
  Menu,
  Dropdown,
  Tabs,
  Tag,
} from "antd";
import moment from "moment";
import React from "react";
import NumberFormat from "react-number-format";
import Api from "../../api/api-handler";
import Pdf from "../../components/Pdf/Pdf";
import smallImage from "../../image/placeholder_image.png";
import logo from "../../static/images/public/logo.png";
import firstShip from "../../static/images/public/ship-1.png";
import secondShip from "../../static/images/public/ship-2.png";
import thirdShip from "../../static/images/public/ship-3.png";
import forthShip from "../../static/images/public/ship-4.png";
import { Container, Signature, SortContainer } from "./Customer.style";
import { SortMenu } from "../../components/job/viewJob.style";
import "../../static/css/add.css";
import { saveAs } from "file-saver";

const API = new Api({});
const { Option } = Select;
const Search = Input.Search;
const antIcon = <Icon type="loading" style={{ fontSize: 24 }} spin />;
const pageSize = 100;
const { TabPane } = Tabs;

class PublicPage extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      items: [],
      customer: {},
      images: [firstShip, secondShip, thirdShip, forthShip],
      visible: false,
      mainItems: [],
      companyId: null,
      companyData: null,
      roomList: [],
      shipments: [],
      inventoryExceptionData: {},
      orderingField:
        localStorage.getItem("orderingField") &&
        localStorage.getItem("orderingField") !== "" &&
        localStorage.getItem("ShipmentIdFilter") === this.props.match.params.id
          ? localStorage.getItem("orderingField")
          : "qr_id",
      orderingWay:
        localStorage.getItem("orderingWay") &&
        localStorage.getItem("orderingWay") !== "" &&
        localStorage.getItem("ShipmentIdFilter") === this.props.match.params.id
          ? localStorage.getItem("orderingWay")
          : "ASC",
      sort_field:
        localStorage.getItem("sort_field") &&
        localStorage.getItem("sort_field") !== "" &&
        localStorage.getItem("ShipmentIdFilter") === this.props.match.params.id
          ? localStorage.getItem("sort_field")
          : "Label number",
      active: 1,
      loading: true,
      data: [],
      totalPage: 0,
      totalCount: 0,
      current: 1,
      minIndex: 0,
      maxIndex: 0,
      okShipment: this.props.location.state.shipment[0].shipment_job_id,
      isPickListCheck: false,
      jobItemTagList: [],
      jobItemRoomList: [],
      tagsSearchList: [],
      roomsSearchList: [],
      defaultTabActiveKey: 1,
      search: null,
      shipmentStagesTab: [],
      currentShipmentStageName: "default_stage",
      currentShipmentStageId: null,
    };
  }
  componentDidMount() {
    if (
      this.props &&
      this.props.location &&
      this.props.location.state &&
      this.props.location.state.shipment &&
      this.props.location.state.shipment.length
    ) {
      this.fetchCustomerDetails(
        this.props.location.state.shipment[0].shipment_job_id
      );
      this.fetchItemListDetails(
        this.props.location.state.shipment[0].shipment_job_id
      );
      this.fetchJobItemTagList(
        this.props.location.state.shipment[0].shipment_job_id
      );
      this.fetchJobItemRoomList(
        this.props.location.state.shipment[0].shipment_job_id
      );
      this.setState({ shipments: this.props.location.state.shipment });
    } else {
      this.props.history.push("/customer-porta/sign_in");
    }
  }

  fetchCustomerDetails = (id) => {
    this.setState({ loading: true });
    // localStorage.setItem("lastSelected", id);
    // let cid = localStorage.getItem("lastSelected");
    let cid = id;
    let tempRoom = [];
    API.get(
      "api/admin/customer/" +
        cid +
        `/job?orderingField=${this.state.orderingField}`
    )
      .then((response) => {
        this.setState({ loading: false });
        localStorage.setItem("ShipmentIdFilter", id);
        if (response) {
          if (response.data) {
            const defaultStage = {
              local_shipment_stage_id: -1,
              name: "Show All Items",
              status: "active",
              order_of_stages: 0,
              local_shipment_type_id: 0,
              ref_shipment_stage_id: 0,
              shipment_job_id: 0,
              is_default: true,
            };
            const check =
              response &&
              response.data.shipment_type_for_shipment &&
              response.data.shipment_type_for_shipment.local_shipment_stage;
            const shipmentStagesTab = check
              ? [defaultStage, ...check]
              : [defaultStage];
            this.setState({
              items: response.data.job_items,
              customer: response.data,
              mainItems: response.data.job_items,
              companyData: response.data.job_company,
              isPickListCheck: false,
              shipmentStagesTab,
            });
            check.forEach((newData) => {
              if (newData.assign_storage_units_to_items === 1) {
                this.setState({
                  isPickListCheck: true,
                });
              }
            });
            tempRoom = response.data.job_items;
          }
        }
        // const { company_id } = this.state.customer.customer_job;
        // this.setState({ companyId: company_id });
        // const data = [];
        // data["data"] = { company_id: this.state.companyId };
        // API.post("api/admin/company/view-company", data)
        // 	.then((response) => {
        // 		if (response) {
        // 			this.setState({
        // 				companyData: response.data && response.data,
        // 			});
        // 		} else {
        // 			message.error(response.message);
        // 		}
        // 	})
        // 	.catch((error) => {
        // 		message.error(error.message);
        // 		//console.log("error:", error);
        // 	});
        //console.log(this.state.items);
        // API.get(`api/admin/inventory/${id}`)
        // 	.then((response) => {
        // 		if (response) {
        // 			//console.log(
        // 				"ViewInventory -> componentDidMount -> response",
        // 				response
        // 			);

        // 			this.setState({
        // 				inventoryExceptionData: response.data && response.data.exceptions,
        // 			});
        // 		} else {
        // 			message.error(response.message);
        // 		}
        // 	})
        // 	.catch((error) => {
        // 		//console.log("error:", error);
        // 	});

        // API.get("api/admin/customer_room/list").then((innerResponse) => {
        // 	console.log("Working");
        // 	console.log(innerResponse)
        // 	if (innerResponse) {
        // 		if (innerResponse.data) {
        // 			let rooms = [];
        // 			innerResponse.data.rows.forEach((element) => {
        // 				tempRoom.forEach((innerElement) => {
        // 					if (element.shipment_room_id === innerElement.room_id) {
        // 						let isExistInList = rooms.filter(
        // 							(item) => item.shipment_room_id === element.shipment_room_id
        // 						);
        // 						if (isExistInList && isExistInList.length === 0) {
        // 							rooms.push(element);
        // 						}
        // 					}
        // 				});
        // 			});
        // 			this.setState({
        // 				roomList: rooms,
        // 			});
        // 		}
        // 	}
        // });
      })
      .catch(() => this.setState({ loading: false }));
  };

  fetchJobItemTagList = (id) => {
    this.setState({ contentloader: true });
    let params = {
      shipmentId: id,
    };
    const data = [];
    data["data"] = params;
    API.post(`api/home/<USER>
      .then((response) => {
        if (response) {
          this.setState({
            jobItemTagList: response.data,
            contentloader: false,
          });
        } else {
          message.error(response.message);
          this.setState({ contentloader: false });
        }
      })
      .catch((error) => {
        this.setState({ contentloader: false });
      });
  };
  fetchJobItemRoomList = (id) => {
    this.setState({ contentloader: true });
    let params = {
      shipmentId: id,
    };
    const data = [];
    data["data"] = params;
    API.post(`api/home/<USER>
      .then((response) => {
        if (response) {
          this.setState({
            jobItemRoomList: response.data,
            contentloader: false,
          });
        } else {
          message.error(response.message);
          this.setState({ contentloader: false });
        }
      })
      .catch((error) => {
        this.setState({ contentloader: false });
      });
  };

  fetchItemListDetails = (id) => {
    this.setState({
      contentloader: true,
      contentloaderInventory: true,
      imageItemLoading: true,
    });
    let params = {
      tagIds: this.state.tagsSearchList,
      roomIds: this.state.roomsSearchList,
      orderingField: this.state.orderingField,
      orderingWay: this.state.orderingWay,
      page_size: pageSize,
      page_no: this.state.current,
      shipmentId: id ? id : this.state.customer && this.state.customer.job_id,
      search: this.state.search,
      currentShipmentStageId:
        this.state.currentShipmentStageName === "default_stage"
          ? null
          : this.state.currentShipmentStageId,
      currentShipmentStageName:
        this.state.currentShipmentStageName === "default_stage"
          ? null
          : this.state.currentShipmentStageName,
    };
    const data = [];
    data["data"] = params;
    API.post(`api/home/<USER>/cms-web`, data)
      .then((response) => {
        if (response) {
          this.setState({
            loading: false,
            data: response.data.rows,
            totalCount: response.data.count,
            customer: {
              ...this.state.customer,
              itemData: response.data.rows,
            },
          });
          this.setState({ contentloader: false, imageItemLoading: false });
        } else {
          message.error(response.message);
          this.setState({
            contentloader: false,
            imageItemLoading: false,
            loading: false,
          });
        }
      })
      .catch((error) => {
        this.setState({ contentloader: false, imageItemLoading: false });
      });
  };

  handleChangeJobItemTagList = (item) => {
    this.setState(
      {
        tagsSearchList: item.map((item) => item.key),
      },
      () => {
        this.fetchItemListDetails();
        this.hide();
      }
    );
  };

  handleChangeJobItemRoomList = (item) => {
    this.setState(
      {
        roomsSearchList: item.map((item) => item.key),
      },
      () => {
        this.fetchItemListDetails();
        this.hide();
      }
    );
  };

  onNewPdf = async () => {
    this.setState({ loading: true });
    let params = {
      shipmentId: this.state.okShipment,
      isPrintPhotos: 0,
      isDownloadByCustomer: 1,
    };
    const data = [];
    data["data"] = params;
    API.post(`api/admin/shipment/pdf/customer`, data)
      .then((response) => {
        const { job_number, shipment_name } = this.state.customer;
        if (response) {
          const data = Uint8Array.from(response.data.data);
          const content = new Blob([data.buffer]);
          saveAs(content, `${job_number}-${shipment_name}.pdf`);
          this.setState({ loading: false });
          message.success("Pdf Downloaded!");
        } else {
          message.error(response.message);
          this.setState({ loading: false });
        }
      })
      .catch((error) => {
        this.setState({ contentloader: false, imageItemLoading: false });
      });
  };

  onNewPdfWithPhotos = async () => {
    this.setState({ loading: true });
    let params = {
      shipmentId: this.state.okShipment,
      isPrintPhotos: 1,
      isDownloadByCustomer: 1,
    };
    const data = [];
    data["data"] = params;
    API.post(`api/admin/shipment/pdf/customer`, data)
      .then((response) => {
        const { job_number, shipment_name } = this.state.customer;
        if (response) {
          const data = Uint8Array.from(response.data.data);
          const content = new Blob([data.buffer]);
          saveAs(content, `${job_number}-${shipment_name}.pdf`);
          this.setState({ loading: false });
          message.success("Pdf Downloaded!");
        } else {
          message.error(response.message);
          this.setState({ loading: false });
        }
      })
      .catch((error) => {
        this.setState({ contentloader: false, imageItemLoading: false });
      });
  };

  handleChangePagination = (page) => {
    this.setState(
      {
        current: page,
        minIndex: (page - 1) * pageSize,
        maxIndex: page * pageSize,
      },
      () => {
        this.fetchItemListDetails(
          this.state && this.state.customer && this.state.customer.job_id
        );
        this.hide();
      }
    );
  };

  handleChange = async (id) => {
    this.setState({
      orderingField: "qr_id",
      orderingWay: "ASC",
      sort_field: "Label number",
      okShipment: id,
    });
    await this.setState({
      current: 1,
    });
    this.fetchCustomerDetails(id);
    this.fetchItemListDetails(id);
    this.fetchJobItemRoomList(id);
    this.fetchJobItemTagList(id);
  };

  handleVisibleChange = (visible) => {
    this.setState({ visible });
  };
  hide = () => {
    this.setState({
      visible: false,
    });
  };
  allClickAPI = () => {
    this.setState({
      items: this.state.mainItems,
      active: 1,
    });
  };

  sortClickAPI = (title, field, direction) => {
    localStorage.setItem("orderingField", field);
    localStorage.setItem("orderingWay", direction);
    localStorage.setItem("sort_field", title);

    this.setState(
      {
        orderingField: field,
        orderingWay: direction,
        sort_field: title,
        imageItemLoading: true,
      },
      () => {
        this.fetchCustomerDetails(this.state.okShipment);
        this.fetchItemListDetails(this.state.okShipment);
        this.hide();
      }
    );
  };

  roomClikAPI = (active, e) => {
    this.setState({
      items: this.state.mainItems.filter(
        (data) => data.room_id === parseInt(e.target.value)
      ),
      active: active,
    });
  };

  viewInventory(id) {
    this.props.history.push({
      pathname: `/customer-portal/inventory/${id}`,
      state: {
        initialStage: this.state.customer,
      },
    });
  }
  toDate = (date) => {
    let s = new Date(date).toLocaleTimeString("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
    return s;
  };

  tabChangeHandler = (activeKey) => {
    const tabIndex = parseInt(activeKey);
    const selectedStage = this.state.shipmentStagesTab[tabIndex];
    if (selectedStage.is_default == true) {
      this.setState({
        currentShipmentStageName: "default_stage",
        currentShipmentStageId: null,
      });
    } else if (selectedStage.add_items_to_inventory) {
      this.setState({
        currentShipmentStageName: "add_items_to_inventory",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.assign_storage_units_to_items) {
      this.setState({
        currentShipmentStageName: "add_items_to_storage",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.unassign_storage_units_from_items) {
      this.setState({
        currentShipmentStageName: "remove_items_from_storage",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.remove_items_to_inventory) {
      this.setState({
        currentShipmentStageName: "remove_items_from_inventory",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.remove_scan_require) {
      this.setState({
        currentShipmentStageName: "remove_scan_require",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else if (selectedStage.scan_require) {
      this.setState({
        currentShipmentStageName: "additional_scan_require",
        currentShipmentStageId: selectedStage.local_shipment_stage_id,
      });
    } else {
      this.setState({
        currentShipmentStageName: "default_stage",
        currentShipmentStageId: null,
      });
    }
    this.setState(
      {
        activeTabKey: activeKey,
        current: 1,
      },
      () => {
        this.fetchItemListDetails();
        this.hide();
      }
    );
  };

  handleSearch = (e) => {
    const searchValue = e;
    this.setState({ search: searchValue, current: 1 }, () => {
      this.fetchItemListDetails();
      this.hide();
    });
  };

  render() {
    let {
      customer,
      images,
      active,
      roomList,
      companyData,
      shipments,
      shipmentStagesTab,
    } = this.state;
    const sortArray = this.state.data;
    const newArray = sortArray.sort(function(a, b) {
      const nameA = a.type.toUpperCase(); // ignore upper and lowercase
      const nameB = b.type.toUpperCase(); // ignore upper and lowercase
      if (nameA > nameB) {
        return -1;
      }
      if (nameA < nameB) {
        return 1;
      }
      // names must be equal
      return 0;
    });

    let firstShipment = shipments && shipments[0] && shipments[0];

    const content = (
      <SortMenu>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Label number", "qr_id", "ASC")}
        >
          Label Number: Low to high
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Label number",
            "qr_id",
            "DESC"
          )}
        >
          Label Number: high to Low
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Scanned Items",
            "isScannedFlag",
            "DESC"
          )}
        >
          Scanned Items
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Unscanned Items",
            "isScannedFlag",
            "ASC"
          )}
        >
          Unscanned Items
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Volume", "volume", "DESC")}
        >
          Volume: High to low
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Volume", "volume", "ASC")}
        >
          Volume: Low to high
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Weight", "weight", "DESC")}
        >
          Weight: High to low
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(this, "Weight", "weight", "ASC")}
        >
          Weight: Low to high
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Alphabetical",
            "item_name",
            "ASC"
          )}
        >
          Alphabetical : A to Z
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Alphabetical",
            "item_name",
            "DESC"
          )}
        >
          Alphabetical : Z to A
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Order of Creation",
            "created_at",
            "ASC"
          )}
        >
          Order of Creation : First-Last
        </p>
        <p
          className="sort_title"
          onClick={this.sortClickAPI.bind(
            this,
            "Order of Creation",
            "created_at",
            "DESC"
          )}
        >
          Order of Creation : Last-First
        </p>
        <p>
          <button style={{ color: "black" }} onClick={this.hide}>
            Close
          </button>
        </p>
      </SortMenu>
    );

    const getOverlayOpacity = (data) => {
      console.log(
        "this.state.currentShipmentStageName",
        this.state.currentShipmentStageName
      );
      if (this.state.currentShipmentStageName === "default_stage") {
        if (data.is_item_scanned_remove_from_storage) {
          return 1;
        }
        return 0;
      }
      if (this.state.currentShipmentStageName === "add_items_to_inventory") {
        if (data.isManualLabel === true || data.isManualLabel === "true") {
          return 1;
        }
        return 0;
      }
      if (this.state.currentShipmentStageName === "additional_scan_require") {
        return 0;
      }
      if (this.state.currentShipmentStageName === "add_items_to_storage") {
        return 0;
      }
      if (this.state.currentShipmentStageName === "remove_items_from_storage") {
        return 0;
      }
      if (this.state.currentShipmentStageName === "remove_scan_require") {
        return 0;
      } else if (data.is_item_scanned_remove_from_storage) {
        return 0;
      }
      return 0;
    };

    const menu = (
      <Menu>
        <Menu.Item key="1">
          <Button type="primary" icon="printer" onClick={() => this.onNewPdf()}>
            Print
          </Button>
        </Menu.Item>
        <Menu.Item key="1">
          <Button
            type="primary"
            icon="printer"
            onClick={() => this.onNewPdfWithPhotos()}
          >
            Print PDF With Photos
          </Button>
        </Menu.Item>
      </Menu>
    );
    return (
      <Container>
        <Spin spinning={this.state.loading} indicator={antIcon}>
          <Row className="header">
            <Col span={24} style={{ textAlign: "center" }}>
              <img src={logo} alt=".." className="main-logo" />
            </Col>
          </Row>
          <Row className="inner-header-container">
            <Row>
              <Col
                span={12}
                style={{
                  display: "flex",
                  alignItems: "flex-start",
                }}
              >
                {companyData && companyData.photo ? (
                  <img
                    alt="company_logo"
                    width="40px"
                    style={{ marginRight: "10px" }}
                    src={`https://openxcell-development-public.s3.ap-south-1.amazonaws.com/mover-inventory/companyProfile/original/${companyData.photo}`}
                  />
                ) : (
                  <i
                    style={{ fontSize: "40px", marginRight: "10px" }}
                    class="fas fa-user-circle fa"
                  ></i>
                )}
                <h1 style={{ textTransform: "capitalize" }}>
                  {companyData ? companyData.company_name : ""}
                </h1>
              </Col>
              <Col span={12}>
                {shipments && shipments.length > 1 ? (
                  <Select
                    labelInValue
                    style={{ width: "100%" }}
                    placeholder="Please select Shipment id"
                    onChange={(e) => this.handleChange(e.key)}
                    defaultValue={{
                      label:
                        firstShipment.shipment_name +
                        ` (${firstShipment.job_number})`,
                      value: firstShipment.shipment_job_id,
                    }}
                  >
                    {shipments.map((item) => (
                      <Option
                        value={item.shipment_job_id}
                        key={item.shipment_job_id}
                      >
                        {item.shipment_name} ({item.job_number})
                      </Option>
                    ))}
                  </Select>
                ) : (
                  ""
                )}
              </Col>
            </Row>
            <Row>
              <Col span={6} style={{ display: "flex", alignItems: "baseline" }}>
                <i class="fas fa-home phone-icon" aria-hidden="true"></i>
                <div>
                  <div>
                    <strong className="text-label">
                      {companyData &&
                      companyData.address1 !== "null" &&
                      companyData.address1 !== "undefined" &&
                      companyData.address1 !== ""
                        ? companyData.address1
                        : ""}
                      &nbsp;
                      {companyData &&
                      companyData.address2 !== "null" &&
                      companyData.address2 !== "undefined" &&
                      companyData.address2 !== ""
                        ? companyData.address2
                        : ""}
                    </strong>
                  </div>
                  <div>
                    <strong className="text-label">
                      {companyData &&
                      companyData.city !== "null" &&
                      companyData.city !== "undefined" &&
                      companyData.city !== ""
                        ? companyData.city
                        : ""}
                      &nbsp;
                      {companyData &&
                      companyData.state !== "null" &&
                      companyData.state !== "undefined" &&
                      companyData.state !== ""
                        ? companyData.state
                        : ""}
                      &nbsp;
                      {companyData &&
                      companyData.zipCode !== "null" &&
                      companyData.zipCode !== "undefined" &&
                      companyData.zipCode !== ""
                        ? ",  " + companyData.zipCode
                        : ""}
                    </strong>
                  </div>
                  <div>
                    <strong className="text-label">
                      {companyData &&
                      companyData.country !== "null" &&
                      companyData.country !== "undefined" &&
                      companyData.country !== ""
                        ? companyData.country
                        : ""}
                    </strong>
                  </div>
                </div>
              </Col>
              <Col span={5}>
                <i className="fa fa-phone phone-icon" aria-hidden="true"></i>
                <strong className="text-label">
                  <NumberFormat
                    value={
                      companyData && companyData.phone ? companyData.phone : "-"
                    }
                    displayType={"text"}
                    format="(###) ###-####"
                  />
                </strong>
              </Col>
              <Col span={8}>
                <i class="fa fa-envelope mail-icon" aria-hidden="true"></i>

                <strong className="text-label">
                  {companyData && companyData.email ? companyData.email : ""}
                </strong>
              </Col>
              <Col span={5}>
                <i class="fas fa-shipping-fast mail-icon"></i>
                <strong className="text-label">#{customer.job_number}</strong>
              </Col>
            </Row>
          </Row>

          <div className="job-container">
            <div className="job-delivery">
              <h1 className="job-header">Job Status</h1>
              <div className="main-ship-icons">
                {customer.shipment_type_for_shipment ? (
                  customer.shipment_type_for_shipment.local_shipment_stage
                    .length ? (
                    customer.shipment_type_for_shipment.local_shipment_stage.map(
                      (item, index) => (
                        <>
                          <div>
                            {customer.local_shipment_job_status ? (
                              customer.local_shipment_job_status
                                .local_shipment_stage_id ===
                              item.local_shipment_stage_id ? (
                                <img
                                  src={images[1]}
                                  alt="..."
                                  className="ship-img home-img active-image"
                                />
                              ) : (
                                <img
                                  src={forthShip}
                                  alt="..."
                                  className="ship-img home-img"
                                />
                              )
                            ) : (
                              <img
                                src={forthShip}
                                alt="..."
                                className="ship-img home-img"
                              />
                            )}
                            {customer.local_shipment_job_status ? (
                              customer.local_job_status ===
                              item.local_shipment_stage_id ? (
                                <p className="absolute-dispatch">{item.name}</p>
                              ) : null
                            ) : null}
                          </div>
                          {customer.shipment_type_for_shipment
                            .local_shipment_stage.length -
                            1 !==
                          index ? (
                            <span className="img-right-border"></span>
                          ) : null}
                        </>
                      )
                    )
                  ) : (
                    <>
                      <div>
                        <img
                          src={forthShip}
                          alt="..."
                          className="ship-img home-img"
                        />
                      </div>
                      <span className="img-right-border"></span>
                    </>
                  )
                ) : (
                  <>
                    <div>
                      <img
                        src={forthShip}
                        alt="..."
                        className="ship-img home-img"
                      />
                    </div>
                    <span className="img-right-border"></span>
                  </>
                )}
              </div>
            </div>
            <div className="top-border"></div>

            <div className="job-status">
              <div className="pickup">
                <h1 className="pickup-header">Origin and Destination</h1>
                <div className="pick-up-line">
                  <div className="display-flex pick-up-first">
                    <span className="title">Origin</span>
                    <span className="dot-h1"> . </span>
                    <p className="border-span"></p>
                  </div>
                  <div className="display-flex">
                    <span className="dot-h2"> . </span>
                    <span className="title">Destination</span>
                  </div>
                </div>
                <div className="address">
                  <div className="address-1">
                    <p>
                      {customer && customer.pickup_date
                        ? moment(customer.pickup_date).format("MM/DD/YYYY")
                        : "To Be Estimated"}
                    </p>
                    <p>
                      {customer.pickup_address}&nbsp; {customer.pickup_address2}
                    </p>
                    <p>
                      {customer.pickup_city}&nbsp;
                      {customer.pickup_state}{" "}
                      {customer.pickup_zipcode
                        ? ",  " + customer.pickup_zipcode
                        : customer.pickup_zipcode}{" "}
                    </p>
                    <p>{customer.pickup_country}</p>
                  </div>
                  <div className="address-2">
                    <p>
                      {customer && customer.delivery_date
                        ? moment(customer.delivery_date).format("MM/DD/YYYY")
                        : "To Be Estimated"}
                    </p>
                    <p>
                      {customer.delivery_address}&nbsp;{" "}
                      {customer.delivery_address2}
                    </p>
                    <p>
                      {customer.delivery_city}&nbsp;
                      {customer.delivery_state}
                      {customer.delivery_zipcode
                        ? ",  " + customer.delivery_zipcode
                        : ""}
                    </p>
                    <p> {customer.delivery_country}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="moving-container">
            <div
              style={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "baseline",
              }}
            >
              <h1 className="moving-header">Moving Items</h1>

              <Dropdown overlay={menu}>
                <Button type="primary">
                  Print <Icon type="down" />
                </Button>
              </Dropdown>
            </div>
            <h3 className="total-count">
              ({customer.total_items} items total)
            </h3>
            <div className="moving-sort-button-container">
              <SortContainer>
                <div className="sort-dropdown">
                  <Popover
                    content={content}
                    title="Title"
                    trigger="click"
                    visible={this.state.visible}
                    onVisibleChange={this.handleVisibleChange}
                  >
                    <button>
                      Sort by : {this.state.sort_field}
                      <i className="fa fa-caret-down"></i>
                    </button>
                  </Popover>
                </div>
                <div
                  style={{
                    minWidth: "200px",
                    maxWidth: "400px",
                    marginTop: "5px",
                  }}
                >
                  <Select
                    getPopupContainer={(trigger) => trigger.parentNode}
                    mode="multiple"
                    labelInValue
                    allowClear
                    placeholder="Please select tags"
                    onChange={this.handleChangeJobItemTagList}
                    style={{
                      width: "100%",
                      maxHeight: "120px",
                      overflow: "auto",
                      border: "1px solid #d9d9d9",
                      scrollbarWidth: "thin",
                      scrollbarColor: "#888 transparent",
                      padding: "0px",
                      margin: "0px",
                      borderRadius: "5px",
                    }}
                  >
                    {this.state.jobItemTagList.map((item, index) => (
                      <Option value={item.tag_id} key={item.tag_id}>
                        <Tag color={item.color ? item.color : ""}>
                          {item.name}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                </div>
                <div
                  style={{
                    minWidth: "200px",
                    maxWidth: "400px",
                    marginLeft: "15px",
                    marginTop: "5px",
                  }}
                >
                  <Select
                    getPopupContainer={(trigger) => trigger.parentNode}
                    mode="multiple"
                    labelInValue
                    allowClear
                    style={{
                      width: "100%",
                      maxHeight: "120px",
                      overflow: "auto",
                      border: "1px solid #d9d9d9",
                      scrollbarWidth: "thin",
                      scrollbarColor: "#888 transparent",
                      padding: "0px",
                      margin: "0px",
                      borderRadius: "5px",
                    }}
                    placeholder="Please select rooms"
                    onChange={this.handleChangeJobItemRoomList}
                  >
                    {this.state.jobItemRoomList.map((item, index) => (
                      <Option value={item.room_id} key={item.room_id}>
                        {item.room_name}
                      </Option>
                    ))}
                  </Select>
                </div>
                <Button
                  className={active === 1 ? "active-btn" : "normal-btn"}
                  onClick={this.allClickAPI}
                >
                  All
                </Button>

                <div style={{ marginLeft: "15px" }}>
                  <Search
                    placeholder="Search Items"
                    onChange={(e) => this.handleSearch(e.target.value)}
                    value={this.state.search}
                    style={{ width: 200 }}
                  />
                </div>
              </SortContainer>

              {roomList.map((item, index) => (
                <Button
                  className={active === index + 2 ? "active-btn" : "normal-btn"}
                  onClick={this.roomClikAPI.bind(null, index + 2)}
                  value={item.shipment_room_id}
                >
                  {item.name}
                </Button>
              ))}
            </div>
            <Tabs
              defaultActiveKey={this.state.defaultTabActiveKey}
              onChange={this.tabChangeHandler}
            >
              {shipmentStagesTab.map((result, i) => (
                <TabPane tab={result.name} key={i}>
                  <Spin
                    spinning={this.state.imageItemLoading}
                    indicator={antIcon}
                  >
                    <Row>
                      {this.state && this.state.data && newArray.length > 0
                        ? newArray.map((data, index) => (
                            <Col
                              xl={4}
                              lg={6}
                              md={8}
                              sm={6}
                              className="main-images"
                              key={index}
                            >
                              <div
                                className="inner-item"
                                style={{ cursor: "pointer" }}
                                onClick={() =>
                                  this.viewInventory(data.inventory_id)
                                }
                              >
                                <div
                                  class="overlay"
                                  style={{
                                    position: "absolute",
                                    top: 0,
                                    left: 0,
                                    width: "100%",
                                    height: "100%",
                                    backgroundColor: "rgba(0, 0, 0, 0.5)",
                                    opacity: getOverlayOpacity(data),
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    zIndex: 100,
                                    transition: "opacity 0.3s ease",
                                  }}
                                >
                                  <Icon
                                    style={{
                                      color: "#ffffff",
                                      fontSize: "50px",
                                    }}
                                    type="check"
                                  />
                                </div>

                                <div
                                  style={{
                                    height: "30px",
                                    fontSize: "12px",
                                    textAlign: "center",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                  }}
                                >
                                  {data.item_name.length > 15
                                    ? `${data.item_name.substring(0, 15)}...`
                                    : data.item_name}
                                </div>
                                <div className="image-container">
                                  <img
                                    src={
                                      data.item_photos &&
                                      data.item_photos.length > 0
                                        ? data.item_photos[0].item_photo
                                        : smallImage
                                    }
                                    alt=".."
                                    className="small-img"
                                  />
                                  {data.item_photos.length > 1 ? (
                                    <span className="extra-image">
                                      +{data.item_photos.length - 1}
                                    </span>
                                  ) : (
                                    ""
                                  )}
                                  {data.exceptions.length >= 1 ? (
                                    <span className="exception">
                                      {data.exceptions.length > 1
                                        ? `+${data.exceptions.length - 1} `
                                        : ""}
                                      E
                                    </span>
                                  ) : (
                                    ""
                                  )}
                                  {data.isOverride === "yes" ? (
                                    <span className="override">O</span>
                                  ) : (
                                    ""
                                  )}
                                </div>
                                <div
                                  style={{
                                    height: "40px",
                                    fontSize: "13px",
                                    textAlign: "center",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                  }}
                                >
                                  {data.volume +
                                    " " +
                                    "Cu. Ft. /" +
                                    data.weight +
                                    " " +
                                    "Lbs"}
                                </div>
                                <div
                                  style={{
                                    height: "40px",
                                    fontSize: "13px",
                                    textAlign: "center",
                                    color: "#8ab932",
                                    overflow: "hidden",
                                    textOverflow: "ellipsis",
                                  }}
                                >
                                  {data.type === "Generic"
                                    ? "G-"
                                    : data.type === "External"
                                    ? "E-"
                                    : ""}
                                  {data.item_qr.random_number.length > 10
                                    ? data.item_qr.label_number +
                                      "/" +
                                      data.item_qr.random_number.substring(
                                        0,
                                        10
                                      ) +
                                      "..."
                                    : data.item_qr.label_number +
                                      "/" +
                                      data.item_qr.random_number}
                                </div>
                              </div>
                            </Col>
                          ))
                        : ""}
                    </Row>
                    <Pagination
                      pageSize={pageSize}
                      current={this.state.current}
                      total={this.state.totalCount}
                      onChange={this.handleChangePagination}
                      style={{ bottom: "0px" }}
                    />
                  </Spin>
                </TabPane>
              ))}
            </Tabs>
          </div>

          <div className="signature-container">
            <h1 className="signature-header">Signature History</h1>
            <Signature>
              <div style={{ display: "flex", justifyContent: "normal" }}>
                <div style={{ width: "200px", padding: "5px" }}>
                  <p style={{ textAlign: "center" }}>
                    <strong>Stage</strong>
                  </p>
                </div>
                <div style={{ width: "350px", padding: "5px" }}>
                  <p style={{ textAlign: "center" }}>
                    <strong>User</strong>
                  </p>
                </div>
                <div style={{ width: "350px", padding: "5px" }}>
                  <p style={{ textAlign: "center" }}>
                    <strong>Customer</strong>
                  </p>
                </div>
                <div style={{ width: "200px", padding: "5px" }}>
                  <p style={{ textAlign: "center" }}>
                    <strong>Date/Time</strong>
                  </p>
                </div>
              </div>
              {customer &&
              customer.shipment_type_for_shipment &&
              customer.shipment_type_for_shipment.local_shipment_stage.length >=
                1 ? (
                customer.shipment_type_for_shipment.local_shipment_stage.map(
                  (e, index) => {
                    return (
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "normal",
                          padding: "10px 0px",
                        }}
                      >
                        <div
                          style={{
                            justifySelf: "baseline",
                            width: "200px",
                            padding: "10px",
                            alignItems: "left",
                          }}
                        >
                          <p>Stage {index + 1}:- </p>
                          <p>{e.name}</p>
                        </div>
                        <div
                          style={{
                            width: "350px",
                            padding: "8px",
                            alignItems: "center",
                          }}
                        >
                          {e.supervisor_signature ? (
                            <>
                              <p style={{ textAlign: "center" }}>
                                {e.supervisor_name}
                              </p>
                              <img
                                style={{ marginLeft: "70px" }}
                                src={e.supervisor_signature}
                                alt=""
                                width="125px"
                              />
                              <p>{e.why_supervisor_signature_require_note}</p>
                            </>
                          ) : (
                            <Icon
                              style={{ fontSize: "20px", marginLeft: "120px" }}
                              type="minus"
                            />
                          )}
                        </div>

                        <div
                          style={{
                            width: "350px",
                            padding: "8px",
                            alignItems: "center",
                          }}
                        >
                          {e.customer_signature ? (
                            <>
                              <p style={{ textAlign: "center" }}>
                                {e.customer_name}
                              </p>
                              <img
                                style={{ marginLeft: "70px" }}
                                src={e.customer_signature}
                                alt=""
                                width="125px"
                              />
                              <p>{e.why_customer_signature_require_note}</p>
                            </>
                          ) : (
                            <Icon
                              style={{ fontSize: "20px", marginLeft: "120px" }}
                              type="minus"
                            />
                          )}
                        </div>
                        <p
                          style={{
                            width: "200px",
                            padding: "5px",
                            alignItems: "right",
                          }}
                        >
                          {e.created_at ? (
                            this.toDate(e.created_at)
                          ) : (
                            <Icon
                              style={{ fontSize: "20px", marginLeft: "120px" }}
                              type="minus"
                            />
                          )}
                        </p>
                      </div>
                    );
                  }
                )
              ) : (
                <h2 className="no_signature">
                  <strong>No Signature history!</strong>
                </h2>
              )}
            </Signature>
          </div>

          <div style={{ margin: "2%", height: "5px" }}></div>
        </Spin>
      </Container>
    );
  }
}
export default PublicPage;
