import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class ShipmentTypeRoutes extends React.Component {
  render() {
    const { url } = this.props.match;
    return (
      <Switch>
        <Route
          exact
          path={`${url}/view-stages/edit/:id`}
          component={asyncComponent(() =>
            import("../../components/shipmentType/editShipmentTypeStage")
          )}
        />
        <Route
          exact
          path={`${url}/view-stages/:id`}
          component={asyncComponent(() =>
            import("../../components/shipmentType/shipmentTypeStageManagement")
          )}
        />
        <Route
          exact
          path={`${url}/edit/:id`}
          component={asyncComponent(() =>
            import("../../components/shipmentType/editShipmentType")
          )}
        />
        <Route
          exact
          path={`${url}/add`}
          component={asyncComponent(() =>
            import("../../components/shipmentType/addShipmentType")
          )}
        />
        <Route
          exact
          path={`${url}`}
          component={asyncComponent(() =>
            import("../../components/shipmentType/shipmentTypeManagement")
          )}
        />
      </Switch>
    );
  }
}

export default ShipmentTypeRoutes;
