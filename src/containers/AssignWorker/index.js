import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class AssignWorkerRoutes extends React.Component {
  render() {
    const { url } = this.props.match;
    return (
      <Switch>
        <Route
          exact
          path={`${url}/:id`}
          component={asyncComponent(() =>
            import("../../components/assignWorker/assignWorkerManagement")
          )}
        />
        <Route
          exact
          path={`${url}/:id/add-worker`}
          component={asyncComponent(() =>
            import("../../components/assignWorker/assignWorkerToJob")
          )}
        />
      </Switch>
    );
  }
}

export default AssignWorkerRoutes;
