import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class TagRoutes extends React.Component {
	render() {
		const { url } = this.props.match;
		return (
			<Switch>
				<Route
					exact
					path={`${url}/edit/:id`}
					component={asyncComponent(() =>
						import("../../components/Tags/editTagName")
					)}
				/>
				<Route
					exact
					path={`${url}/add`}
					component={asyncComponent(() =>
						import("../../components/Tags/addTagName")
					)}
				/>
				<Route
					exact
					path={`${url}`}
					component={asyncComponent(() =>
						import("../../components/Tags/tagManagement")
					)}
				/>
			</Switch>
		);
	}
}

export default TagRoutes;
