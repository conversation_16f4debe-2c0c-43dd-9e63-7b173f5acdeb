import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class CompanyRoutes extends React.Component {
  render() {
    const { url } = this.props.match;
    return (
      <Switch>
        <Route
          exact
          path={`${url}/edit/:id`}
          component={asyncComponent(() =>
            import("../../components/company/editCompany")
          )}
        />
        <Route
          exact
          path={`${url}/edit-password/:id`}
          component={asyncComponent(() =>
            import("../../components/company/editCompanyPassword")
          )}
        />
        <Route
          exact
          path={`${url}/add`}
          component={asyncComponent(() =>
            import("../../components/company/addCompany")
          )}
        />
        <Route
          exact
          path={`${url}/viewCompanyKey/:id`}
          component={asyncComponent(() =>
            import("../../components/company/viewCompanyKey")
          )}
        />
        <Route
          exact
          path={`${url}`}
          component={asyncComponent(() =>
            import("../../components/company/companyManagement")
          )}
        />
      </Switch>
    );
  }
}

export default CompanyRoutes;
