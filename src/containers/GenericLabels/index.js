import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class GenericLabels extends React.Component {
	render() {
		const { url } = this.props.match;
		return (
			<Switch>
				<Route
					exact
					path={`${url}/view/:id/edit/:id`}
					component={asyncComponent(() =>
						import("../../components/GenericLabels/editLabels")
					)}
				/>
				<Route
					exact
					path={`${url}/view/:id/:id/print`}
					component={asyncComponent(() =>
						import("../../components/GenericLabels/printLabel")
					)}
				/>
				<Route
					exact
					path={`${url}/add`}
					component={asyncComponent(() =>
						import("../../components/GenericLabels/addLabels")
					)}
				/>
				<Route
					exact
					path={`${url}/view/:id`}
					component={asyncComponent(() =>
						import("../../components/GenericLabels/labelManagement")
					)}
				/>
				<Route
					exact
					path={`${url}/view/:id/dymo-print`}
					component={asyncComponent(() =>
						import("../../components/GenericLabels/printDymoQrCode")
					)}
				/>
				<Route
					exact
					path={`${url}`}
					component={asyncComponent(() =>
						import("../../components/GenericLabels/batchLabelManagement")
					)}
				/>
			</Switch>
		);
	}
}

export default GenericLabels;
