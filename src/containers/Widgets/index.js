import React, { Component } from 'react';
import { Row, Col } from 'antd';
import LayoutWrapper from '../../components/utility/layoutWrapper.js';
import basicStyle from '../../settings/basicStyle';
import IsoWidgetsWrapper from './widgets-wrapper';
import SaleWidget from './sale/sale-widget';
import IntlMessages from '../../components/utility/intlMessages';
import StickerWidget from './sticker/sticker-widget';

export default class extends Component {
  render() {
    const { rowStyle, colStyle } = basicStyle;
    const pageStyle = {
      display: 'flex',
      flexFlow: 'row wrap',
      alignItems: 'flex-start',
      overflow: 'hidden',
    };

    return (
      <LayoutWrapper>
        <div style={pageStyle}>
          <Row style={rowStyle} gutter={0} justify="start">
            <Col lg={6} md={12} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <StickerWidget
                  number={<IntlMessages id="widget.stickerwidget1.number" />}
                  text={<IntlMessages id="widget.stickerwidget1.text" />}
                  icon="ion-email-unread"
                  fontColor="#ffffff"
                  bgColor="#7266BA"
                />
              </IsoWidgetsWrapper>
            </Col>

            <Col lg={6} md={12} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <StickerWidget
                  number={<IntlMessages id="widget.stickerwidget1.number" />}
                  text={<IntlMessages id="widget.stickerwidget2.text" />}
                  icon="ion-android-camera"
                  fontColor="#ffffff"
                  bgColor="#42A5F6"
                />
              </IsoWidgetsWrapper>
            </Col>

            <Col lg={6} md={12} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <StickerWidget
                  number={<IntlMessages id="widget.stickerwidget1.number" />}
                  text={<IntlMessages id="widget.stickerwidget3.text" />}
                  icon="ion-chatbubbles"
                  fontColor="#ffffff"
                  bgColor="#7ED320"
                />
              </IsoWidgetsWrapper>
            </Col>

            <Col lg={6} md={12} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <StickerWidget
                  number={<IntlMessages id="widget.stickerwidget1.number" />}
                  text={<IntlMessages id="widget.stickerwidget4.text" />}
                  icon="ion-android-cart"
                  fontColor="#ffffff"
                  bgColor="#F75D81"
                />
              </IsoWidgetsWrapper>
            </Col>
          </Row>

          <Row style={rowStyle} gutter={0} justify="start">
            <Col lg={6} md={12} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <SaleWidget
                  label={<IntlMessages id="widget.salewidget1.label" />}
                  price={<IntlMessages id="widget.salewidget1.price" />}
                  details={<IntlMessages id="widget.salewidget1.details" />}
                  fontColor="#F75D81"
                />
              </IsoWidgetsWrapper>
            </Col>

            <Col lg={6} md={12} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <SaleWidget
                  label={<IntlMessages id="widget.salewidget2.label" />}
                  price={<IntlMessages id="widget.salewidget2.price" />}
                  details={<IntlMessages id="widget.salewidget2.details" />}
                  fontColor="#F75D81"
                />
              </IsoWidgetsWrapper>
            </Col>

            <Col lg={6} md={12} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <SaleWidget
                  label={<IntlMessages id="widget.salewidget3.label" />}
                  price={<IntlMessages id="widget.salewidget3.price" />}
                  details={<IntlMessages id="widget.salewidget3.details" />}
                  fontColor="#F75D81"
                />
              </IsoWidgetsWrapper>
            </Col>

            <Col lg={6} md={12} sm={12} xs={24} style={colStyle}>
              <IsoWidgetsWrapper>
                <SaleWidget
                  label={<IntlMessages id="widget.salewidget4.label" />}
                  price={<IntlMessages id="widget.salewidget4.price" />}
                  details={<IntlMessages id="widget.salewidget4.details" />}
                  fontColor="#F75D81"
                />
              </IsoWidgetsWrapper>
            </Col>
          </Row>
        </div>
      </LayoutWrapper>
    );
  }
}
