@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&family=Montserrat:wght@300;400;500;600;700&family=Open+Sans:wght@300;400;600&display=swap");

.container {
	background-color: rgba(144, 238, 144, 0.5);
	border: 2px solid #223d24;
	box-shadow: 3px 3px 5px rgba(0, 0, 0, 0.25);
	border-radius: 2px;
	padding: 15px 0px;
	transition: transform 0.25s ease-in-out;
}
.container:hover {
	transform: scale(1.05);
	box-shadow: 0 5px 15px rgba(0, 0, 0, 0.35);
}
.sub_container {
	padding: 10px 25px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.table {
	font-family: "Open Sans", sans-serif;
	font-size: 16px;
	text-transform: capitalize;
	letter-spacing: 0.6px;
	color: #202020;
	width: 100%;
	margin: 0 auto;
	border-collapse: collapse;
	text-align: -webkit-center;
}
.table thead td {
	font-weight: 600;
	/* text-align: center; */
text-align: -webkit-center;
}
.table thead td:nth-of-type(1){
	font-weight: 600;
	/* text-align: center; */
text-align: -webkit-left;
}
.table tbody {
	text-align: center;
}
.table tbody tr td:nth-of-type(1) {
	text-align: start;
}
/* .detail_container {
	padding: 10px 0;
	display: flex;
	justify-content: space-around;
	align-items: center;
	margin-right: 25px;
} */

.container h2 {
	font-family: "Montserrat", sans-serif;
	font-size: 20px;
	font-weight: 600;
	letter-spacing: 0.6px;
	text-transform: uppercase;
	text-align: center;
	margin-bottom: 5px;
	color: #333;
}
.container h2 i {
	margin-right: 10px;
}
.container .sub_container h4 {
	font-family: "Open Sans", sans-serif;
	font-size: 16px;
	font-weight: 600;
	text-transform: capitalize;
	letter-spacing: 0.6px;
	/* text-align: center; */
	margin-bottom: 0px;
	color: #202020;
}

.container p {
	font-family: "Lato", sans-serif;
	font-size: 16px;
	text-align: center;
	margin-bottom: 0px;
	color: #303030;
}
@media (max-width: 575px) {
	.container {
		margin-bottom: 15px;
	}
}
