import React from "react";
import { Route, Switch } from "react-router-dom";
import asyncComponent from "../../helpers/AsyncFunc";

const index = ({ match }) => {
	const { url } = match;

	return (
		<Switch>
			{/* <Route
				exact
				path={`${url}/edit/:id`}
				component={asyncComponent(() =>
					import("../../components/customer/editCustomer")
				)}
			/>*/}
			<Route
				exact
				path={`${url}/add`}
				component={asyncComponent(() =>
					import("../../components/ExternalApi/Add")
				)}
			/>
			<Route
				exact
				path={`${url}`}
				component={asyncComponent(() =>
					import("../../components/ExternalApi/List")
				)}
			/>
		</Switch>
	);
};

export default index;
