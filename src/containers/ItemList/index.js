import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class ItemListRoutes extends React.Component {
	render() {
		const { url } = this.props.match;
		return (
			<Switch>
				<Route
					exact
					path={`${url}/edit/:id`}
					component={asyncComponent(() =>
						import("../../components/ItemList/editItemList")
					)}
				/>
				<Route
					exact
					path={`${url}/add`}
					component={asyncComponent(() =>
						import("../../components/ItemList/addItemList")
					)}
				/>
				<Route
					exact
					path={`${url}`}
					component={asyncComponent(() =>
						import("../../components/ItemList/itemListManagement")
					)}
				/>
			</Switch>
		);
	}
}

export default ItemListRoutes;
