import { Route, Switch } from "react-router-dom";

import React from "react";
import asyncComponent from "../../helpers/AsyncFunc";

class CustomerRoutes extends React.Component {
  render() {
    const { url } = this.props.match;
    return (
      <Switch>
        <Route
          exact
          path={`${url}/edit/:id`}
          component={asyncComponent(() =>
            import("../../components/customer/editCustomer")
          )}
        />
        <Route
          exact
          path={`${url}/add`}
          component={asyncComponent(() =>
            import("../../components/customer/addCustomer")
          )}
        />
        <Route
          exact
          path={`${url}`}
          component={asyncComponent(() =>
            import("../../components/customer/customerManagement")
          )}
        />
      </Switch>
    );
  }
}

export default CustomerRoutes;
