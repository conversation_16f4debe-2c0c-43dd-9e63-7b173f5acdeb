{"sidebar.email": "Email", "sidebar.ecommerce": "Ecommerce", "sidebar.shop": "tienda", "sidebar.cart": "<PERSON><PERSON>", "sidebar.checkout": "revisa", "sidebar.cards": "Divertido Tarjetas", "sidebar.maps": "Mapas", "sidebar.googleMap": "Mapa de Google", "sidebar.leafletMap": "Mapa del folleto", "sidebar.calendar": "Calendario", "sidebar.notes": "Notas", "sidebar.todos": "Todos", "sidebar.contacts": "Contactos", "sidebar.shuffle": "Barajar", "sidebar.charts": "Grá<PERSON><PERSON>", "sidebar.googleCharts": "Google Carts", "sidebar.recharts": "Recharts", "sidebar.reactVis": "Reaccionar Vis", "sidebar.reactChart2": "React-Chart-2", "sidebar.reactTrend": "Reaccionar", "sidebar.eChart": "Echart", "sidebar.forms": "Formularios", "sidebar.input": "Entrada", "sidebar.editor": "Editor", "sidebar.formsWithValidation": "Formularios con validación", "sidebar.progress": "Progreso", "sidebar.button": "Botón", "sidebar.tab": "Lengüeta", "sidebar.checkbox": "Caja", "sidebar.radiobox": "Radiobox", "sidebar.selectbox": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sidebar.transfer": "Transferir", "sidebar.autocomplete": "Autocompletar", "sidebar.boxOptions": "Opciones de Caja", "sidebar.uiElements": "Elementos de la interfaz de usuario", "sidebar.badge": "Distintivo", "sidebar.card2": "Tarjeta", "sidebar.corusel": "<PERSON><PERSON><PERSON>", "sidebar.collapse": "Colapso", "sidebar.popover": "Acercarse", "sidebar.tooltip": "<PERSON><PERSON><PERSON>", "sidebar.tag": "Etiqueta", "sidebar.timeline": "Cronograma", "sidebar.dropdown": "Desplegable", "sidebar.pagination": "Paginación", "sidebar.rating": "Clasificación", "sidebar.tree": "Árbol", "sidebar.advancedElements": "Elementos avanzados", "sidebar.reactDates": "Reaccionar fechas", "sidebar.codeMirror": "<PERSON><PERSON><PERSON>", "sidebar.uppy": "Uppy Uploader", "sidebar.dropzone": "Zona de descenso", "sidebar.feedback": "Realimentación", "sidebar.alert": "<PERSON><PERSON><PERSON>", "sidebar.modal": "Modal", "sidebar.message": "Men<PERSON><PERSON>", "sidebar.notification": "Notificación", "sidebar.popConfirm": "Pop confirmar", "sidebar.spin": "<PERSON><PERSON><PERSON>", "sidebar.tables": "Mesas", "sidebar.antTables": "Tablas de hormigas", "sidebar.pages": "<PERSON><PERSON><PERSON><PERSON>", "sidebar.500": "500", "sidebar.404": "404", "sidebar.signIn": "Registrarse", "sidebar.signUp": "Regístrate", "sidebar.forgotPw": "<PERSON><PERSON><PERSON><PERSON> con<PERSON>", "sidebar.resetPw": "Restablecer contrase<PERSON>s", "sidebar.invoice": "Factura", "sidebar.menuLevels": "<PERSON><PERSON><PERSON> de <PERSON>ú", "sidebar.item1": "Artículo 1", "sidebar.item2": "Artículo 2", "sidebar.option1": "Opción 1", "sidebar.option2": "opcion 2", "sidebar.option3": "Opción 3", "sidebar.option4": "Opción 4", "sidebar.blankPage": "Página en blanco", "sidebar.githubSearch": "Github Buscar", "sidebar.youtubeSearch": "Búsqueda de Youtube", "languageSwitcher.label": "Cambiar idioma", "themeSwitcher": "Selector <PERSON>", "themeSwitcher.Sidebar": "Barra lateral", "themeSwitcher.Topbar": "Barra superior", "themeSwitcher.Background": "Fondo", "feedback.alert.basicTitle": "<PERSON><PERSON><PERSON><PERSON>", "feedback.alert.successText": "Texto de éxito", "feedback.alert.infoText": "Texto de la información", "feedback.alert.warningText": "Texto de advertencia", "feedback.alert.errorText": "Texto de error", "feedback.alert.closableAlertType": "Tipo de Alerta Closable", "feedback.alert.iconAlertType": "Tipo de alerta de icono", "feedback.alert.iconInfoAlertType": "Tipo de Alerta", "feedback.alert.successTips": "consejos de éxito", "feedback.alert.successTipsDescription": "Descripción detallada y consejos sobre copywriting exitoso.", "feedback.alert.informationTips": "Notas informativas", "feedback.alert.informationDescription": "Descripción adicional e informaciones sobre copywriting.", "feedback.alert.warningTips": "Advertencia", "feedback.alert.warningDescription": "Este es un aviso de advertencia sobre copywriting.", "feedback.alert.errorTips": "Error", "feedback.alert.errorDescription": "Este es un mensaje de error acerca de copywriting.", "feedback.alert.modalTitle": "Modal uno con personalizar <PERSON>", "feedback.alert.modalSubTitle": "Diálogo modal básico.", "feedback.alert.successTitle": "Éxito", "feedback.alert.infoTitle": "Información", "feedback.alert.errorTitle": "Error", "feedback.alert.warningTitle": "Advertencia", "feedback.alert.modalBlockTitle": "Modal", "feedback.alert.confirmationModalDialogue": "Cuadro de diálogo modal de confirmación", "feedback.alert.simpleModalDialogue": "Diálogo modal simple", "feedback.alert.message": "Men<PERSON><PERSON>", "feedback.alert.normalMessageTitle": "Mensaje normal", "feedback.alert.normalMessageSubtitle": "Mensajes normales como retroalimentación.", "feedback.alert.displayMessage": "Mostrar mensaje normal", "feedback.alert.displayOtherTypeMessageTitle": "Otros tipos de mensaje", "feedback.alert.displayOtherTypeMessageSubTitle": "Mensajes de éxito   error y tipos de advertencia.", "feedback.alert.customizeDurationTitle": "Personalizar duración", "feedback.alert.customizeDurationSubTitle": "ustomize la duración de la exhibición del mensaje de 1.5s a 10s por defecto.", "feedback.alert.customizeDurationButton": "Duración de la pantalla personalizada", "feedback.alert.messageLoadingTitle": "Mensaje de carga", "feedback.alert.messageLoadingSubTitle": "Mostrar un indicador de carga global   que se descarta por sí mismo de forma asíncrona.", "feedback.alert.displayLoadIndicator": "Mostrar un indicador de carga", "feedback.alert.notification": "Notificación", "feedback.alert.notificationBasicTitle": "BASIC", "feedback.alert.notificationBasicSubTitle": "El uso más simple que cierre la caja de notificación después de 4.5s.", "feedback.alert.notificationBasicDescription": "Abrir el cuadro de notificación", "feedback.alert.notificationDurationTitle": "Duración después de la cual se cierra el cuadro de notificación", "feedback.alert.notificationDurationSubTitle": "La duración se puede utilizar para especificar cuánto tiempo permanece abierta la notificación. Una vez transcurrido el tiempo de duración   la notificación se cierra automáticamente. Si no se especifica   el valor predeterminado es 4  5 segundos. Si establece el valor en 0   el cuadro de notificación nunca se cerrará automáticamente.", "feedback.alert.notificationwithIconTitle": "Notificación con icono", "feedback.alert.notificationwithIconSubTitle": "Un cuadro de notificación con un icono en el lado izquierdo.", "feedback.alert.notificationwithCustomIconTitle": "Notificación con icono personalizado", "feedback.alert.notificationwithCustomIconSubTitle": "Mensajes normales como retroalimentación.", "feedback.alert.notificationwithCustomButtonTitle": "Notificación con botón personalizado", "feedback.alert.notificationwithCustomButtonSubTitle": "Mensajes normales como retroalimentación.", "feedback.alert.popConfirm": "Pop confirmar", "feedback.alert.popConfirm.basicTitle": "Confirmación básica", "feedback.alert.popConfirm.basicSubTitle": "El ejemplo básico.", "feedback.alert.popConfirm.delete": "Bo<PERSON>r", "feedback.alert.popConfirm.notiWithIconTitle": "Notificación con icono personalizado", "feedback.alert.popConfirm.notiWithIconSubTitle": "Mensajes normales como retroalimentación.", "feedback.alert.popConfirm.TL": "TL", "feedback.alert.popConfirm.top": "Parte superior", "feedback.alert.popConfirm.TR": "TR", "feedback.alert.popConfirm.LT": "LT", "feedback.alert.popConfirm.left": "Iz<PERSON>erda", "feedback.alert.popConfirm.LB": "LB", "feedback.alert.popConfirm.RT": "RT", "feedback.alert.popConfirm.right": "Derecha", "feedback.alert.popConfirm.RB": "RB", "feedback.alert.popConfirm.Bl": "licenciado en Derecho", "feedback.alert.popConfirm.bottom": "Fondo", "feedback.alert.popConfirm.BR": "BR", "feedback.alert.spin": "<PERSON><PERSON><PERSON>", "feedback.alert.spin.basicTitle": "Girar el tamaño", "feedback.alert.spin.background": "<PERSON><PERSON><PERSON> con fondo", "feedback.alert.spin.backgroundDescription": "Descripción de Spin With Background", "feedback.alert.spin.loadingState": "Estado de carga ", "feedback.alert.spin.alertTitle": "Título del mensaje de alerta", "feedback.alert.spin.alertDescription": "Más detalles sobre el contexto de esta alerta.", "forms.input.header": "Entrada", "forms.input.basicTitle": "Uso básico", "forms.input.basicSubTitle": "Ejemplo de uso básico.", "forms.input.variationsTitle": "Tres tamaños de entrada", "forms.input.variationsSubtitle": "Hay tres tamaños de un cuadro de entrada  grande (42px     predeterminado (35px   y pequeño (30px  . Nota  Dentro de los formularios   sólo se utiliza el tamaño grande.", "forms.input.groupTitle": "Grupo de entrada", "forms.input.groupSubTitle": "Ejemplo de Input.Group Nota  No necesita Col para controlar el ancho en el modo compacto.", "forms.input.autoSizingTitle": "Autosizing la altura para ajustar el contenido", "forms.input.autoSizingSubTitle": "prop de autosize para un tipo de entrada textarea hace que la altura se ajuste automáticamente en función del contenido. Se puede proporcionar un objeto de opciones al tamaño automático para especificar el número mínimo y máximo de líneas que la zona de texto ajustará automáticamente.", "forms.input.prePostTabTitle": "Pestaña Pre    Post", "forms.input.prePostTabSubTitle": "El uso de pre & amp; post tabs ejemplo ..", "forms.input.textAreaTitle": "Área de texto", "forms.input.textAreaSubTitle": "Para casos de entrada de usuario multi-línea   se puede utilizar una entrada cuyo tipo prop tiene el valor de textarea.", "forms.input.searchTitle": "Buscar", "forms.input.searchSubTitle": "Ejemplo de creación de un cuadro de búsqueda agrupando una entrada estándar con un botón de búsqueda", "forms.editor.header": "Editor", "forms.formsWithValidation.header": "Formulario de validación personalizado", "forms.formsWithValidation.failLabel": "<PERSON>ar", "forms.formsWithValidation.failHelp": "Debe ser la combinación de números & amp; alfabetos", "forms.formsWithValidation.warningLabel": "Advertencia", "forms.formsWithValidation.ValidatingLabel": "Validando", "forms.formsWithValidation.ValidatingHelp": "La información está siendo validada ...", "forms.formsWithValidation.SuccessLabel": "Éxito", "forms.formsWithValidation.WarninghasFeedbackLabel": "Advertencia", "forms.formsWithValidation.FailhasFeedbackLabel": "<PERSON>ar", "forms.formsWithValidation.FailhasFeedbackHelp": "Debe ser la combinación de números & amp; alfabetos", "forms.progressBar.header": "Barra de progreso", "forms.progressBar.standardTitle": "Barra de progreso", "forms.progressBar.standardSubTitle": "Una barra de progreso estándar.", "forms.progressBar.circularTitle": "Barra de progreso circular", "forms.progressBar.circularSubTitle": "Una barra de progreso circular.", "forms.progressBar.miniTitle": "Barra de progreso de tamaño mini", "forms.progressBar.miniSubTitle": "Adecuado para un área estrecha.", "forms.progressBar.miniCircularTitle": "Una barra de progreso circular más pequeña.", "forms.progressBar.dynamicCircularTitle": "Barra de progreso circular dinámica", "forms.progressBar.dynamicCircularSubTitle": "Una barra de progreso dinámica es mejor.", "forms.progressBar.customTextTitle": "Formato de texto personalizado", "forms.progressBar.customTextSubTitle": "Puede personalizar el formato de texto configurando el formato.", "forms.progressBar.dashboardTitle": "<PERSON><PERSON>", "forms.progressBar.dashboardSubTitle": "Un estilo de progreso en el tablero de instrumentos.", "forms.button.header": "Botones", "forms.button.simpleButton": "Tipo de botón", "forms.button.iconButton": "Icono de botón", "forms.button.simpleButtonPrimaryText": "Primario", "forms.button.simpleButtonDefaultText": "Defecto", "forms.button.simpleButtonDashedText": "<PERSON><PERSON><PERSON>", "forms.button.simpleButtonDangerText": "<PERSON><PERSON><PERSON>", "forms.button.iconPrimaryButton": "buscar", "forms.button.iconSimpleButton": "buscar", "forms.button.iconCirculerButton": "buscar", "forms.button.iconDashedButton": "buscar", "forms.button.SizedButton": "Tamaño del botón", "forms.button.DisabledButton": "Botón desactivado", "forms.button.LoadingButton": "Botón de carga", "forms.button.MultipleButton": "<PERSON><PERSON><PERSON>", "forms.button.groupButton": "Grupo de botones", "forms.Tabs.header": "Pestañas", "forms.Tabs.simpleTabTitle": "buscar", "forms.Tabs.simpleTabSubTitle": "Pestañas inhabilitadas", "forms.Tabs.iconTabTitle": "Icono de las pestañas", "forms.Tabs.miniTabTitle": "Mini pestañas", "forms.Tabs.extraTabTitle": "Pestañas de acción adicionales", "forms.Tabs.TabpositionTitle": "Posición", "forms.Tabs.TabpositionSubTitle": "Posición de las pestañas  izquierda   derecha   arriba o abajo", "forms.Tabs.cardTitle": "Fichas de tipo de tarjeta", "forms.Tabs.editableTitle": "Agregar y cerrar pestañas", "forms.Tabs.verticalTitle": "Fichas de tipo vertical", "forms.Tabs.basicTitle": "Pestañas básicas", "forms.checkbox.header": "Caja", "forms.checkbox.basicTitle": "Casilla de verificación básica", "forms.checkbox.basicSubTitle": "Uso básico de la casilla de verificación.", "forms.checkbox.groupTitle": "Grupo de casillas de verificación", "forms.checkbox.groupSubTitle": "Genera un grupo de casillas de verificación de una matriz. Utilizar desactivado para deshabilitar una casilla de verificación.", "forms.checkbox.groupCheckTitle": "Grupo de casillas de verificación", "forms.checkbox.groupCheckSubTitle": "Genera un grupo de casillas de verificación de una matriz. Utilizar desactivado para deshabilitar una casilla de verificación.", "forms.radio.header": "Radio", "forms.radio.simpleTitle": "Radio básica", "forms.radio.simpleSubTitle": "El uso más simple. Utilizar desactivado para desactivar una radio.", "forms.radio.groupTitle": "Grupo de radio vertical", "forms.radio.groupSubTitle": "Vertical RadioGroup   con más radios.", "forms.radio.groupSecondTitle": "Grupo de radio", "forms.radio.groupSecondSubTitle": "Un grupo de componentes de radio.", "forms.radio.groupThirdTitle": "Grupo de radio", "forms.radio.groupThirdSubTitle": "Un grupo de componentes de radio.", "forms.transfer.header": "Transferir", "forms.transfer.SubTitle": "Transferir con un cuadro de búsqueda.", "forms.transfer.Title": "Buscar", "forms.autocomplete.header": "Autocompletar", "forms.autocomplete.simpleTitle": "Personalizado", "forms.autocomplete.simpleSubTitle": "Puede pasar AutoComplete.Option como hijos de Autocompletar   en lugar de utilizar dataSource", "forms.autocomplete.customizeTitle": "Personalizar el componente de entrada", "forms.autocomplete.customizeSubTitle": "Personalizar el componente de entrada", "uiElements.badge.badge": "Distintivo", "uiElements.badge.basicExample": "Ejemplo Básico", "uiElements.badge.basicExampleSubTitle": "Uso más simple. La insignia se ocultará cuando count sea 0   pero podemos usar showZero para mostrarlo.", "uiElements.badge.overflowCount": "Cuenta de desbordamiento", "uiElements.badge.overflowCountSubTitle": "OverflowCount se muestra cuando count es mayor que overflowCount. El valor predeterminado de overflowCount es 99.", "uiElements.badge.status": "Estado", "uiElements.badge.statusSubTitle": "Insignia autónoma con estado.", "uiElements.badge.success": "Éxito", "uiElements.badge.error": "Error", "uiElements.badge.default": "Defecto", "uiElements.badge.processing": "Tratamiento", "uiElements.badge.warning": "Advertencia", "uiElements.badge.redBadge": "Insignia roja", "uiElements.badge.redBadgeSubTitle": "Esto simplemente mostrará una insignia roja   sin un conteo específico.", "uiElements.badge.linkSomething": "Enlace algo", "uiElements.cards.cards": "Divertido Tarjetas", "uiElements.cards.basicCard": "Tarjeta básica", "uiElements.cards.basicCardSubTitle": "Una tarjeta básica que contiene un título   contenido y un contenido de esquina adicional.", "uiElements.cards.more": "Más", "uiElements.cards.cardTitle": "<PERSON><PERSON><PERSON><PERSON> de la tarjeta", "uiElements.cards.cardContent": "Contenido de la tarjeta", "uiElements.cards.lorem": "Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor incididunt ut labore y dolore magna aliqua. Ut enim ad minim veniam   quis nostrud ejercicio ullamco laboris nisi ut aliquip ex y commodo consequat.", "uiElements.cards.noBorder": "<PERSON> bordes", "uiElements.cards.noBorderSubTitle": "Una tarjeta sin fronteras sobre un fondo gris.", "uiElements.cards.gridCard": "Tarjeta de red", "uiElements.cards.gridCardSubTitle": "Las tarjetas suelen cooperar con el diseño de la cuadrícula en la página de vista general.", "uiElements.cards.loadingCard": "Carga de la tarjeta", "uiElements.cards.loadingCardSubTitle": "Muestra un indicador de carga mientras se está recuperando el contenido de la tarjeta.", "uiElements.cards.whateverContent": "<PERSON><PERSON><PERSON><PERSON> contenido", "uiElements.cards.customizedContentTitle": "Contenido personalizado", "uiElements.cards.customizedContent": "Muestra un indicador de carga mientras se está recuperando el contenido de la tarjeta.", "uiElements.cards.europeStreetBeat": "Europa Street beat", "uiElements.cards.instagram": "www.instagram.com", "uiElements.carousel.carousel": "<PERSON><PERSON><PERSON>", "uiElements.carousel.verticalCarousel": "Carrusel vertical", "uiElements.carousel.verticalCarouselSubTitle": "Paginación vertical. use   vertical = true  ", "uiElements.carousel.basicCarousel": "Carrusel básico", "uiElements.carousel.basicCarouselSubTitle": "Uso básico", "uiElements.carousel.fadeInTransition": "Fade In Transition", "uiElements.carousel.fadeInTransitionSubTitle": "Las diapositivas utilizan el fundido para la transición.   effect = fade  ", "uiElements.carousel.scrollAutomatically": "Desplazarse automáticamente", "uiElements.carousel.scrollAutomaticallySubTitle": "Tiempo de desplazamiento a la siguiente tarjeta    imagen. auto reproducción", "uiElements.collapse.collapse": "Colapso", "uiElements.collapse.collapseSubTitle": "Se puede ampliar más de un panel a la vez   el primer panel se inicializa para estar activo en este caso. use   defaultActiveKey =   [keyNum]    ", "uiElements.collapse.text": "Un perro es un tipo de animal domesticado. Conocido por su lealtad y fidelidad   se puede encontrar como un invitado de bienvenida en muchos hogares de todo el mundo.", "uiElements.collapse.headerOne": "Este es el encabezado del panel 1", "uiElements.collapse.headerTwo": "Se trata de la cabecera del panel 2", "uiElements.collapse.headerThree": "Este es el encabezado del panel 3", "uiElements.collapse.headerNested": "Éste es panel del nido del panel", "uiElements.collapse.nestedExample": "<PERSON><PERSON><PERSON><PERSON> an<PERSON>", "uiElements.collapse.nestedExampleSubTitle": "Collapse está anidado dentro del Collapse.", "uiElements.collapse.borderlessExample": "<PERSON><PERSON><PERSON><PERSON> sin márgenes", "uiElements.collapse.borderlessExampleSubTitle": "Un estilo sin fronteras de Collapse. use   bordered =   false    ", "uiElements.collapse.accordion": "Acordeón", "uiElements.collapse.accordionSubTitle": "Acordeón   sólo se puede ampliar un panel cada vez. El primer panel se ampliará de forma predeterminada. utilizar acordeón", "uiElements.popover.popover": "Popover", "uiElements.popover.basicExample": "Ejemplo Básico", "uiElements.popover.basicExampleSubTitle": "El ejemplo más básico. El tamaño de la capa flotante depende de la región del contenido.", "uiElements.popover.hoverMe": "<PERSON><PERSON><PERSON><PERSON>", "uiElements.popover.title": "<PERSON><PERSON><PERSON><PERSON>", "uiElements.popover.titleTrigger": "Tres maneras de activar", "uiElements.popover.titleTriggerSubTitle": "El ratón para hacer clic   enfocar y moverse.", "uiElements.popover.focusMe": "<PERSON>fó<PERSON>", "uiElements.popover.clickMe": "Haz click en mi", "uiElements.popover.placement": "Colocación", "uiElements.popover.placementSubTitle": "Hay 12 opciones de colocación disponibles.", "uiElements.popover.top": "Parte superior", "uiElements.popover.topLeft": "Arriba a la izquierda", "uiElements.popover.topRight": "Parte superior derecha", "uiElements.popover.leftTop": "Parte superior izquierda", "uiElements.popover.left": "Iz<PERSON>erda", "uiElements.popover.leftBottom": "Abajo a la izquierda", "uiElements.popover.rightTop": "<PERSON><PERSON>", "uiElements.popover.right": "Derecha", "uiElements.popover.bottom": "Fondo", "uiElements.popover.bottomLeft": "<PERSON><PERSON><PERSON>", "uiElements.popover.bottomRight": "Abajo a la derecha", "uiElements.popover.boxTitle": "Control del cierre del diálogo", "uiElements.popover.boxSubTitle": "Utilice el apoyo visible para controlar la visualización de la tarjeta.", "uiElements.popover.TR": "TR", "uiElements.popover.TL": "TL", "uiElements.popover.LT": "LT", "uiElements.popover.LB": "LB", "uiElements.popover.RT": "RT", "uiElements.popover.RB": "RB", "uiElements.popover.BL": "licenciado en Derecho", "uiElements.popover.BR": "BR", "uiElements.popover.close": "Cerca", "uiElements.tooltip.tooltip": "<PERSON><PERSON><PERSON>", "uiElements.tooltip.tooltipContent": "Contenido de información sobre herramientas", "uiElements.tooltip.basicExample": "Ejemplo Básico", "uiElements.tooltip.basicExampleSubTitle": "El uso más simple.", "uiElements.tooltip.placementTitle": "Colocación", "uiElements.tooltip.placementSubTitle": "La herramienta tiene 12 opciones de ubicación.", "uiElements.tooltip.TL": "TL", "uiElements.tooltip.TR": "TR", "uiElements.tooltip.LT": "LT", "uiElements.tooltip.LB": "LB", "uiElements.tooltip.RT": "RT", "uiElements.tooltip.RB": "RB", "uiElements.tooltip.BL": "licenciado en Derecho", "uiElements.tooltip.BR": "BR", "uiElements.tooltip.bottom": "Fondo", "uiElements.tooltip.right": "Derecha", "uiElements.tooltip.left": "Iz<PERSON>erda", "uiElements.tooltip.top": "Parte superior", "uiElements.tooltip.tooltipContentSpan": "La información sobre herramientas se mostrará cuando se introduzca el ratón.", "uiElements.tooltip.contentSpan": "Contenido de información sobre herramientas", "uiElements.tags.tags": "Etiquetas", "uiElements.tags.basicExample": "Ejemplo Básico", "uiElements.tags.basicExampleSubTitle": "Uso de la etiqueta básica   y podría ser cerrable por la propiedad cerrable del sistema. La etiqueta Closable soporta eventos onClose afterClose.", "uiElements.tags.tagOne": "Etiqueta 1", "uiElements.tags.tagTwo": "Etiqueta 2", "uiElements.tags.link": "<PERSON><PERSON><PERSON>", "uiElements.tags.preventDefault": "Prevenga el Incumplimiento", "uiElements.tags.colorfulTag": "Etiqueta colorida", "uiElements.tags.hotTags": "Etiquetas populares", "uiElements.tags.hotTagsSubTitle": "Seleccione sus temas favoritos.", "uiElements.tags.hots": "Hots ", "uiElements.tags.addRemoveDynamically": "Agregar y eliminar dinámic<PERSON>e", "uiElements.tags.addRemoveDynamicallySubTitle": "Generando un conjunto de etiquetas por matriz   puede agregar y quitar dinámicamente. Se basa en el evento afterClose   que se activará mientras finaliza la animación de cierre.", "uiElements.tags.newTag": "+ Nueva etiqueta", "uiElements.timeline.timeline": "Cronograma", "uiElements.timeline.basicExample": "Ejemplo Básico", "uiElements.timeline.basicTimeline": "Línea de tiempo básica", "uiElements.timeline.lastNode": "<PERSON><PERSON>imo nodo", "uiElements.timeline.lastNodeContent": "Cuando la línea de tiempo está incompleta y en curso   poner un nodo fantasma por fin. set   pending =   true     o   pending =   un elemento React    ", "uiElements.timeline.seeMore": "<PERSON>er más", "uiElements.timeline.custom": "Personalizado", "uiElements.timeline.customContent": "Establezca un nodo como un icono u otro elemento personalizado.", "uiElements.timeline.colorExample": "Ejemplo de color", "uiElements.timeline.colorExampleContent": "Establecer el color de los círculos. verde significa estado completado o de éxito   rojo significa advertencia o error y azul significa estado en curso u otro estado predeterminado.", "uiElements.timeline.createServiceSite": "Crear un sitio de servicios 2015-09-01", "uiElements.timeline.solveInitialNetwork": "Resolver problemas de red iniciales 2015-09-01", "uiElements.timeline.networkProblemSolved": "Problemas de red resueltos 2015-09-01", "uiElements.timeline.technicalTesting": "Pruebas técnicas 2015-09-01", "uiElements.dropdown.dropdown": "Desplegable", "uiElements.dropdown.hoverDropdown": "Desplácese", "uiElements.dropdown.hoverMe": "<PERSON><PERSON><PERSON><PERSON>", "uiElements.dropdown.hoverPlacement": "Despliegue de colocación de cola", "uiElements.dropdown.hoverDisableLink": "Desplazamiento con desplegable", "uiElements.dropdown.clickedDropdown": "Desplegable pulsado", "uiElements.dropdown.buttonDropdown": "Botón con menú desplegable", "uiElements.pagination.pagination": "Paginación", "uiElements.pagination.basic": "BASIC", "uiElements.pagination.more": "Más", "uiElements.pagination.changer": "Cambiador", "uiElements.pagination.jumper": "Saltador", "uiElements.pagination.miniSize": "Tamaño mini", "uiElements.pagination.simpleMode": "Modo simple", "uiElements.pagination.controlled": "<PERSON><PERSON><PERSON>", "uiElements.pagination.totalNumber": "Numero total", "uiElements.rating.rating": "Clasificación", "uiElements.rating.basicExample": "Ejemplo Básico", "uiElements.rating.basicExampleSubTitle": "El uso más simple.", "uiElements.rating.halfStar": "Media estrella", "uiElements.rating.halfStarSubTitle": "Soporte de media estrella.", "uiElements.rating.showCopywriting": "Mostrar copywriting", "uiElements.rating.showCopywritingSubTitle": "Añadir copywriting en los componentes de la tarifa.", "uiElements.rating.readOnly": "Solo lectura", "uiElements.rating.readOnlySubTitle": "Sólo lectura   no puede utilizar el ratón para interactuar.", "uiElements.rating.otherCharacter": "<PERSON><PERSON>", "uiElements.rating.otherCharacterSubTitle": "Reemplace la estrella predeterminada por otro carácter como alfabeto   dígito   iconfonte o incluso palabra china.", "uiElements.tree.tree": "Árbol", "uiElements.tree.basicExample": "Ejemplo básico", "uiElements.tree.basicExampleSubTitle": "El uso más básico   te dirá cómo usar checkable   seleccionable   disabled   defaultExpandKeys   y etc.", "uiElements.tree.basicControlledExample": "Ejemplo controlado básico", "uiElements.tree.basicControlledExampleSubTitle": "ejemplo controlado básico", "uiElements.tree.draggableExample": "<PERSON><PERSON><PERSON><PERSON> arrastrable", "uiElements.tree.draggableExampleSubTitle": "Arrastre treeNode para insertar después del otro treeNode o inserte en el otro TreeNode padre.", "uiElements.tree.loadAsync": "<PERSON><PERSON> datos asincrónicamente", "uiElements.tree.loadAsyncSubTitle": "Para cargar datos asincrónicamente cuando haga clic para expandir un treeNode.", "uiElements.tree.searchableExample": "Ejemplo de búsqueda", "uiElements.tree.searchableExampleSubTitle": "Árbol de búsqueda", "uiElements.tree.treeWithLine": "Árbol con línea", "shuffle.descriptionOne": "Netscape 2.0 se expande   introduciendo Javascript", "shuffle.descriptionTwo": "<PERSON> lanza la especificación AJAX", "shuffle.descriptionThree": "jQuery 1.0 publicado", "shuffle.descriptionFour": "Primero underscore.js commit", "shuffle.descriptionFive": "Backbone.js se convierte en una cosa", "shuffle.descriptionSix": "Angular 1.0 liberado", "shuffle.descriptionSeven": "Reaccionar es de código abierto; los desarrolladores se regocijan", "toggle.list": "Lista", "toggle.grid": "Cuadrícula", "toggle.ascending": "Ascendente", "toggle.descending": "Descendente", "toggle.shuffle": "Barajar", "toggle.rotate": "<PERSON><PERSON><PERSON>", "toggle.addItem": "<PERSON><PERSON><PERSON>", "toggle.removeItem": "Remover el artículo", "contactlist.searchContacts": "Buscar contactos", "contactlist.addNewContact": "Añadir nuevo contacto", "notes.ChoseColor": "Elige un color para tu nota", "notes.addNote": "<PERSON><PERSON><PERSON> nueva nota", "page404.title": "404", "page404.subTitle": "Parece que te has perdido", "page404.description": "La página que estás buscando no existe o se ha movido.", "page404.backButton": "VOLVER A LA CASA", "page500.title": "500", "page500.subTitle": "error de servidor interno", "page500.description": "Algo salió mal. Por favor   inténtelo de nuevo.", "page500.backButton": "VOLVER A LA CASA", "page.forgetPassTitle": "Isomórfico", "page.forgetPassSubTitle": "¿Se te olvidó tu contraseña?", "page.forgetPassDescription": "Introduzca su correo electrónico y le enviaremos un enlace de restablecimiento.", "page.sendRequest": "Enviar petición", "page.resetPassTitle": "Isomórfico", "page.resetPassSubTitle": "Restablecer la contraseña", "page.resetPassDescription": "Introduzca una nueva contraseña y confirme.", "page.resetPassSave": "<PERSON><PERSON>", "page.signInTitle": "Isomórfico", "page.signInRememberMe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "page.signInButton": "Registrarse", "page.signInPreview": "nombre de usuario  demo   contraseña  demodemo   o simplemente haga clic en cualquier botón.", "page.signInFacebook": "Iniciar sesión usando Facebook", "page.signInGooglePlus": "Acceder con Google Plus", "page.signInAuth0": "Iniciar se<PERSON><PERSON> con Auth0", "page.signInForgotPass": "Se te olvidó tu contraseña", "page.signInCreateAccount": "Crear una cuenta Isomorphoic", "page.signUpTitle": "Isomórfico", "page.signUpTermsConditions": "Estoy de acuerdo con los términos y condiciones", "page.signUpButton": "Regístrate", "page.signUpFacebook": "Registrate con Facebook", "page.signUpGooglePlus": "Regístrese con Google Plus", "page.signUpAuth0": "Regístrese con Auth0", "page.signUpAlreadyAccount": "¿Ya tienes una cuenta? Registrarse.", "widget.reportswidget.label": "Ingresos", "widget.reportswidget.details": "Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor", "widget.singleprogresswidget1.label": "<PERSON><PERSON><PERSON><PERSON>", "widget.singleprogresswidget2.label": "Addvertisement", "widget.singleprogresswidget3.label": "Consultante", "widget.singleprogresswidget4.label": "Desarrollo", "widget.stickerwidget1.number": "210", "widget.stickerwidget1.text": "Correo electrónico no leído", "widget.stickerwidget2.number": "1749", "widget.stickerwidget2.text": "Subida de imagen", "widget.stickerwidget3.number": "3024", "widget.stickerwidget3.text": "Total de mensajes", "widget.stickerwidget4.number": "54", "widget.stickerwidget4.text": "Pedidos", "widget.salewidget1.label": "Ingresos", "widget.salewidget1.price": "15000 $", "widget.salewidget1.details": "Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor", "widget.salewidget2.label": "Ingresos", "widget.salewidget2.price": "15000 $", "widget.salewidget2.details": "Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor", "widget.salewidget3.label": "Ingresos", "widget.salewidget3.price": "15000 $", "widget.salewidget3.details": "Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor", "widget.salewidget4.label": "Ingresos", "widget.salewidget4.price": "15000 $", "widget.salewidget4.details": "Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor", "widget.cardwidget1.number": "110", "widget.cardwidget1.text": "Nuevos mensajes", "widget.cardwidget2.number": "100%", "widget.cardwidget2.text": "Volumen", "widget.cardwidget3.number": "137", "widget.cardwidget3.text": "Logro", "widget.progresswidget1.label": "<PERSON><PERSON><PERSON>", "widget.progresswidget1.details": "50% Completo", "widget.progresswidget2.label": "Apoyo", "widget.progresswidget2.details": "80% de clientes satisfechos", "widget.progresswidget3.label": "Subir", "widget.progresswidget3.details": "65% Completo", "widget.vcardwidget.name": "<PERSON><PERSON>", "widget.vcardwidget.title": "<PERSON>. <PERSON>", "widget.vcardwidget.description": "Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed eiusmod tempor ammet dolar consectetur adipisicing elit", "checkout.billingform.firstname": "Nombre de pila", "checkout.billingform.lastname": "Apellido", "checkout.billingform.company": "nombre de empresa", "checkout.billingform.email": "Dirección de correo electrónico", "checkout.billingform.mobile": "No móviles", "checkout.billingform.country": "<PERSON><PERSON>", "checkout.billingform.city": "Ciudad", "checkout.billingform.address": "Dirección", "checkout.billingform.addressoptional": "Apartamento   suite   unidad   etc. (opcional  ", "checkout.billingform.checkbox": "¿Crea una cuenta?", "antTable.title.image": "Imagen", "antTable.title.firstName": "Nombre de pila", "antTable.title.lastName": "Apellido", "antTable.title.city": "Ciudad", "antTable.title.street": "Calle", "antTable.title.email": "Email", "antTable.title.dob": "DOB", "Map.leaflet.basicTitle": "Mapa básico", "Map.leaflet.basicMarkerTitle": "Mapa básico (con marcador predeterminado  ", "Map.leaflet.leafletCustomMarkerTitle": "Mapa básico (con marcador de icono personalizado  ", "Map.leaflet.leafletCustomHtmlMarkerTitle": "Mapa básico (con marcador HTML personalizado  ", "Map.leaflet.leafletMarkerClusterTitle": "Mapa básico (con grupo de marcadores  ", "Map.leaflet.leafletRoutingTitle": "Enrutamiento básico del mapa", "Component.contacts.noOption": "No se ha encontrado ningún contacto", "email.send": "ENVIAR", "email.cancel": "CANCELAR", "email.compose": "COMPONER", "email.noMessage": "Por favor seleccione un correo para leer", "themeSwitcher.purchase": "Compra ahora", "themeSwitcher.settings": "AJUSTES", "sidebar.frappeChart": "Frappe Charts", "topbar.help": "<PERSON><PERSON><PERSON>", "topbar.logout": "<PERSON><PERSON><PERSON>", "topbar.viewAll": "Ver todo", "topbar.viewCart": "Ver carro", "topbar.totalPrice": "Precio total"}