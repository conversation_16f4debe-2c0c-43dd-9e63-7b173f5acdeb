{"sidebar.email": "E-mail", "sidebar.ecommerce": "ecommerce", "sidebar.shop": "Negozio", "sidebar.cart": "<PERSON><PERSON>", "sidebar.checkout": "check-out", "sidebar.cards": "<PERSON><PERSON>", "sidebar.maps": "Mappe", "sidebar.googleMap": "Google Map", "sidebar.leafletMap": "Mappa del foglio", "sidebar.calendar": "Calendario", "sidebar.notes": "<PERSON><PERSON> appunti", "sidebar.todos": "Todos", "sidebar.contacts": "<PERSON><PERSON><PERSON>", "sidebar.shuffle": "rimescolare", "sidebar.charts": "<PERSON><PERSON>", "sidebar.googleCharts": "Google Carts", "sidebar.recharts": "Recharts", "sidebar.reactVis": "React Vis", "sidebar.reactChart2": "React-Chart-2", "sidebar.reactTrend": "React-Trend", "sidebar.eChart": "Echart", "sidebar.forms": "Forme", "sidebar.input": "Ingresso", "sidebar.editor": "editore", "sidebar.formsWithValidation": "Forme con validazione", "sidebar.progress": "Progresso", "sidebar.button": "Pulsante", "sidebar.tab": "linguetta", "sidebar.checkbox": "casella di controllo", "sidebar.radiobox": "radiobox", "sidebar.transfer": "Trasferimento", "sidebar.autocomplete": "Completamento automatico", "sidebar.boxOptions": "Opzioni casella", "sidebar.uiElements": "Elementi UI", "sidebar.badge": "Distintivo", "sidebar.card2": "Carta", "sidebar.corusel": "gozzoviglia", "sidebar.collapse": "Crollo", "sidebar.popover": "Pop Over", "sidebar.tooltip": "tooltip", "sidebar.tag": "<PERSON><PERSON><PERSON><PERSON>", "sidebar.timeline": "Sequenza temporale", "sidebar.dropdown": "Cadere in picchiata", "sidebar.pagination": "paginatura", "sidebar.rating": "Valutazione", "sidebar.tree": "Albero", "sidebar.advancedElements": "Elementi avanzati", "sidebar.reactDates": "Date di reazione", "sidebar.codeMirror": "S<PERSON><PERSON><PERSON> di codice", "sidebar.uppy": "Uploader Uppy", "sidebar.dropzone": "Zona di rilascio", "sidebar.feedback": "Risposta", "sidebar.alert": "Mettere in guardia", "sidebar.modal": "Modale", "sidebar.message": "Messaggio", "sidebar.notification": "Notifica", "sidebar.popConfirm": "Pop <PERSON>fer<PERSON>", "sidebar.spin": "Roteare", "sidebar.tables": "tabelle", "sidebar.antTables": "Ant Table", "sidebar.pages": "pagine", "sidebar.500": "500", "sidebar.404": "404", "sidebar.signIn": "Registrati", "sidebar.signUp": "Registrazione", "sidebar.forgotPw": "Hai dimenticato le password", "sidebar.resetPw": "Azzerare le password", "sidebar.invoice": "Fattura", "sidebar.menuLevels": "Livelli del menu", "sidebar.item1": "Voce 1", "sidebar.item2": "Articolo 2", "sidebar.option1": "opzione 1", "sidebar.option2": "Opzione 2", "sidebar.option3": "Opzione 3", "sidebar.option4": "Opzione 4", "sidebar.blankPage": "<PERSON><PERSON><PERSON> vuota", "sidebar.githubSearch": "Ricerca Github", "sidebar.youtubeSearch": "Cambia lingua", "languageSwitcher.label": "Switcher di temi", "themeSwitcher": "Sidebar", "themeSwitcher.Sidebar": "topbar", "themeSwitcher.Topbar": "sfondo", "themeSwitcher.Background": "Titolo di base", "feedback.alert.basicTitle": "Testo di successo", "feedback.alert.successText": "<PERSON><PERSON>", "feedback.alert.infoText": "Testo di avviso", "feedback.alert.warningText": "Testo di errore", "feedback.alert.errorText": "Tipo avvisi chiudibili", "feedback.alert.closableAlertType": "Tipo di avviso di icone", "feedback.alert.iconAlertType": "Tipo di avviso di informazioni sullicona", "feedback.alert.iconInfoAlertType": "suggerimenti di successo", "feedback.alert.successTips": "Descrizione dettagliata e consigli su copywriting di successo.", "feedback.alert.successTipsDescription": "Note informative", "feedback.alert.informationTips": "Descrizione e informazioni aggiuntive su copywriting.", "feedback.alert.informationDescription": "avvertimento", "feedback.alert.warningTips": "Questo è un avviso di avviso di copywriting.", "feedback.alert.warningDescription": "Errore", "feedback.alert.errorTips": "Si tratta di un messaggio di errore relativo a copywriting.", "feedback.alert.errorDescription": "Modale con personalizzazione di piè di pagina", "feedback.alert.modalTitle": "Dialogo modale di base.", "feedback.alert.modalSubTitle": "Successo", "feedback.alert.successTitle": "Informazioni", "feedback.alert.infoTitle": "Errore", "feedback.alert.errorTitle": "avvertimento", "feedback.alert.warningTitle": "Modale", "feedback.alert.modalBlockTitle": "Finestra di dialogo Modalità di conferma", "feedback.alert.confirmationModalDialogue": "Semplice dialogo modale", "feedback.alert.simpleModalDialogue": "Messaggio", "feedback.alert.message": "Messaggio normale", "feedback.alert.normalMessageTitle": "Messaggi normali come feedback.", "feedback.alert.normalMessageSubtitle": "Visualizza il messaggio normale", "feedback.alert.displayMessage": "Altri tipi di messaggio", "feedback.alert.displayOtherTypeMessageTitle": "Messaggi di tipo di successo   di errore e di avviso.", "feedback.alert.displayOtherTypeMessageSubTitle": "Personalizza durata", "feedback.alert.customizeDurationTitle": "personalizzare la durata della visualizzazione dei messaggi da default da 1.5s a 10s.", "feedback.alert.customizeDurationSubTitle": "Durata del display personalizzata", "feedback.alert.customizeDurationButton": "Messaggio di caricamento", "feedback.alert.messageLoadingTitle": "Visualizzare un indicatore globale di caricamento   che viene eliminato in modo sincrono.", "feedback.alert.messageLoadingSubTitle": "Visualizzare un indicatore di caricamento", "feedback.alert.displayLoadIndicator": "Notifica", "feedback.alert.notification": "Di base", "feedback.alert.notificationBasicTitle": "<PERSON><PERSON><PERSON><PERSON> più semplice che chiude la casella di notifica dopo 4.5s.", "feedback.alert.notificationBasicSubTitle": "Aprire la casella di notifica", "feedback.alert.notificationBasicDescription": "Durata dopo la chiusura della casella di notifica", "feedback.alert.notificationDurationTitle": "La durata può essere utilizzata per specificare la durata della notifica rimanere aperta. Dopo la scadenza della durata   la notifica si chiude automaticamente. Se non è specificato   il valore predefinito è di 4  5 secondi. Se si imposta il valore su 0   la casella di notifica non si chiude automaticamente.", "feedback.alert.notificationDurationSubTitle": "Notifica con icona", "feedback.alert.notificationwithIconTitle": "Una casella di notifica con unicona sul lato sinistro.", "feedback.alert.notificationwithIconSubTitle": "Notifica con icona personalizzata", "feedback.alert.notificationwithCustomIconTitle": "Messaggi normali come feedback.", "feedback.alert.notificationwithCustomIconSubTitle": "Notifica con il pulsante personalizzato", "feedback.alert.notificationwithCustomButtonTitle": "Messaggi normali come feedback.", "feedback.alert.notificationwithCustomButtonSubTitle": "Pop <PERSON>fer<PERSON>", "feedback.alert.popConfirm": "Conferma fondamentale", "feedback.alert.popConfirm.basicTitle": "Lesempio di base.", "feedback.alert.popConfirm.basicSubTitle": "Elimina", "feedback.alert.popConfirm.delete": "Notifica con icona personalizzata", "feedback.alert.popConfirm.notiWithIconTitle": "Messaggi normali come feedback.", "feedback.alert.popConfirm.notiWithIconSubTitle": "TL", "feedback.alert.popConfirm.TL": "Superiore", "feedback.alert.popConfirm.top": "TR", "feedback.alert.popConfirm.TR": "LT", "feedback.alert.popConfirm.LT": "Sinistra", "feedback.alert.popConfirm.left": "LIBBRE", "feedback.alert.popConfirm.LB": "RT", "feedback.alert.popConfirm.RT": "Destra", "feedback.alert.popConfirm.right": "RB", "feedback.alert.popConfirm.RB": "BL", "feedback.alert.popConfirm.Bl": "Parte inferiore", "feedback.alert.popConfirm.bottom": "BR", "feedback.alert.popConfirm.BR": "Roteare", "feedback.alert.spin": "Dimensioni Spin", "feedback.alert.spin.basicTitle": "Spin con lo sfondo", "feedback.alert.spin.background": "Spin con descrizione di sfondo", "feedback.alert.spin.backgroundDescription": "Stato di caricamento", "feedback.alert.spin.loadingState": "Titolo del messaggio di avviso", "feedback.alert.spin.alertTitle": "Ulteriori dettagli sul contesto di questo avviso.", "feedback.alert.spin.alertDescription": "Ingresso", "forms.input.header": "Utilizzo di base", "forms.input.basicTitle": "Esempio di utilizzo di base.", "forms.input.basicSubTitle": "Tre formati di ingresso", "forms.input.variationsTitle": "Sono disponibili tre dimensioni di una casella Input  grande (42px     predefinito (35px   e piccolo (30px  . Nota  Allinterno delle forme viene utilizzata solo la grande dimensione.", "forms.input.variationsSubtitle": "Gruppo di input", "forms.input.groupTitle": "Esempio di input.Group Nota  Non è necessario Col per controllare la larghezza nella modalità compatta.", "forms.input.groupSubTitle": "Autosizing laltezza per adattarsi al contenuto", "forms.input.autoSizingTitle": "autosize prop per un tipo di textarea dellinput rende laltezza regolabile automaticamente in base al contenuto. Può essere fornito un oggetto opzioni per autosizzare per specificare il numero minimo e massimo di righe che larea textarea regolerà automaticamente.", "forms.input.autoSizingSubTitle": "Scheda Pre    Post", "forms.input.prePostTabTitle": "Utilizzo di pre & amp; esempi di tabulazioni post ..", "forms.input.prePostTabSubTitle": "Textarea", "forms.input.textAreaTitle": "Per i casi di input utente multi-line   è possibile utilizzare un input il cui tipo prop ha il valore di textarea.", "forms.input.textAreaSubTitle": "Ricerca", "forms.input.searchTitle": "Esempio di creazione di una casella di ricerca raggruppando un input standard con un pulsante di ricerca", "forms.input.searchSubTitle": "editore", "forms.editor.header": "Modulo di convalida personalizzata", "forms.formsWithValidation.header": "Fallire", "forms.formsWithValidation.failLabel": "Dovrebbe essere combinazione di numeri & amp; alfabeti", "forms.formsWithValidation.failHelp": "avvertimento", "forms.formsWithValidation.warningLabel": "Convalida", "forms.formsWithValidation.ValidatingLabel": "Le informazioni vengono convalidate ...", "forms.formsWithValidation.ValidatingHelp": "Successo", "forms.formsWithValidation.SuccessLabel": "avvertimento", "forms.formsWithValidation.WarninghasFeedbackLabel": "Fallire", "forms.formsWithValidation.FailhasFeedbackLabel": "Dovrebbe essere combinazione di numeri & amp; alfabeti", "forms.formsWithValidation.FailhasFeedbackHelp": "Barra di avanzamento", "forms.progressBar.header": "Barra di avanzamento", "forms.progressBar.standardTitle": "Una barra di avanzamento standard.", "forms.progressBar.standardSubTitle": "Barra di progressione circolare", "forms.progressBar.circularTitle": "Una barra di avanzamento circolare.", "forms.progressBar.circularSubTitle": "Barra di avanzamento di taglia minima", "forms.progressBar.miniTitle": "Adatto per una zona stretta.", "forms.progressBar.miniSubTitle": "Una barra di avanzamento circolare più piccola.", "forms.progressBar.miniCircularTitle": "Barra di avanzamento circolare dinamica", "forms.progressBar.dynamicCircularTitle": "Una barra dinamica di avanzamento è migliore.", "forms.progressBar.dynamicCircularSubTitle": "Formato di testo personalizzato", "forms.progressBar.customTextTitle": "È possibile formattare il testo personalizzato impostando il formato.", "forms.progressBar.customTextSubTitle": "Cruscotto", "forms.progressBar.dashboardTitle": "Uno stile del cruscotto del progresso.", "forms.progressBar.dashboardSubTitle": "pulsanti", "forms.button.header": "Tipo di pulsante", "forms.button.simpleButton": "Icona pulsante", "forms.button.iconButton": "Primario", "forms.button.simpleButtonPrimaryText": "Predefinito", "forms.button.simpleButtonDefaultText": "trat<PERSON><PERSON><PERSON><PERSON>", "forms.button.simpleButtonDashedText": "<PERSON><PERSON><PERSON>", "forms.button.simpleButtonDangerText": "ricerca", "forms.button.iconPrimaryButton": "ricerca", "forms.button.iconSimpleButton": "ricerca", "forms.button.iconCirculerButton": "ricerca", "forms.button.iconDashedButton": "Dimensioni del pulsante", "forms.button.SizedButton": "Pulsante disabilitato", "forms.button.DisabledButton": "Caricamento del tasto", "forms.button.LoadingButton": "Pulsante multiplo", "forms.button.MultipleButton": "Gruppo di pulsanti", "forms.button.groupButton": "Tabs", "forms.Tabs.header": "ricerca", "forms.Tabs.simpleTabTitle": "Schede disattivate", "forms.Tabs.simpleTabSubTitle": "Tabulazioni delle icone", "forms.Tabs.iconTabTitle": "<PERSON> schede", "forms.Tabs.miniTabTitle": "Schede Azione Extra", "forms.Tabs.extraTabTitle": "Posizione", "forms.Tabs.TabpositionTitle": "Posizione delle schede  sinistra   destra   superiore o inferiore", "forms.Tabs.TabpositionSubTitle": "Schede del tipo di scheda", "forms.Tabs.cardTitle": "Aggiungi e chiudi le schede", "forms.Tabs.editableTitle": "Schede di tipo verticale", "forms.Tabs.verticalTitle": "Schede di base", "forms.Tabs.basicTitle": "casella di controllo", "forms.checkbox.header": "Casella di controllo di base", "forms.checkbox.basicTitle": "Utilizzo di base della casella di controllo.", "forms.checkbox.basicSubTitle": "Gruppo di casella di controllo", "forms.checkbox.groupTitle": "Generare un gruppo di caselle di controllo da un array. Utilizza disabilitato per disattivare una casella di controllo.", "forms.checkbox.groupSubTitle": "Gruppo di casella di controllo", "forms.checkbox.groupCheckTitle": "Generare un gruppo di caselle di controllo da un array. Utilizza disabilitato per disattivare una casella di controllo.", "forms.checkbox.groupCheckSubTitle": "Radio", "forms.radio.header": "Radio di base", "forms.radio.simpleTitle": "Luso più semplice. Usare disabilitato per disattivare una radio.", "forms.radio.simpleSubTitle": "RadioGroup verticale", "forms.radio.groupTitle": "RadioGroup verticale   con più radio.", "forms.radio.groupSubTitle": "RadioGroup", "forms.radio.groupSecondTitle": "Un gruppo di componenti radio.", "forms.radio.groupSecondSubTitle": "RadioGroup", "forms.radio.groupThirdTitle": "Un gruppo di componenti radio.", "forms.radio.groupThirdSubTitle": "Trasferimento", "forms.transfer.header": "Trasferisci con una casella di ricerca.", "forms.transfer.SubTitle": "Ricerca", "forms.transfer.Title": "Completamento automatico", "forms.autocomplete.header": "su misura", "forms.autocomplete.simpleTitle": "Potresti passare AutoComplete.Option come bambini di AutoComplete   invece di utilizzare dataSource", "forms.autocomplete.simpleSubTitle": "Personalizza componente di input", "forms.autocomplete.customizeTitle": "Personalizza componente di input", "forms.autocomplete.customizeSubTitle": "Distintivo", "uiElements.badge.badge": "Esempio di base", "uiElements.badge.basicExample": "Uso più semplice. Il distintivo sarà nascosto quando il conteggio è 0   ma possiamo usare showZero per mostrarlo.", "uiElements.badge.basicExampleSubTitle": "Numero di overflow", "uiElements.badge.overflowCount": "OverflowCount viene visualizzato quando il conteggio è maggiore di overflowCount. Il valore predefinito di overflowCount è 99.", "uiElements.badge.overflowCountSubTitle": "Stato", "uiElements.badge.status": "Distintivo autonomo con stato.", "uiElements.badge.statusSubTitle": "Successo", "uiElements.badge.success": "Errore", "uiElements.badge.error": "Predefinito", "uiElements.badge.default": "lavorazione", "uiElements.badge.processing": "avvertimento", "uiElements.badge.warning": "Distintivo rosso", "uiElements.badge.redBadge": "Questo mostrerà semplicemente un distintivo rosso   senza un conteggio specifico.", "uiElements.badge.redBadgeSubTitle": "Collegare qualcosa", "uiElements.badge.linkSomething": "<PERSON><PERSON>", "uiElements.cards.cards": "Scheda di base", "uiElements.cards.basicCard": "Una scheda di base contenente un titolo   un contenuto e un contenuto aggiuntivo dangolo.", "uiElements.cards.basicCardSubTitle": "Di <PERSON>", "uiElements.cards.more": "<PERSON><PERSON>a", "uiElements.cards.cardTitle": "Contenuto della scheda", "uiElements.cards.cardContent": "Il peso del peso è ridotto   lelit di adipisizione del consectetur   che rende meno efficace il lavoro e la dolce magna aliqua. Lut enim ad minim veniam   quis nostrud esercizio ullamco laboris nisi ut aliquip ex ea commodo consequat.", "uiElements.cards.lorem": "<PERSON><PERSON><PERSON> bordo", "uiElements.cards.noBorder": "Una carta senza bordi su uno sfondo grigio.", "uiElements.cards.noBorderSubTitle": "Scheda di griglia", "uiElements.cards.gridCard": "Carte di solito cooperano con il layout della griglia nella pagina di panoramica.", "uiElements.cards.gridCardSubTitle": "Caricamento della carta", "uiElements.cards.loadingCard": "Mostra un indicatore di caricamento durante il recupero del contenuto della scheda.", "uiElements.cards.loadingCardSubTitle": "<PERSON><PERSON><PERSON><PERSON> contenuto", "uiElements.cards.whateverContent": "<PERSON><PERSON><PERSON>", "uiElements.cards.customizedContentTitle": "Mostra un indicatore di caricamento durante il recupero del contenuto della scheda.", "uiElements.cards.customizedContent": "Europa Street beat", "uiElements.cards.europeStreetBeat": "www.instagram.com", "uiElements.cards.instagram": "gozzoviglia", "uiElements.carousel.carousel": "Carosello verticale", "uiElements.carousel.verticalCarousel": "Pagination verticale. utilizzare   vertical = true", "uiElements.carousel.verticalCarouselSubTitle": "Carosello di base", "uiElements.carousel.basicCarousel": "Utilizzo di base", "uiElements.carousel.basicCarouselSubTitle": "Fade in transizione", "uiElements.carousel.fadeInTransition": "Le diapositive utilizzano dissolvenza per la transizione.   Effetto = dissolvenza", "uiElements.carousel.fadeInTransitionSubTitle": "Scorri automaticamente", "uiElements.carousel.scrollAutomatically": "Timing di scorrimento alla scheda    immagine successiva. riproduzione automatica", "uiElements.carousel.scrollAutomaticallySubTitle": "Crollo", "uiElements.collapse.collapse": "Più di un pannello può essere espanso alla volta   il primo pannello viene inizializzato per essere attivo in questo caso. utilizzare   defaultActiveKey =   [keyNum]", "uiElements.collapse.collapseSubTitle": "Un cane è un tipo di animale domestico. Conosciuto per la sua fedeltà e fedeltà   si può trovare come un ospite benvenuto in molte famiglie in tutto il mondo.", "uiElements.collapse.text": "Questa è lintestazione del pannello 1", "uiElements.collapse.headerOne": "Questa è lintestazione del pannello 2", "uiElements.collapse.headerTwo": "Questa è lintestazione del pannello 3", "uiElements.collapse.headerThree": "Questo è il pannello nido del pannello", "uiElements.collapse.headerNested": "Esempio nidificato", "uiElements.collapse.nestedExample": "Il crollo è nidificato allinterno del Collapse.", "uiElements.collapse.nestedExampleSubTitle": "Esempio senza bordi", "uiElements.collapse.borderlessExample": "Uno stile senza bordo di Collapse. utilizzare   bordered =   false", "uiElements.collapse.borderlessExampleSubTitle": "Fisarmonica", "uiElements.collapse.accordion": "Modalità fisarmonica   è possibile espandere un solo pannello alla volta. Il primo pannello verrà espanso per impostazione predefinita. utilizzare la fisarmonica", "uiElements.collapse.accordionSubTitle": "popover", "uiElements.popover.popover": "Esempio di base", "uiElements.popover.basicExample": "Lesempio più semplice. La dimensione dello strato galleggiante dipende dalla regione dei contenuti.", "uiElements.popover.basicExampleSubTitle": "Allontanami", "uiElements.popover.hoverMe": "<PERSON><PERSON>", "uiElements.popover.title": "Tre modi per attivare", "uiElements.popover.titleTrigger": "Mouse per fare clic   concentrarsi e muoversi.", "uiElements.popover.titleTriggerSubTitle": "Mi concentri", "uiElements.popover.focusMe": "<PERSON><PERSON><PERSON><PERSON>", "uiElements.popover.clickMe": "Posizionamento", "uiElements.popover.placement": "Sono disponibili 12 opzioni di posizionamento.", "uiElements.popover.placementSubTitle": "Superiore", "uiElements.popover.top": "A sinistra in alto", "uiElements.popover.topLeft": "In alto a destra", "uiElements.popover.topRight": "In alto a sinistra", "uiElements.popover.leftTop": "Sinistra", "uiElements.popover.left": "Sinistra inferiore", "uiElements.popover.leftBottom": "Destra destra", "uiElements.popover.rightTop": "Destra", "uiElements.popover.right": "Parte inferiore", "uiElements.popover.bottom": "In basso a sinistra", "uiElements.popover.bottomLeft": "In basso a destra", "uiElements.popover.bottomRight": "Controllare la chiusura della finestra di dialogo", "uiElements.popover.boxTitle": "Utilizzare un supporto visibile per controllare la visualizzazione della scheda.", "uiElements.popover.boxSubTitle": "TR", "uiElements.popover.TR": "TL", "uiElements.popover.TL": "LT", "uiElements.popover.LT": "LIBBRE", "uiElements.popover.LB": "RT", "uiElements.popover.RT": "RB", "uiElements.popover.RB": "BL", "uiElements.popover.BL": "BR", "uiElements.popover.BR": "<PERSON><PERSON>", "uiElements.popover.close": "tooltip", "uiElements.tooltip.tooltip": "Contenuto del Tooltip", "uiElements.tooltip.tooltipContent": "Esempio di base", "uiElements.tooltip.basicExample": "<PERSON>so più semplice.", "uiElements.tooltip.basicExampleSubTitle": "Posizionamento", "uiElements.tooltip.placementTitle": "La ToolTip ha 12 scelta dei posizionamenti.", "uiElements.tooltip.placementSubTitle": "TL", "uiElements.tooltip.TL": "TR", "uiElements.tooltip.TR": "LT", "uiElements.tooltip.LT": "LIBBRE", "uiElements.tooltip.LB": "RT", "uiElements.tooltip.RT": "RB", "uiElements.tooltip.RB": "BL", "uiElements.tooltip.BL": "BR", "uiElements.tooltip.BR": "Parte inferiore", "uiElements.tooltip.bottom": "Destra", "uiElements.tooltip.right": "Sinistra", "uiElements.tooltip.left": "Superiore", "uiElements.tooltip.top": "Tooltip verrà mostrato quando il mouse entra.", "uiElements.tooltip.tooltipContentSpan": "Contenuto del Tooltip", "uiElements.tooltip.contentSpan": "tag", "uiElements.tags.tags": "Esempio di base", "uiElements.tags.basicExample": "Utilizzo di Tag di base e potrebbe essere chiuso da una proprietà chiusa configurabile. Tag Closable supporta onClose afterClose eventi.", "uiElements.tags.basicExampleSubTitle": "Tag 1", "uiElements.tags.tagOne": "Tag 2", "uiElements.tags.tagTwo": "collegamento", "uiElements.tags.link": "Impedire il default", "uiElements.tags.preventDefault": "Tag colorato", "uiElements.tags.colorfulTag": "Tag Hot", "uiElements.tags.hotTags": "Seleziona i tuoi argomenti preferiti.", "uiElements.tags.hotTagsSubTitle": "Hots", "uiElements.tags.hots": "Aggiungi e rimuove in modo dinamico", "uiElements.tags.addRemoveDynamically": "La generazione di un insieme di tag per array consente di aggiungere e rimuovere in modo dinamico. Il suo è basato sullevento afterClose   che verrà attivato mentre la fine animazione fine.", "uiElements.tags.addRemoveDynamicallySubTitle": "+ Nuovo tag", "uiElements.tags.newTag": "Sequenza temporale", "uiElements.timeline.timeline": "Esempio di base", "uiElements.timeline.basicExample": "Timeline di base", "uiElements.timeline.basicTimeline": "<PERSON><PERSON>imo nodo", "uiElements.timeline.lastNode": "Quando la timeline è incompleta e in corso   infine   metti un nodo fantasma. impostare   in attesa =   true     o   in attesa =   a React Element", "uiElements.timeline.lastNodeContent": "<PERSON><PERSON><PERSON>", "uiElements.timeline.seeMore": "costume", "uiElements.timeline.custom": "Imposta un nodo come unicona o un altro elemento personalizzato.", "uiElements.timeline.customContent": "Esempio di colore", "uiElements.timeline.colorExample": "Imposta il colore dei cerchi. verde significa stato completato o successo   rosso significa avvertimento o errore   e blu significa stato continuo o altro.", "uiElements.timeline.colorExampleContent": "Creare un sito di servizi per il 2015-09-01", "uiElements.timeline.createServiceSite": "Risolvere i problemi di rete iniziali dal 2015-09-01", "uiElements.timeline.solveInitialNetwork": "Problemi di rete risolti 2015-09-01", "uiElements.timeline.networkProblemSolved": "Test tecnici del 2015-09-01", "uiElements.timeline.technicalTesting": "Cadere in picchiata", "uiElements.dropdown.dropdown": "Disattiva a discesa", "uiElements.dropdown.hoverDropdown": "Allontanami", "uiElements.dropdown.hoverMe": "Posizionamento di posizionamento a discesa", "uiElements.dropdown.hoverPlacement": "Sospendi con il disattivato link", "uiElements.dropdown.hoverDisableLink": "Clicca su Drop Down", "uiElements.dropdown.clickedDropdown": "Pulsante con menu a discesa", "uiElements.dropdown.buttonDropdown": "paginatura", "uiElements.pagination.pagination": "Di base", "uiElements.pagination.basic": "Di <PERSON>", "uiElements.pagination.more": "Changer", "uiElements.pagination.changer": "Saltatore", "uiElements.pagination.jumper": "Mini formato", "uiElements.pagination.miniSize": "Modalità semplice", "uiElements.pagination.simpleMode": "Controlled", "uiElements.pagination.controlled": "Numero totale", "uiElements.pagination.totalNumber": "Valutazione", "uiElements.rating.rating": "Esempio di base", "uiElements.rating.basicExample": "<PERSON>so più semplice.", "uiElements.rating.basicExampleSubTitle": "Metà stella", "uiElements.rating.halfStar": "Sostenere selezionare la metà della stella.", "uiElements.rating.halfStarSubTitle": "Mostra copywriting", "uiElements.rating.showCopywriting": "Aggiungi copywriting in componenti di velocità.", "uiElements.rating.showCopywritingSubTitle": "Sola lettura", "uiElements.rating.readOnly": "Leggi solo   non può utilizzare il mouse per interagire.", "uiElements.rating.readOnlySubTitle": "Altro carattere", "uiElements.rating.otherCharacter": "Sostituire la stella predefinita in altri caratteri come lalfabeto   la cifra   licona o anche la parola cinese.", "uiElements.rating.otherCharacterSubTitle": "Albero", "uiElements.tree.tree": "Esempio di base", "uiElements.tree.basicExample": "Lutil<PERSON>zo più semplice   ti dice come utilizzare controllibili   selezionabili   disattivati   defaultExpandKeys e così via.", "uiElements.tree.basicExampleSubTitle": "Esempio controllato di base", "uiElements.tree.basicControlledExample": "esempio controllato di base", "uiElements.tree.basicControlledExampleSubTitle": "Esempio Draggable", "uiElements.tree.draggableExample": "Trascinare alberoNode da inserire dopo laltro alberoNodo o inserire nellaltro albero TreeNode.", "uiElements.tree.draggableExampleSubTitle": "Caricare i dati in modo asincrono", "uiElements.tree.loadAsync": "Per caricare i dati in modo asincrono quando si fa clic per espandere un alberoNodo.", "uiElements.tree.loadAsyncSubTitle": "Esempio esplorabile", "uiElements.tree.searchableExample": "Albero ricercabile", "uiElements.tree.searchableExampleSubTitle": "Albero Con Linea", "uiElements.tree.treeWithLine": "Netscape 2.0 viene fornito   introducendo Javascript", "shuffle.descriptionOne": "<PERSON> rilascia AJAX spec", "shuffle.descriptionTwo": "jQuery 1.0 è stato rilasciato", "shuffle.descriptionThree": "Prima sottolineare. Commit", "shuffle.descriptionFour": "Backbone.js diventa una cosa", "shuffle.descriptionFive": "Angular 1.0 è stato rilasciato", "shuffle.descriptionSix": "React è aperto; gli sviluppatori si rallegrano", "shuffle.descriptionSeven": "Elenco", "toggle.list": "Griglia", "toggle.grid": "Ascendente", "toggle.ascending": "<PERSON><PERSON><PERSON>", "toggle.descending": "rimescolare", "toggle.shuffle": "<PERSON><PERSON><PERSON><PERSON>", "toggle.rotate": "Aggiungi articolo", "toggle.addItem": "<PERSON><PERSON><PERSON><PERSON>", "toggle.removeItem": "Cerca i contatti", "contactlist.searchContacts": "Aggiungi nuovo contatto", "contactlist.addNewContact": "Scegli un colore per la tua nota", "notes.ChoseColor": "Aggiungi nuova nota", "notes.addNote": "404", "page404.title": "Se<PERSON>ra che ti sei perso", "page404.subTitle": "La pagina che stai cercando non esiste o è stata spostata.", "page404.description": "RITORNO A CASA", "page404.backButton": "500", "page500.title": "Errore interno del server", "page500.subTitle": "Qualcosa è andato storto. Riprova la lettera.", "page500.description": "RITORNO A CASA", "page500.backButton": "isomorfo", "page.forgetPassTitle": "Ha dimenticato la password?", "page.forgetPassSubTitle": "Inserisci la tua email e ti inviamo un collegamento di ripristino.", "page.forgetPassDescription": "Invia richiesta", "page.sendRequest": "isomorfo", "page.resetPassTitle": "<PERSON><PERSON><PERSON> la <PERSON>", "page.resetPassSubTitle": "Inserire una nuova password e confermarla.", "page.resetPassDescription": "<PERSON><PERSON><PERSON>", "page.resetPassSave": "isomorfo", "page.signInTitle": "Ricordati di me", "page.signInRememberMe": "registrati", "page.signInButton": "username  demo   password  demodemo   o basta cliccare su qualsiasi pulsante.", "page.signInPreview": "Accedi con Facebook", "page.signInFacebook": "Accedi con Google Plus", "page.signInGooglePlus": "Accedi con Auth0", "page.signInAuth0": "Ha dimenticato la password", "page.signInForgotPass": "Crea un account Isomorphoic", "page.signInCreateAccount": "isomorfo", "page.signUpTitle": "Sono da<PERSON>rdo con i termini e le condivisioni", "page.signUpTermsConditions": "Registrazione", "page.signUpButton": "Registrati con Facebook", "page.signUpFacebook": "Registrati con Google Plus", "page.signUpGooglePlus": "Registrati con Auth0", "page.signUpAuth0": "Hai già un account? Registrati.", "page.signUpAlreadyAccount": "Reddito", "widget.reportswidget.label": "Il suo peso è ridotto   è aumentato   è aumentato", "widget.reportswidget.details": "Marketing", "widget.singleprogresswidget1.label": "Addvertisement", "widget.singleprogresswidget2.label": "Consulenza", "widget.singleprogresswidget3.label": "<PERSON><PERSON><PERSON><PERSON>", "widget.singleprogresswidget4.label": "210", "widget.stickerwidget1.number": "Email non letti", "widget.stickerwidget1.text": "1749", "widget.stickerwidget2.number": "Upload di immagini", "widget.stickerwidget2.text": "3024", "widget.stickerwidget3.number": "Messaggio totale", "widget.stickerwidget3.text": "54", "widget.stickerwidget4.number": "Ordini Post", "widget.stickerwidget4.text": "Reddito", "widget.salewidget1.label": "$ 15000", "widget.salewidget1.price": "Il suo peso è ridotto   è aumentato   è aumentato", "widget.salewidget1.details": "Reddito", "widget.salewidget2.label": "$ 15000", "widget.salewidget2.price": "Il suo peso è ridotto   è aumentato   è aumentato", "widget.salewidget2.details": "Reddito", "widget.salewidget3.label": "$ 15000", "widget.salewidget3.price": "Il suo peso è ridotto   è aumentato   è aumentato", "widget.salewidget3.details": "Reddito", "widget.salewidget4.label": "$ 15000", "widget.salewidget4.price": "Il suo peso è ridotto   è aumentato   è aumentato", "widget.salewidget4.details": "110", "widget.cardwidget1.number": "<PERSON><PERSON><PERSON> messaggi", "widget.cardwidget1.text": "100%", "widget.cardwidget2.number": "Volume", "widget.cardwidget2.text": "137", "widget.cardwidget3.number": "realizzazione", "widget.cardwidget3.text": "Scaricare", "widget.progresswidget1.label": "50% completato", "widget.progresswidget1.details": "Supporto", "widget.progresswidget2.label": "80% <PERSON><PERSON><PERSON>", "widget.progresswidget2.details": "Caricare", "widget.progresswidget3.label": "65% completato", "widget.progresswidget3.details": "<PERSON><PERSON>", "widget.vcardwidget.name": "Sr. iOS Developer", "widget.vcardwidget.title": "Il peso è ridotto   il prezzo è basso   il prezzo è basso   il prezzo è basso", "widget.vcardwidget.description": "Nome di battesimo", "checkout.billingform.firstname": "Cognome", "checkout.billingform.lastname": "Nome della ditta", "checkout.billingform.company": "Indirizzo email", "checkout.billingform.email": "Mobile no", "checkout.billingform.mobile": "<PERSON><PERSON>", "checkout.billingform.country": "Città", "checkout.billingform.city": "<PERSON><PERSON><PERSON><PERSON>", "checkout.billingform.address": "Appartamento   suite   unità ecc. (Opzionale", "checkout.billingform.addressoptional": "Crea un account?", "checkout.billingform.checkbox": "<PERSON><PERSON><PERSON><PERSON>", "antTable.title.image": "Nome di battesimo", "antTable.title.firstName": "Cognome", "antTable.title.lastName": "Città", "antTable.title.city": "strada", "antTable.title.street": "E-mail", "antTable.title.email": "DOB", "antTable.title.dob": "Mappa di base", "Map.leaflet.basicTitle": "Mappa di base (con il marcatore di default", "Map.leaflet.basicMarkerTitle": "Mappa di base (con il simbolo personalizzato dellicona", "Map.leaflet.leafletCustomMarkerTitle": "Mappa di base (con il personalizzatore Html Marker", "Map.leaflet.leafletCustomHtmlMarkerTitle": "Mappa di base (con cluster di marcatori", "Map.leaflet.leafletMarkerClusterTitle": "Routing della mappa di base", "Map.leaflet.leafletRoutingTitle": "<PERSON><PERSON><PERSON> contatto trovato", "Component.contacts.noOption": "INVIARE", "email.send": "ANNULLA", "email.cancel": "COMPORRE", "email.compose": "Seleziona una mail per leggere", "email.noMessage": "PAGARE ORA", "themeSwitcher.purchase": "IMPOSTAZIONI", "themeSwitcher.settings": "Selezionare", "sidebar.selectbox": "", "sidebar.frappeChart": "Frappe Charts", "topbar.help": "<PERSON><PERSON>", "topbar.logout": "<PERSON>nne<PERSON><PERSON>", "topbar.viewAll": "Guarda tutto", "topbar.viewCart": "Visualizza carrello", "topbar.totalPrice": "Prezzo totale"}