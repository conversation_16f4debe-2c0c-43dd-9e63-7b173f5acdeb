{"sidebar.email": "电子邮件", "sidebar.ecommerce": "电子商务", "sidebar.shop": "店", "sidebar.cart": "大车", "sidebar.checkout": "查看", "sidebar.cards": "牌", "sidebar.maps": "地图", "sidebar.googleMap": "谷歌地图", "sidebar.leafletMap": "传单地图", "sidebar.calendar": "日历", "sidebar.notes": "笔记", "sidebar.todos": "待办事项", "sidebar.contacts": "往来", "sidebar.shuffle": "拖曳", "sidebar.charts": "图表", "sidebar.googleCharts": "Google购物车", "sidebar.recharts": "Recharts", "sidebar.reactVis": "反应可见", "sidebar.reactChart2": "反应 - 图2", "sidebar.reactTrend": "反应-趋势", "sidebar.eChart": "Echart", "sidebar.forms": "形式", "sidebar.input": "输入", "sidebar.editor": "编辑", "sidebar.formsWithValidation": "验证形式", "sidebar.progress": "进展", "sidebar.button": "按键", "sidebar.tab": "标签", "sidebar.checkbox": "复选框", "sidebar.radiobox": "Radiobox", "sidebar.transfer": "转让", "sidebar.autocomplete": "自动完成", "sidebar.boxOptions": "框选项", "sidebar.uiElements": "UI元素", "sidebar.badge": "徽章", "sidebar.card2": "卡", "sidebar.corusel": "狂欢游行", "sidebar.collapse": "坍方", "sidebar.popover": "流行", "sidebar.tooltip": "提示", "sidebar.tag": "标签", "sidebar.timeline": "时间线", "sidebar.dropdown": "落下", "sidebar.pagination": "分页", "sidebar.rating": "评分", "sidebar.tree": "树", "sidebar.advancedElements": "高级元素", "sidebar.reactDates": "反应日期", "sidebar.codeMirror": "代码镜", "sidebar.uppy": "Uppy上传器", "sidebar.dropzone": "拖放区", "sidebar.feedback": "反馈", "sidebar.alert": "警报", "sidebar.modal": "语气", "sidebar.message": "信息", "sidebar.notification": "通知", "sidebar.popConfirm": "流行确认", "sidebar.spin": "纺", "sidebar.tables": "表", "sidebar.antTables": "蚂蚁表", "sidebar.pages": "网页", "sidebar.500": "500", "sidebar.404": "404", "sidebar.signIn": "签到", "sidebar.signUp": "注册", "sidebar.forgotPw": "忘记密码", "sidebar.resetPw": "重置密码", "sidebar.invoice": "发票", "sidebar.menuLevels": "菜单级别", "sidebar.item1": "项目1", "sidebar.item2": "项目2", "sidebar.option1": "选项1", "sidebar.option2": "选项2", "sidebar.option3": "选项3", "sidebar.option4": "选项4", "sidebar.blankPage": "空白页", "sidebar.githubSearch": "G<PERSON><PERSON>搜索", "sidebar.youtubeSearch": "Youtube搜索", "languageSwitcher.label": "改变语言", "themeSwitcher": "主题切换器", "themeSwitcher.Sidebar": "侧边栏", "themeSwitcher.Topbar": "顶栏", "themeSwitcher.Background": "背景", "feedback.alert.basicTitle": "基本标题", "feedback.alert.successText": "成功文本", "feedback.alert.infoText": "信息文本", "feedback.alert.warningText": "警告文字", "feedback.alert.errorText": "错误文本", "feedback.alert.closableAlertType": "可关闭警报类型", "feedback.alert.iconAlertType": "图标警报类型", "feedback.alert.iconInfoAlertType": "图标信息警报类型", "feedback.alert.successTips": "成功提示", "feedback.alert.successTipsDescription": "关于成功写作的详细描述和建议。", "feedback.alert.informationTips": "信息备注", "feedback.alert.informationDescription": "关于文案的附加说明和信息。", "feedback.alert.warningTips": "警告", "feedback.alert.warningDescription": "这是关于文案的警告通知。", "feedback.alert.errorTips": "错误", "feedback.alert.errorDescription": "这是关于文案的错误信息。", "feedback.alert.modalTitle": "具有自定义页脚的模态", "feedback.alert.modalSubTitle": "基本模态对话框", "feedback.alert.successTitle": "成功", "feedback.alert.infoTitle": "信息", "feedback.alert.errorTitle": "错误", "feedback.alert.warningTitle": "警告", "feedback.alert.modalBlockTitle": "语气", "feedback.alert.confirmationModalDialogue": "确认模态对话框", "feedback.alert.simpleModalDialogue": "简单的模态对话框", "feedback.alert.message": "信息", "feedback.alert.normalMessageTitle": "正常讯息", "feedback.alert.normalMessageSubtitle": "正常消息作为反馈。", "feedback.alert.displayMessage": "显示正常消息", "feedback.alert.displayOtherTypeMessageTitle": "其他类型的消息", "feedback.alert.displayOtherTypeMessageSubTitle": "消息的成功，错误和警告类型。", "feedback.alert.customizeDurationTitle": "自定义持续时间", "feedback.alert.customizeDurationSubTitle": "自定义消息显示持续时间从默认值1.5s到10s。", "feedback.alert.customizeDurationButton": "定制显示持续时间", "feedback.alert.messageLoadingTitle": "加载消息", "feedback.alert.messageLoadingSubTitle": "显示一个全局加载指示器，它自身被异步地关闭。", "feedback.alert.displayLoadIndicator": "显示装载指示灯", "feedback.alert.notification": "通知", "feedback.alert.notificationBasicTitle": "基本", "feedback.alert.notificationBasicSubTitle": "4.5s后关闭通知框的最简单的用法。", "feedback.alert.notificationBasicDescription": "打开通知框", "feedback.alert.notificationDurationTitle": "通知框关闭之后的时间", "feedback.alert.notificationDurationSubTitle": "持续时间可用于指定通知保持打开的时间。持续时间过后，通知自动关闭。如果未指定，默认值为4.5秒。如果将值设置为0，则通知框将永远不会自动关闭。", "feedback.alert.notificationwithIconTitle": "通知图标", "feedback.alert.notificationwithIconSubTitle": "具有左侧图标的通知框。", "feedback.alert.notificationwithCustomIconTitle": "通知与自定义图标", "feedback.alert.notificationwithCustomIconSubTitle": "正常消息作为反馈。", "feedback.alert.notificationwithCustomButtonTitle": "通知使用自定义按钮", "feedback.alert.notificationwithCustomButtonSubTitle": "正常消息作为反馈。", "feedback.alert.popConfirm": "流行确认", "feedback.alert.popConfirm.basicTitle": "基本确认", "feedback.alert.popConfirm.basicSubTitle": "的基本例子。", "feedback.alert.popConfirm.delete": "删除", "feedback.alert.popConfirm.notiWithIconTitle": "通知与自定义图标", "feedback.alert.popConfirm.notiWithIconSubTitle": "正常消息作为反馈。", "feedback.alert.popConfirm.TL": "TL", "feedback.alert.popConfirm.top": "最佳", "feedback.alert.popConfirm.TR": "TR", "feedback.alert.popConfirm.LT": "LT", "feedback.alert.popConfirm.left": "剩下", "feedback.alert.popConfirm.LB": "磅", "feedback.alert.popConfirm.RT": "RT", "feedback.alert.popConfirm.right": "对", "feedback.alert.popConfirm.RB": "RB", "feedback.alert.popConfirm.Bl": "BL", "feedback.alert.popConfirm.bottom": "底部", "feedback.alert.popConfirm.BR": "BR", "feedback.alert.spin": "纺", "feedback.alert.spin.basicTitle": "尺寸旋转", "feedback.alert.spin.background": "旋转背景", "feedback.alert.spin.backgroundDescription": "旋转背景描述", "feedback.alert.spin.loadingState": "装载状态：", "feedback.alert.spin.alertTitle": "提醒消息标题", "feedback.alert.spin.alertDescription": "有关此警报的上下文的更多详细信息。", "forms.input.header": "输入", "forms.input.basicTitle": "基本用法", "forms.input.basicSubTitle": "基本使用示例。", "forms.input.variationsTitle": "三种尺寸的输入", "forms.input.variationsSubtitle": "输入框有三种尺寸：大（42像素），默认（35像素）和小（30像素）。注意：在表格内部，只使用大尺寸。", "forms.input.groupTitle": "输入组", "forms.input.groupSubTitle": "Input.Group示例注意：您不需要Col来控制紧凑模式下的宽度。", "forms.input.autoSizingTitle": "自动调整高度以适应内容", "forms.input.autoSizingSubTitle": "对于textarea类型的输入，autosize prop可以根据内容自动调整高度。可以提供一个选项对象来自动调整，以指定textarea将自动调整的最小和最大行数。", "forms.input.prePostTabTitle": "前  后选项卡", "forms.input.prePostTabSubTitle": "使用前＆amp;帖子标签示例..", "forms.input.textAreaTitle": "多行文本", "forms.input.textAreaSubTitle": "对于多行用户输入案例，可以使用类型prop具有textarea值的输入。", "forms.input.searchTitle": "搜索", "forms.input.searchSubTitle": "通过使用搜索按钮对标准输入进行分组来创建搜索框的示例", "forms.editor.header": "编辑", "forms.formsWithValidation.header": "定制验证表", "forms.formsWithValidation.failLabel": "失败", "forms.formsWithValidation.failHelp": "应该是数字＆amp;字母", "forms.formsWithValidation.warningLabel": "警告", "forms.formsWithValidation.ValidatingLabel": "证实", "forms.formsWithValidation.ValidatingHelp": "信息正在验证...", "forms.formsWithValidation.SuccessLabel": "成功", "forms.formsWithValidation.WarninghasFeedbackLabel": "警告", "forms.formsWithValidation.FailhasFeedbackLabel": "失败", "forms.formsWithValidation.FailhasFeedbackHelp": "应该是数字＆amp;字母", "forms.progressBar.header": "进度条", "forms.progressBar.standardTitle": "进度条", "forms.progressBar.standardSubTitle": "标准进度条。", "forms.progressBar.circularTitle": "循环进度栏", "forms.progressBar.circularSubTitle": "一个循环进度条。", "forms.progressBar.miniTitle": "迷你尺寸进度条", "forms.progressBar.miniSubTitle": "适合狭窄的地区。", "forms.progressBar.miniCircularTitle": "一个较小的圆形进度条。", "forms.progressBar.dynamicCircularTitle": "动态循环进度条", "forms.progressBar.dynamicCircularSubTitle": "动态进度条更好。", "forms.progressBar.customTextTitle": "自定义文本格式", "forms.progressBar.customTextSubTitle": "您可以通过设置格式自定义文本格式。", "forms.progressBar.dashboardTitle": "仪表板", "forms.progressBar.dashboardSubTitle": "仪表板风格的进步。", "forms.button.header": "纽扣", "forms.button.simpleButton": "按钮类型", "forms.button.iconButton": "按钮图标", "forms.button.simpleButtonPrimaryText": "主", "forms.button.simpleButtonDefaultText": "默认", "forms.button.simpleButtonDashedText": "虚线", "forms.button.simpleButtonDangerText": "危险", "forms.button.iconPrimaryButton": "搜索", "forms.button.iconSimpleButton": "搜索", "forms.button.iconCirculerButton": "搜索", "forms.button.iconDashedButton": "搜索", "forms.button.SizedButton": "按钮大小", "forms.button.DisabledButton": "按钮禁用", "forms.button.LoadingButton": "按钮加载", "forms.button.MultipleButton": "多重按钮", "forms.button.groupButton": "按钮组", "forms.Tabs.header": "标签", "forms.Tabs.simpleTabTitle": "搜索", "forms.Tabs.simpleTabSubTitle": "禁用标签", "forms.Tabs.iconTabTitle": "图标标签", "forms.Tabs.miniTabTitle": "迷你标签", "forms.Tabs.extraTabTitle": "额外动作标签", "forms.Tabs.TabpositionTitle": "位置", "forms.Tabs.TabpositionSubTitle": "标签的位置：左，右，上或下", "forms.Tabs.cardTitle": "卡类型选项卡", "forms.Tabs.editableTitle": "添加和关闭选项卡", "forms.Tabs.verticalTitle": "垂直类型标签", "forms.Tabs.basicTitle": "基本标签", "forms.checkbox.header": "复选框", "forms.checkbox.basicTitle": "基本复选框", "forms.checkbox.basicSubTitle": "复选框的基本用法", "forms.checkbox.groupTitle": "复选框组", "forms.checkbox.groupSubTitle": "从数组生成一组复选框。禁用此功能可禁用复选框。", "forms.checkbox.groupCheckTitle": "复选框组", "forms.checkbox.groupCheckSubTitle": "从数组生成一组复选框。禁用此功能可禁用复选框。", "forms.radio.header": "无线电", "forms.radio.simpleTitle": "基本电台", "forms.radio.simpleSubTitle": "最简单的用法禁用禁用广播。", "forms.radio.groupTitle": "垂直无线电组", "forms.radio.groupSubTitle": "垂直无线电组，更多收音机。", "forms.radio.groupSecondTitle": "RadioGroup中", "forms.radio.groupSecondSubTitle": "一组无线电组件。", "forms.radio.groupThirdTitle": "RadioGroup中", "forms.radio.groupThirdSubTitle": "一组无线电组件。", "forms.transfer.header": "转让", "forms.transfer.SubTitle": "使用搜索框转移。", "forms.transfer.Title": "搜索", "forms.autocomplete.header": "自动完成", "forms.autocomplete.simpleTitle": "定制", "forms.autocomplete.simpleSubTitle": "您可以将AutoComplete.Option作为AutoComplete的子代码，而不是使用dataSource", "forms.autocomplete.customizeTitle": "自定义输入组件", "forms.autocomplete.customizeSubTitle": "自定义输入组件", "uiElements.badge.badge": "徽章", "uiElements.badge.basicExample": "基本例子", "uiElements.badge.basicExampleSubTitle": "最简单的用法当count为0时，徽章将被隐藏，但是我们可以使用showZero来显示。", "uiElements.badge.overflowCount": "溢出计数", "uiElements.badge.overflowCountSubTitle": "当count大于overflowCount时，会显示OverflowCount。 overflowCount的默认值为99。", "uiElements.badge.status": "状态", "uiElements.badge.statusSubTitle": "独立徽章与状态。", "uiElements.badge.success": "成功", "uiElements.badge.error": "错误", "uiElements.badge.default": "默认", "uiElements.badge.processing": "处理", "uiElements.badge.warning": "警告", "uiElements.badge.redBadge": "红色徽章", "uiElements.badge.redBadgeSubTitle": "这只会显示一个红色的徽章，没有特定的数字。", "uiElements.badge.linkSomething": "链接一些东西", "uiElements.cards.cards": "牌", "uiElements.cards.basicCard": "基本卡", "uiElements.cards.basicCardSubTitle": "包含标题，内容和额外角落内容的基本卡。", "uiElements.cards.more": "更多", "uiElements.cards.cardTitle": "卡标题", "uiElements.cards.cardContent": "卡内容", "uiElements.cards.lorem": "Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor incididunt ut labore et dolore magna aliqua。 Ut enim ad minim veniam，quis nostrud exerciseitation ullamco laboris nisi ut aliquip ex ea commodo因此。", "uiElements.cards.noBorder": "没有边界", "uiElements.cards.noBorderSubTitle": "在灰色背景上的无边界的卡。", "uiElements.cards.gridCard": "网格卡", "uiElements.cards.gridCardSubTitle": "卡通常在概览页面中与网格布局配合使用。", "uiElements.cards.loadingCard": "装卡", "uiElements.cards.loadingCardSubTitle": "显示加载指示符，同时正在取出卡的内容。", "uiElements.cards.whateverContent": "无论内容", "uiElements.cards.customizedContentTitle": "定制内容", "uiElements.cards.customizedContent": "显示加载指示符，同时正在取出卡的内容。", "uiElements.cards.europeStreetBeat": "欧洲街拍", "uiElements.cards.instagram": "www.instagram.com", "uiElements.carousel.carousel": "狂欢游行", "uiElements.carousel.verticalCarousel": "垂直旋转木马", "uiElements.carousel.verticalCarouselSubTitle": "垂直分页。使用  vertical =true", "uiElements.carousel.basicCarousel": "基本转盘", "uiElements.carousel.basicCarouselSubTitle": "基本用法", "uiElements.carousel.fadeInTransition": "淡入淡出", "uiElements.carousel.fadeInTransitionSubTitle": "幻灯片使用淡入淡出。   效果= 淡入", "uiElements.carousel.scrollAutomatically": "自动滚动", "uiElements.carousel.scrollAutomaticallySubTitle": "滚动到下一张卡  图片的时间。自动播放", "uiElements.collapse.collapse": "坍方", "uiElements.collapse.collapseSubTitle": "一次可以扩展多个面板，在这种情况下，第一个面板被初始化为活动状态。使用  defaultActiveKey =   [keyNum]", "uiElements.collapse.text": "狗是一种驯养动物。以其忠诚和忠诚而闻名，可以作为世界各地许多家庭的欢迎客人。", "uiElements.collapse.headerOne": "这是面板标题1", "uiElements.collapse.headerTwo": "这是面板标题2", "uiElements.collapse.headerThree": "这是面板标题3", "uiElements.collapse.headerNested": "这是面板嵌套面板", "uiElements.collapse.nestedExample": "嵌套示例", "uiElements.collapse.nestedExampleSubTitle": "折叠嵌套在折叠中。", "uiElements.collapse.borderlessExample": "无边界的例子", "uiElements.collapse.borderlessExampleSubTitle": "无边界风格的折叠。使用  bordered =   false", "uiElements.collapse.accordion": "手风琴", "uiElements.collapse.accordionSubTitle": "手风琴模式，一次只能扩展一个面板。默认情况下，第一个面板将被扩展。使用手风琴", "uiElements.popover.popover": "酥料饼", "uiElements.popover.basicExample": "基本例子", "uiElements.popover.basicExampleSubTitle": "最基本的例子。浮动层的大小取决于内容区域。", "uiElements.popover.hoverMe": "悬停我", "uiElements.popover.title": "标题", "uiElements.popover.titleTrigger": "三种触发方式", "uiElements.popover.titleTriggerSubTitle": "鼠标点击，对焦和移动。", "uiElements.popover.focusMe": "聚焦我", "uiElements.popover.clickMe": "点击我", "uiElements.popover.placement": "放置", "uiElements.popover.placementSubTitle": "有12个放置选项可用。", "uiElements.popover.top": "最佳", "uiElements.popover.topLeft": "左上角", "uiElements.popover.topRight": "右上", "uiElements.popover.leftTop": "左上", "uiElements.popover.left": "剩下", "uiElements.popover.leftBottom": "左下", "uiElements.popover.rightTop": "右上", "uiElements.popover.right": "对", "uiElements.popover.bottom": "底部", "uiElements.popover.bottomLeft": "左下方", "uiElements.popover.bottomRight": "右下", "uiElements.popover.boxTitle": "控制对话框的关闭", "uiElements.popover.boxSubTitle": "使用可见支柱来控制显卡的显示。", "uiElements.popover.TR": "TR", "uiElements.popover.TL": "TL", "uiElements.popover.LT": "LT", "uiElements.popover.LB": "磅", "uiElements.popover.RT": "RT", "uiElements.popover.RB": "RB", "uiElements.popover.BL": "BL", "uiElements.popover.BR": "BR", "uiElements.popover.close": "关", "uiElements.tooltip.tooltip": "提示", "uiElements.tooltip.tooltipContent": "工具提示内容", "uiElements.tooltip.basicExample": "基本例子", "uiElements.tooltip.basicExampleSubTitle": "最简单的用法", "uiElements.tooltip.placementTitle": "放置", "uiElements.tooltip.placementSubTitle": "工具提示有12个布局选择。", "uiElements.tooltip.TL": "TL", "uiElements.tooltip.TR": "TR", "uiElements.tooltip.LT": "LT", "uiElements.tooltip.LB": "磅", "uiElements.tooltip.RT": "RT", "uiElements.tooltip.RB": "RB", "uiElements.tooltip.BL": "BL", "uiElements.tooltip.BR": "BR", "uiElements.tooltip.bottom": "底部", "uiElements.tooltip.right": "对", "uiElements.tooltip.left": "剩下", "uiElements.tooltip.top": "最佳", "uiElements.tooltip.tooltipContentSpan": "鼠标进入时会显示工具提示。", "uiElements.tooltip.contentSpan": "工具提示内容", "uiElements.tags.tags": "标签", "uiElements.tags.basicExample": "基本例子", "uiElements.tags.basicExampleSubTitle": "使用基本标签，它可以通过set closable属性来关闭。关闭标签支持onClose afterClose事件。", "uiElements.tags.tagOne": "标签1", "uiElements.tags.tagTwo": "标签2", "uiElements.tags.link": "链接", "uiElements.tags.preventDefault": "防止默认", "uiElements.tags.colorfulTag": "多彩标签", "uiElements.tags.hotTags": "热门标签", "uiElements.tags.hotTagsSubTitle": "选择您最喜欢的主题。", "uiElements.tags.hots": "热门活动：", "uiElements.tags.addRemoveDynamically": "动态添加和删除", "uiElements.tags.addRemoveDynamicallySubTitle": "通过数组生成一组标签，可以动态添加和删除。它基于afterClose事件，这将在关闭动画结束时触发。", "uiElements.tags.newTag": "+新标签", "uiElements.timeline.timeline": "时间线", "uiElements.timeline.basicExample": "基本例子", "uiElements.timeline.basicTimeline": "基本时间表", "uiElements.timeline.lastNode": "上一个节点", "uiElements.timeline.lastNodeContent": "当时间轴不完整和持续时，最后放一个鬼节点。 set   pending =   true    或  pending =   a React Element", "uiElements.timeline.seeMore": "查看更多", "uiElements.timeline.custom": "习惯", "uiElements.timeline.customContent": "将节点设置为图标或其他自定义元素。", "uiElements.timeline.colorExample": "颜色示例", "uiElements.timeline.colorExampleContent": "设置圈子的颜色。绿色表示完成或成功状态，红色表示警告或错误，蓝色表示正在进行或其他默认状态。", "uiElements.timeline.createServiceSite": "创建服务网站2015-09-01", "uiElements.timeline.solveInitialNetwork": "解决初始网络问题2015-09-01", "uiElements.timeline.networkProblemSolved": "网络问题正在解决2015-09-01", "uiElements.timeline.technicalTesting": "技术测试2015-09-01", "uiElements.dropdown.dropdown": "落下", "uiElements.dropdown.hoverDropdown": "悬停下拉", "uiElements.dropdown.hoverMe": "悬停我", "uiElements.dropdown.hoverPlacement": "悬停放置下拉", "uiElements.dropdown.hoverDisableLink": "悬停下拉与禁用链接", "uiElements.dropdown.clickedDropdown": "点击下拉", "uiElements.dropdown.buttonDropdown": "按钮与下拉菜单", "uiElements.pagination.pagination": "分页", "uiElements.pagination.basic": "基本", "uiElements.pagination.more": "更多", "uiElements.pagination.changer": "换", "uiElements.pagination.jumper": "跨接器", "uiElements.pagination.miniSize": "迷你尺寸", "uiElements.pagination.simpleMode": "简单模式", "uiElements.pagination.controlled": "受控", "uiElements.pagination.totalNumber": "总数", "uiElements.rating.rating": "评分", "uiElements.rating.basicExample": "基本例子", "uiElements.rating.basicExampleSubTitle": "最简单的用法", "uiElements.rating.halfStar": "半星", "uiElements.rating.halfStarSubTitle": "支持选择半星。", "uiElements.rating.showCopywriting": "显示文案", "uiElements.rating.showCopywritingSubTitle": "以速率组件添加文案。", "uiElements.rating.readOnly": "只读", "uiElements.rating.readOnlySubTitle": "只读，不能使用鼠标进行交互。", "uiElements.rating.otherCharacter": "其他性格", "uiElements.rating.otherCharacterSubTitle": "将默认明星替换为其他字符，如字母，数字，iconfont甚至中文字。", "uiElements.tree.tree": "树", "uiElements.tree.basicExample": "基本例子", "uiElements.tree.basicExampleSubTitle": "最基本的用法，告诉你如何使用可检查，可选择，禁用，defaultExpandKeys等。", "uiElements.tree.basicControlledExample": "基本控制示例", "uiElements.tree.basicControlledExampleSubTitle": "基本控制示例", "uiElements.tree.draggableExample": "可拖曳的例子", "uiElements.tree.draggableExampleSubTitle": "拖动treeNode插入另一个treeNode或插入到另一个父TreeNode中。", "uiElements.tree.loadAsync": "异步加载数据", "uiElements.tree.loadAsyncSubTitle": "单击以展开treeNode时异步加载数据。", "uiElements.tree.searchableExample": "可搜索的例子", "uiElements.tree.searchableExampleSubTitle": "可搜索的树", "uiElements.tree.treeWithLine": "树与线", "shuffle.descriptionOne": "Netscape 2.0发布，引入Javascript", "shuffle.descriptionTwo": "<PERSON>发布了AJAX规范", "shuffle.descriptionThree": "jQuery 1.0发布", "shuffle.descriptionFour": "首先下划线", "shuffle.descriptionFive": "Backbone.js成为一件事情", "shuffle.descriptionSix": "角度1.0发布", "shuffle.descriptionSeven": "反应是开源的开发商高兴", "toggle.list": "名单", "toggle.grid": "格", "toggle.ascending": "上升", "toggle.descending": "降序", "toggle.shuffle": "拖曳", "toggle.rotate": "旋转", "toggle.addItem": "新增项目", "toggle.removeItem": "除去项目", "contactlist.searchContacts": "搜索联系人", "contactlist.addNewContact": "添加新联系人", "notes.ChoseColor": "为您的笔记选择一种颜色", "notes.addNote": "添加新注", "page404.title": "404", "page404.subTitle": "看起来你已经迷路了", "page404.description": "您正在寻找的页面不存在或已被移动。", "page404.backButton": "回家", "page500.title": "500", "page500.subTitle": "内部服务器错误", "page500.description": "出了些问题。请再试一次信。", "page500.backButton": "回家", "page.forgetPassTitle": "同构", "page.forgetPassSubTitle": "忘记密码？", "page.forgetPassDescription": "输入您的电子邮件，我们向您发送重置链接。", "page.sendRequest": "发送请求", "page.resetPassTitle": "同构", "page.resetPassSubTitle": "重设密码", "page.resetPassDescription": "输入新密码并进行确认。", "page.resetPassSave": "保存", "page.signInTitle": "同构", "page.signInRememberMe": "记住我", "page.signInButton": "签到", "page.signInPreview": "用户名：demo，密码：demodemo，或者点击任意按钮。", "page.signInFacebook": "用Facebook登录", "page.signInGooglePlus": "使用Google Plus登录", "page.signInAuth0": "用Auth0登录", "page.signInForgotPass": "忘记密码", "page.signInCreateAccount": "创建一个异构帐户", "page.signUpTitle": "同构", "page.signUpTermsConditions": "我同意条款和条件", "page.signUpButton": "注册", "page.signUpFacebook": "用Facebook注册", "page.signUpGooglePlus": "用Google Plus注册", "page.signUpAuth0": "注册Auth0", "page.signUpAlreadyAccount": "已经有账户？签到。", "widget.reportswidget.label": "收入", "widget.reportswidget.details": "Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor", "widget.singleprogresswidget1.label": "营销", "widget.singleprogresswidget2.label": "Addvertisement", "widget.singleprogresswidget3.label": "咨询", "widget.singleprogresswidget4.label": "发展", "widget.stickerwidget1.number": "210", "widget.stickerwidget1.text": "未读电子邮件", "widget.stickerwidget2.number": "1749", "widget.stickerwidget2.text": "图片上传", "widget.stickerwidget3.number": "3024", "widget.stickerwidget3.text": "总信息", "widget.stickerwidget4.number": "54", "widget.stickerwidget4.text": "订单发布", "widget.salewidget1.label": "收入", "widget.salewidget1.price": "$ 15000", "widget.salewidget1.details": "Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor", "widget.salewidget2.label": "收入", "widget.salewidget2.price": "$ 15000", "widget.salewidget2.details": "Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor", "widget.salewidget3.label": "收入", "widget.salewidget3.price": "$ 15000", "widget.salewidget3.details": "Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor", "widget.salewidget4.label": "收入", "widget.salewidget4.price": "$ 15000", "widget.salewidget4.details": "Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor", "widget.cardwidget1.number": "110", "widget.cardwidget1.text": "新消息", "widget.cardwidget2.number": "100％", "widget.cardwidget2.text": "卷", "widget.cardwidget3.number": "137", "widget.cardwidget3.text": "成就", "widget.progresswidget1.label": "下载", "widget.progresswidget1.details": "50％完成", "widget.progresswidget2.label": "支持", "widget.progresswidget2.details": "80％满意客户", "widget.progresswidget3.label": "上传", "widget.progresswidget3.details": "65％完成", "widget.vcardwidget.name": "<PERSON><PERSON>", "widget.vcardwidget.title": "高级iOS开发人员", "widget.vcardwidget.description": "Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor ammet dolar consectetur adipisicing elit", "checkout.billingform.firstname": "名字", "checkout.billingform.lastname": "姓", "checkout.billingform.company": "公司名", "checkout.billingform.email": "电子邮件地址", "checkout.billingform.mobile": "手机号码", "checkout.billingform.country": "国家", "checkout.billingform.city": "市", "checkout.billingform.address": "地址", "checkout.billingform.addressoptional": "公寓，套房，单位等（可选）", "checkout.billingform.checkbox": "创建一个帐户？", "antTable.title.image": "图片", "antTable.title.firstName": "名字", "antTable.title.lastName": "姓", "antTable.title.city": "市", "antTable.title.street": "街", "antTable.title.email": "电子邮件", "antTable.title.dob": "DOB", "Map.leaflet.basicTitle": "基本地图", "Map.leaflet.basicMarkerTitle": "基本地图（带默认标记）", "Map.leaflet.leafletCustomMarkerTitle": "基本地图（使用自定义图标标记）", "Map.leaflet.leafletCustomHtmlMarkerTitle": "基本地图（使用自定义Html标记）", "Map.leaflet.leafletMarkerClusterTitle": "基本地图（带标记群集）", "Map.leaflet.leafletRoutingTitle": "基本地图路由", "Component.contacts.noOption": "找不到联系", "email.send": "发送", "email.cancel": "取消", "email.compose": "撰写", "email.noMessage": "请选择一个邮件阅读", "themeSwitcher.purchase": "立即购买", "themeSwitcher.settings": "设置", "sidebar.selectbox": "选择", "sidebar.frappeChart": "Frappe Charts", "topbar.help": "帮帮我", "topbar.logout": "登出", "topbar.viewAll": "查看全部", "topbar.viewCart": "查看购物车", "topbar.totalPrice": "总价格"}