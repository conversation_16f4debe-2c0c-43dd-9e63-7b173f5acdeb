const txt = `
Email
Ecommerce
Shop
Cart
Checkout
Cards
Maps
Google Map
Leaflet Map
Calendar
Notes
Todos
Contacts
Shuffle
Charts
Google Carts
Recharts
React Vis
React-Chart-2
React-Trend
Echart
Forms
Input
Editor
Forms With Validation
Progress
Button
Tab
Checkbox
Radiobox
Transfer
AutoComplete
Box Options
UI Elements
Badge
Card
Carousal
Collapse
Pop Over
Tooltip
Tag
Timeline
Dropdown
Pagination
Rating
Tree
Advanced Elements
React Dates
Code Mirror
Uppy Uploader
Drop Zone
Feedback
Alert
Modal
Message
Notification
Pop Confirm
Spin
Tables
Ant Tables
Pages
500
404
Sign In
Sign Up
Forgot Passwords
Reset Passwords
Invoice
Menu Levels
Item 1
Item 2
Option 1
Option 2
Option 3
Option 4
Blank Page
Github Search
Youtube Search
Change Language
Theme Switcher
Sidebar
Topbar
Background
Basic Title
Success text
Info Text
Warning Text
Error Text
Closable Alert Type
Icon Alert Type
Icon Info Alert Type
success tips
Detailed description and advices about successful copywriting.
Informational Notes
Additional description and informations about copywriting.
Warning
This is a warning notice about copywriting.
Error
This is an error message about copywriting.
Modal one with customize Footer
Basic modal dialog.
Success
Info
Error
Warning
Modal
Confirmation modal dialog
Simple modal dialog
Message
Normal Message
Normal messages as feedbacks.
Display normal message
Other Types of Message
Messages of success   error and warning types.
Customize duration
ustomize message display duration from default 1.5s to 10s.
Customized display duration
Message of loading
Display a global loading indicator   which is dismissed by itself asynchronously.
Display a loading indicator
Notification
Basic
The simplest usage that close the notification box after 4.5s.
Open the notification box
Duration after which the notification box is closed
Duration can be used to specify how long the notification stays open. After the duration time elapses   the notification closes automatically. If not specified   default value is 4.5 seconds. If you set the value to 0   the notification box will never close automatically.
Notification with icon
A notification box with a icon at the left side.
Notification with custom icon
Normal messages as feedbacks.
Notification with custom button
Normal messages as feedbacks.
Pop Confirm
Basic Confirm
The basic example.
Delete
Notification with custom icon
Normal messages as feedbacks.
TL
Top
TR
LT
Left
LB
RT
Right
RB
BL
Bottom
BR
Spin
Size Spin
Spin With Background
Spin With Background description
Loading State
Alert message title
Further details about the context of this alert.
Input
Basic usage
Basic usage example.
Three sizes of Input
There are three sizes of an Input box  large (42px  、default (35px   and small (30px  . Note  Inside of forms   only the large size is used.
Input Group
Input.Group example Note  You dont need Col to control the width in the compact mode.
Autosizing the height to fit the content
autosize prop for a textarea type of Input makes the height to automatically adjust based on the content. An options object can be provided to autosize to specify the minimum and maximum number of lines the textarea will automatically adjust.
Pre    Post tab
Using pre &amp; post tabs example..
Textarea
For multi-line user input cases   an input whose type prop has the value of textarea can be used.
Search
Example of creating a search box by grouping a standard input with a search button
Editor
Customized Validation Form
Fail
Should be combination of numbers &amp; alphabets
Warning
Validating
The information is being validated...
Success
Warning
Fail
Should be combination of numbers &amp; alphabets
Progress Bar
Progress bar
A standard progress bar.
Circular Progress bar
A circular progress bar.
Mini size progress bar
Appropriate for a narrow area.
A smaller circular progress bar.
Dynamic circular progress bar
A dynamic progress bar is better.
Custom text format
You can custom text format by setting format.
Dashboard
A dashboard style of progress.
Buttons
Button Type
Button Icon
Primary
Default
Dashed
Danger
search
search
search
search
Button Size
Button Disabled
Button Loading
Multiple Button
Button Group
Tabs
search
Disabled Tabs
Icon Tabs
Mini Tabs
Extra Action Tabs
Position
Tabss position  left   right   top or bottom
Card Type Tabs
Add and Close Tabs
Vertical Type Tabs
Basic Tabs
Checkbox
Basic Checkbox
Basic usage of checkbox.
Checkbox Group
Generate a group of checkboxes from an array. Use disabled to disable a checkbox.
Checkbox Group
Generate a group of checkboxes from an array. Use disabled to disable a checkbox.
Radio
Basic Radio
The simplest use. Use disabled to disable a radio.
Vertical RadioGroup
Vertical RadioGroup   with more radios.
RadioGroup
A group of radio components.
RadioGroup
A group of radio components.
Transfer
Transfer with a search box.
Search
Autocomplete
Customized
You could pass AutoComplete.Option as children of AutoComplete   instead of using dataSource
Customize Input Component
Customize Input Component
Badge
Basic Example
Simplest Usage. Badge will be hidden when count is 0   but we can use showZero to show it.
Overflow Count
OverflowCount is displayed when count is larger than overflowCount. The default value of overflowCount is 99.
Status
Standalone badge with status.
Success
Error
Default
Processing
Warning
Red badge
This will simply display a red badge   without a specific count.
Link something
Cards
Basic card
A basic card containing a title   content and an extra corner content.
More
Card Title
Card content
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam   quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
No Border
A borderless card on a gray background.
Grid card
Cards usually cooperate with grid layout in overview page.
Loading card
Shows a loading indicator while the contents of the card is being fetched.
Whatever content
Customized Content
Shows a loading indicator while the contents of the card is being fetched.
Europe Street beat
www.instagram.com
Carousal
Vertical Carousel
Vertical pagination. use   vertical=true
Basic Carousel
Basic usage
Fade In Transition
Slides use fade for transition.   effect=fade
Scroll Automatically
Timing of scrolling to the next card  picture. autoplay
Collapse
More than one panel can be expanded at a time   the first panel is initialized to be active in this case. use   defaultActiveKey=  [keyNum]
A dog is a type of domesticated animal. Known for its loyalty and faithfulness   it can be found as a welcome guest in many households across the world.
This is panel header 1
This is panel header 2
This is panel header 3
This is panel nest panel
Nested Example
Collapse is nested inside the Collapse.
Borderless Example
A borderless style of Collapse. use   bordered=  false
Accordion
Accordion mode   only one panel can be expanded at a time. The first panel will be expanded by default. use accordion
Popover
Basic Example
The most basic example. The size of the floating layer depends on the contents region.
Hover me
Title
Three ways to trigger
Mouse to click   focus and move in.
Focus me
Click me
Placement
There are 12 placement options available.
Top
Top Left
Top Right
Left Top
Left
Left Bottom
Right Top
Right
Bottom
Bottom Left
Bottom Right
Controlling the close of the dialog
Use visible prop to control the display of the card.
TR
TL
LT
LB
RT
RB
BL
BR
Close
Tooltip
Tooltip Content
Basic Example
The simplest usage.
Placement
The ToolTip has 12 placements choice.
TL
TR
LT
LB
RT
RB
BL
BR
Bottom
Right
Left
Top
Tooltip will show when mouse enter.
Tooltip Content
Tags
Basic Example
Usage of basic Tag   and it could be closable by set closable property. Closable Tag supports onClose afterClose events.
Tag 1
Tag 2
Link
Prevent Default
Colorful Tag
Hot Tags
Select your favourite topics.
Hots
Add & Remove Dynamically
Generating a set of Tags by array   you can add and remove dynamically. Its based on afterClose event   which will be triggered while the close animation end.
+ New Tag
Timeline
Basic Example
Basic timeline
Last Node
When the timeline is incomplete and ongoing   put a ghost node at last. set   pending=  true     or   pending=  a React Element
See more
Custom
Set a node as an icon or other custom element.
Color Example
Set the color of circles. green means completed or success status   red means warning or error   and blue means ongoing or other default status.
Create a services site 2015-09-01
Solve initial network problems 2015-09-01
Network problems being solved 2015-09-01
Technical testing 2015-09-01
Dropdown
Hover Drop Down
Hover me
Hover Placement Drop Down
Hover Drop Down with Disable link
Clicked Drop Down
Button with dropdown menu
Pagination
Basic
More
Changer
Jumper
Mini Size
Simple Mode
Controlled
Total Number
Rating
Basic Example
The simplest usage.
Half star
Support select half star.
Show copywriting
Add copywriting in rate components.
Read only
Read only   cant use mouse to interact.
Other Character
Replace the default star to other character like alphabet   digit   iconfont or even Chinese word.
Tree
Basic example
The most basic usage   tell you how to use checkable   selectable   disabled   defaultExpandKeys   and etc.
Basic controlled example
basic controlled example
Draggable example
Drag treeNode to insert after the other treeNode or insert into the other parent TreeNode.
Load data asynchronously
To load data asynchronously when click to expand a treeNode.
Searchable example
Searchable Tree
Tree With Line
Netscape 2.0 ships   introducing Javascript
Jesse James Garrett releases AJAX spec
jQuery 1.0 released
First underscore.js commit
Backbone.js becomes a thing
Angular 1.0 released
React is open-sourced; developers rejoice
List
Grid
Ascending
Descending
Shuffle
Rotate
Add Item
Remove Item
Search Contacts
Add New Contact
Choose a color for your note
Add New Note
404
Looks like youve got lost
The page youre looking for doesnt exist or has been moved.
BACK HOME
500
Internal Server Error
Something went wrong. Please try again letter.
BACK HOME
Isomorphic
Forgot Password?
Enter your email and we send you a reset link.
Send request
Isomorphic
Reset Password
Enter new password and confirm it.
Save
Isomorphic
Remember me
Sign in
username  demo   password  demodemo   or Just click on any button.
Sign in with Facebook
Sign in with Google Plus
Sign in with Auth0
Forgot password
Create an Isomorphoic account
Isomorphic
I agree with terms and condtions
Sign Up
Sign up with Facebook
Sign up with Google Plus
Sign Up with Auth0
Already have an account? Sign in.
Income
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
Marketing
Addvertisement
Consulting
Development
210
Unread Email
1749
Image Upload
3024
Total Message
54
Orders Post
Income
$15000
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
Income
$15000
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
Income
$15000
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
Income
$15000
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
110
New Messages
100%
Volume
137
Achievement
Download
50% Complete
Support
80% Satisfied Customer
Upload
65% Complete
Jhon Doe
Sr. iOS Developer
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor ammet dolar consectetur adipisicing elit
First Name
Last Name
Company Name
Email Address
Mobile No
Country
City
Address
Apartment   suite   unit etc. (optional
Create an account?
Image
First Name
Last Name
City
Street
Email
DOB
Basic Map
Basic Map(With Default Marker
Basic Map(With Custom Icon Marker
Basic Map(With Custom Html Marker
Basic Map(With Marker Cluster
Basic Map Routing
No contact found
SEND
CANCEL
COMPOSE
Please select a mail to read
PURCHAGE NOW
SETTINGS
Select
Frappe Charts
Help
Logout
View All
View Cart
Total Price
`;
export default txt;
