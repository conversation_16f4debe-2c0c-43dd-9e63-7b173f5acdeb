const txt = `
Email
Commerce électronique
Boutique
Chariot
check-out
Cartes
Cartes
Google Map
Carte de brochure
Calendrier
Remarques
Todos
Contacts
Shuffle
Graphiques
Chariots Google
Recharts
Reagir Vis
React-Chart-2
React-Trend
Echart
Formes
Contribution
Éditeur
Formulaires avec validation
Le progrès
Bouton
Languette
Case à cocher
Radiobox
Transfert
AutoComplete
Options de boîtes
UI Elements
Badge
Carte
Beuverie
Effondrer
Pop Over
Info-bulle
Marque
Chronologie
Menu déroulant
Pagination
Évaluation
Arbre
Éléments avancés
Réagissez aux dates
Code miroir
Uppy Uploader
Zone de largage
Retour dinformation
Alerte
Modal
Message
Notification
Pop Confirmer
Tourner
les tables
Tables dAnt
Pages
500
404
Se connecter
Sinscrire
Mot de passe oublié
Restaurer les mots de passe
Facture dachat
Niveaux de menu
Objet 1
Point 2
Option 1
Option 2
Option 3
Option 4
Page blanche
Github Rechercher
Recherche YouTube
Changer de langue
Commutateur de thème
Barre latérale
Barre du haut
Contexte
Titre de base
Texte de réussite
Texte dinformation
Texte davertissement
Texte derreur
Type dalerte accessible
Type dalerte dicône
Type dalerte Info icône
conseils de réussite
Description détaillée et conseils sur la rédaction réussie.
Notes dinformation
Description supplémentaire et informations sur la rédaction.
Attention
Il sagit dun avis davertissement concernant la rédaction.
Erreur
Il sagit dun message derreur sur la rédaction.
Modal avec pied de page personnalisé
Dialogue modal de base.
Succès
Info
Erreur
Attention
Modal
Dialogue modal de confirmation
Dialogue modal simple
Message
Message normal
Les messages normaux sont des commentaires.
Afficher le message normal
Autres types de message
Messages de succès   erreurs et types davertissement.
Personnaliser la durée
ustomiser la durée daffichage du message de 1.5s à 10s par défaut.
Durée daffichage personnalisée
Message de chargement
Affichez un indicateur de chargement global   qui est rejeté par lui-même de manière asynchrone.
Afficher un indicateur de chargement
Notification
De base
Lutilisation la plus simple qui ferme la boîte de notification après 4.5s.
Ouvrir la boîte de notification
Durée après laquelle la boîte de notification est fermée
La durée peut être utilisée pour spécifier la durée pendant laquelle la notification reste ouverte. Une fois la durée écoulée   la notification se ferme automatiquement. Si elle nest pas spécifiée   la valeur par défaut est de 4  5 secondes. Si vous définissez la valeur sur 0   la boîte de notification ne se ferme jamais automatiquement.
Notification avec icône
Une boîte de notification avec une icône sur le côté gauche.
Notification avec icône personnalisée
Les messages normaux sont des commentaires.
Notification avec bouton personnalisé
Les messages normaux sont des commentaires.
Pop Confirmer
Confirmation de base
Lexemple de base.
Effacer
Notification avec icône personnalisée
Les messages normaux sont des commentaires.
TL
Haut
TR
LT
La gauche
KG
RT
Droite
RB
BL
Bas
BR
Tourner
Taille Spin
Spin With Background
Spin With Background description
État de chargement
Titre du message dalerte
Plus de détails sur le contexte de cette alerte.
Contribution
Utilisation de base
Exemple dutilisation de base.
Trois tailles dentrée
Il existe trois tailles dune boîte de saisie  grande (42px     par défaut (35px   et petite (30px  . Remarque  À lintérieur des formes   seule la grande taille est utilisée.
Groupe dentrée
Exemple Input.Group Remarque  Vous navez pas besoin de Col pour contrôler la largeur en mode compact.
Faire correspondre la hauteur pour sadapter au contenu
La fonction dautosize pour un type dentrée Textarea permet à la hauteur de sadapter automatiquement en fonction du contenu. Un objet doptions peut être fourni pour lautosize pour spécifier le nombre minimum et maximum de lignes que la zone de texte va automatiquement ajuster.
Onglet Pré et Post
En utilisant pre & amp; exemple donglets de publication
Textarea
Pour les cas dentrée dutilisateurs multi-lignes   une entrée dont le type prop a la valeur de la zone de texte peut être utilisée.
Chercher
Exemple de création dun champ de recherche en regroupant une entrée standard avec un bouton de recherche
Éditeur
Formulaire de validation personnalisé
Échouer
Doit être une combinaison de nombres et amp; alphabets
Attention
Validation
Linformation est validée ...
Succès
Attention
Échouer
Doit être une combinaison de nombres et amp; alphabets
Barre de progression
Barre de progression
Une barre de progression standard.
Barre de progression circulaire
Une barre de progression circulaire.
Barre de progression de taille mini
Approprié pour une zone étroite.
Une barre de progression circulaire plus petite.
Barre de progression circulaire dynamique
Une barre de progression dynamique est meilleure.
Format de texte personnalisé
Vous pouvez personnaliser le format de texte en définissant le format.
Tableau de bord
Un style de progrès du tableau de bord.
Boutons
Type de bouton
Icône de bouton
Primaire
Défaut
tireté
Danger
chercher
chercher
chercher
chercher
Taille du bouton
Bouton désactivé
Chargement du bouton
Bouton multiple
Groupe de boutons
Onglets
chercher
Onglets désactivés
Onglets dicônes
Mini tabs
Onglets daction supplémentaires
Position
Position de longlet  gauche   droite   haut ou bas
Onglets de type de carte
Ajouter et fermer les onglets
Onglets de type vertical
Onglets de base
Case à cocher
Basic Checkbox
Utilisation de base de la case à cocher.
Checkbox Group
Générer un groupe de cases à cocher dans un tableau. Utilisez désactivé pour désactiver une case à cocher.
Checkbox Group
Générer un groupe de cases à cocher dans un tableau. Utilisez désactivé pour désactiver une case à cocher.
Radio
Radio de base
Lutilisation la plus simple. Utiliser désactivé pour désactiver une radio.
Groupe radio vertical
Vertical RadioGroup   avec plus de radios.
RadioGroup
Un groupe de composants radio.
RadioGroup
Un groupe de composants radio.
Transfert
Transfert avec un champ de recherche.
Chercher
Autocomplète
Personnalisé
Vous pouvez passer AutoComplete.Option en tant que enfants dAutoComplete   au lieu dutiliser DataSource
Personnaliser le composant dentrée
Personnaliser le composant dentrée
Badge
Exemple de base
Lutilisation la plus simple. Le badge sera caché lorsque count est 0   mais nous pouvons utiliser showZero pour le montrer.
Compte de débordement
OverflowCount saffiche lorsque le nombre est supérieur à OverflowCount. La valeur par défaut de overflowCount est 99.
Statut
Un badge autonome avec statut.
Succès
Erreur
Défaut
En traitement
Attention
Badge rouge
Cela affichera simplement un badge rouge   sans compter précisément.
Relier quelque chose
Cartes
Carte de base
Une carte de base contenant un titre   un contenu et un contenu de coin supplémentaire.
Plus
Titre de la carte
Contenu de la carte
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor accessidunt ut labore et dolore magna aliqua. Ut enim ad minim veniam   quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consecuat.
Pas de frontière
Une carte sans bordure sur fond gris.
Carte grille
Les cartes coopèrent généralement avec la disposition de la grille dans la page daperçu.
Chargement de la carte
Affiche un indicateur de chargement pendant que le contenu de la carte est récupéré.
Quel que soit le contenu
Contenu personnalisé
Affiche un indicateur de chargement pendant que le contenu de la carte est récupéré.
Europe Street beat
www.instagram.com
Beuverie
Carrousel vertical
Pagination verticale. utilisez   vertical = vrai
Carrousel de base
Utilisation de base
Fade In Transition
Les diapositives utilisent le fondu pour la transition.   effet = fade
Défilement automatique
Temporisation du défilement vers la prochaine carte    image. lecture automatique
Effondrer
Plus dun panneau peut être développé à la fois   le premier panneau est initialisé pour être actif dans ce cas. utilisez   defaultActiveKey =   [keyNum]
Un chien est un type danimal domestiqué. Connu pour sa loyauté et sa fidélité   il peut être considéré comme un invité de bienvenue dans de nombreux ménages à travers le monde.
Il sagit de len-tête de panneau 1
Il sagit de len-tête de panneau 2
Ceci est len-tête de panneau 3
Cest un panneau de nid décran
Exemple niché
Leffondrement est imbriqué à lintérieur de leffondrement.
Exemple sans bordure
Un style sans marge de Collapse. utiliser   bordered =   false
Accordéon
Mode accordéon   un seul panneau peut être étendu à la fois. Le premier panneau sera étendu par défaut. utiliser laccordéon
Popover
Exemple de base
Lexemple le plus bas. La taille de la couche flottante dépend de la région du contenu.
Passez moi sur moi
Titre
Trois façons de déclencher
Souris pour cliquer   se concentrer et se déplacer.
Concentrez-vous sur moi
Cliquez-moi
Placement
Il y a 12 options de placement disponibles.
Haut
En haut à gauche
En haut à droite
En haut à gauche
La gauche
En bas à gauche
Haut droit
Droite
Bas
En bas à gauche
En bas à droite
Contrôle de la fin de la boîte de dialogue
Utilisez laccessoire visible pour contrôler laffichage de la carte.
TR
TL
LT
KG
RT
RB
BL
BR
Fermer
Info-bulle
Contenu de linfo-bulle
Exemple de base
Lutilisation la plus simple.
Placement
Linfo-bulle dispose de 12 choix de placement.
TL
TR
LT
KG
RT
RB
BL
BR
Bas
Droite
La gauche
Haut
Tooltip saffiche lorsque la souris entre.
Contenu de linfo-bulle
Mots clés
Exemple de base
Utilisation de la balise de base   et elle pourrait être fermée par une propriété fermée. Le tag Fermer permet de fermer après avoir fermé les événements.
Tag 1
Tag 2
Lien
Prévenir les défauts
Tag coloré
Tags chaudes
Sélectionnez vos sujets favoris.
Hots
Ajouter et supprimer dynamiquement
En générant un ensemble de tags par tableau   vous pouvez ajouter et supprimer dynamiquement. Cest basé sur lévénement afterClose   qui sera déclenché pendant la fin de lanimation close.
+ Nouveau Tag
Chronologie
Exemple de base
Chronologie de base
Dernier nœud
Lorsque la chronologie est incomplète et continue   placez enfin un nœud fantôme. set   pending =   true     ou   pending =   a React Element
Voir plus
Douane
Définissez un nœud comme icône ou autre élément personnalisé.
Exemple de couleur
Définissez la couleur des cercles. vert signifie état complet ou réussi   rouge signifie avertissement ou erreur   et bleu signifie état continu ou autre état par défaut.
Créer un site de services 2015-09-01
Résoudre les problèmes de réseau initial 2015-09-01
Problèmes de réseau résolus 2015-09-01
Test technique 2015-09-01
Menu déroulant
Passer le temps
Passez moi sur moi
Déplacer le placement de positionnement
Passer le curseur vers le bas avec le lien Désactiver
Cliqué dans la liste déroulante
Bouton avec menu déroulant
Pagination
De base
Plus
Changer
Sauteur
Mini Taille
Mode simple
Contrôlée
Nombre total
Évaluation
Exemple de base
Lutilisation la plus simple.
Demi-étoile
Supporte la demi-étoile sélectionnée.
Afficher la rédaction
Ajouter une rédaction sur les composants de tarification.
Lecture seulement
En lecture seule   ne peut pas utiliser la souris pour interagir.
Autre caractère
Remplacez létoile par défaut par un autre caractère comme lalphabet   le chiffre   licône ou même le mot chinois.
Arbre
Exemple de base
Lutilisation la plus élémentaire   vous explique comment utiliser checkable   selectable   disabled   defaultExpandKeys   etc.
Exemple de base contrôlé
exemple de base contrôlé
Exemple de draggable
Faites glisser treeNode pour insérer après lautre arbreNode ou insérez dans lautre TreeNode parent.
Charger les données de manière asynchrone
Pour charger des données de façon asynchrone lorsque vous cliquez pour développer un arbreNode.
Exemple de recherche
Arbre recherché
Arbre avec ligne
Navires Netscape 2.0   présentant Javascript
Jesse James Garrett publie des spécifications AJAX
jQuery 1.0 a publié
Premier soulignement.js commit
Backbone.js devient une chose
Angular 1.0 publié
Réagir est open-source; les développeurs se réjouissent
liste
la grille
Ascendant
Descendant
Shuffle
Tourner
Ajouter un item
Retirer lobjet
Recherche de contacts
Ajouter un nouveau contact
Choisissez une couleur pour votre note
Ajouter une nouvelle note
404
On dirait que vous vous êtes perdu
La page que vous recherchez nexiste pas ou a été déplacée.
RETOUR À LA MAISON
500
Erreur Interne du Serveur
Quelque chose a mal tourné. Veuillez réessayer la lettre.
RETOUR À LA MAISON
Isomorphique
Mot de passe oublié?
Entrez votre email et nous vous envoyons un lien de réinitialisation.
Envoyer une demande
Isomorphique
réinitialiser le mot de passe
Entrez un nouveau mot de passe et confirmez-le.
sauvegarder
Isomorphique
Souviens-toi de moi
se connecter
nom dutilisateur  démo   mot de passe  démodemo   ou cliquez simplement sur nimporte quel bouton.
Connectez-vous avec Facebook
Connectez-vous avec Google Plus
Connectez-vous avec Auth0
Mot de passe oublié
Créer un compte isomorphoïque
Isomorphique
Je suis daccord avec les termes et conditions
Sinscrire
Inscrivez-vous avec Facebook
Inscrivez-vous avec Google Plus
Inscrivez-vous avec Auth0
Vous avez déjà un compte? Se connecter.
le revenu
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
Commercialisation
Addvertisement
Consultant
Développement
210
Courrier électronique non-lu
1749
Téléchargement de limage
3024
Message total
54
Poste des commandes
le revenu
15 000 $
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
le revenu
15 000 $
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
le revenu
15 000 $
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
le revenu
15 000 $
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor
110
Nouveaux messages
100%
Le volume
137
Réussite
Télécharger
50% complet
Soutien
80% de client satisfait
Télécharger
65% complet
Jhon Doe
Développeur senior iOS
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor ammet dolar consectetur adipisicing elit
Prénom
Nom de famille
Nom de la compagnie
Adresse e-mail
Mobile No
Pays
Ville
Adresse
Appartement   suite   unité   etc. (facultatif
Créer un compte?
Image
Prénom
Nom de famille
Ville
rue
Email
DOB
Carte de base
Carte de base (avec marqueur par défaut
Carte de base (avec marqueur personnalisé dicônes
Carte de base (avec marqueur personnalisé Html
Carte de base (avec le cluster de marqueur
Routage de carte de base
Aucun contact trouvé
ENVOYER
ANNULER
COMPOSER
Sélectionnez un mail pour lire
PURCHAGE MAINTENANT
PARAMÈTRES
Sélectionner
Frappe Charts
Aidez-moi
Connectez - Out
Voir tout
Voir le panier
Prix ​​total
`;
export default txt;
