const txt = `
Email
Ecommerce
tienda
Carro
revisa
Divertido Tarjetas
Mapas
Mapa de Google
Mapa del folleto
Calendario
Notas
Todos
Contactos
Barajar
Gráficos
Google Carts
Recharts
Reaccionar Vis
React-Chart-2
Reaccionar
Echart
Formularios
Entrada
Editor
Formularios con validación
Progreso
Botón
Lengüeta
Caja
Radiobox
Transferir
Autocompletar
Opciones de Caja
Elementos de la interfaz de usuario
Distintivo
Tarjeta
Parranda
Colapso
Acercarse
Tooltip
Etiqueta
Cronograma
Desplegable
Paginación
Clasificación
Árbol
Elementos avanzados
Reaccionar fechas
Código Espejo
Uppy Uploader
Zona de descenso
Realimentación
Alerta
Modal
Mensaje
Notificación
Pop confirmar
Girar
Mesas
Tablas de hormigas
Páginas
500
404
Registrarse
Regístrate
Olvidé contraseñas
Restablecer contraseñas
Factura
Niveles de menú
Artículo 1
Artículo 2
Opción 1
opcion 2
Opción 3
Opción 4
Página en blanco
Github Buscar
Búsqueda de Youtube
Cambiar idioma
Selector de temas
Barra lateral
Barra superior
Fondo
Título Básico
Texto de éxito
Texto de la información
Texto de advertencia
Texto de error
Tipo de Alerta Closable
Tipo de alerta de icono
Tipo de Alerta
consejos de éxito
Descripción detallada y consejos sobre copywriting exitoso.
Notas informativas
Descripción adicional e informaciones sobre copywriting.
Advertencia
Este es un aviso de advertencia sobre copywriting.
Error
Este es un mensaje de error acerca de copywriting.
Modal uno con personalizar Footer
Diálogo modal básico.
Éxito
Información
Error
Advertencia
Modal
Cuadro de diálogo modal de confirmación
Diálogo modal simple
Mensaje
Mensaje normal
Mensajes normales como retroalimentación.
Mostrar mensaje normal
Otros tipos de mensaje
Mensajes de éxito   error y tipos de advertencia.
Personalizar duración
ustomize la duración de la exhibición del mensaje de 1.5s a 10s por defecto.
Duración de la pantalla personalizada
Mensaje de carga
Mostrar un indicador de carga global   que se descarta por sí mismo de forma asíncrona.
Mostrar un indicador de carga
Notificación
BASIC
El uso más simple que cierre la caja de notificación después de 4.5s.
Abrir el cuadro de notificación
Duración después de la cual se cierra el cuadro de notificación
La duración se puede utilizar para especificar cuánto tiempo permanece abierta la notificación. Una vez transcurrido el tiempo de duración   la notificación se cierra automáticamente. Si no se especifica   el valor predeterminado es 4  5 segundos. Si establece el valor en 0   el cuadro de notificación nunca se cerrará automáticamente.
Notificación con icono
Un cuadro de notificación con un icono en el lado izquierdo.
Notificación con icono personalizado
Mensajes normales como retroalimentación.
Notificación con botón personalizado
Mensajes normales como retroalimentación.
Pop confirmar
Confirmación básica
El ejemplo básico.
Borrar
Notificación con icono personalizado
Mensajes normales como retroalimentación.
TL
Parte superior
TR
LT
Izquierda
LB
RT
Derecha
RB
licenciado en Derecho
Fondo
BR
Girar
Girar el tamaño
Girar con fondo
Descripción de Spin With Background
Estado de carga
Título del mensaje de alerta
Más detalles sobre el contexto de esta alerta.
Entrada
Uso básico
Ejemplo de uso básico.
Tres tamaños de entrada
Hay tres tamaños de un cuadro de entrada  grande (42px     predeterminado (35px   y pequeño (30px  . Nota  Dentro de los formularios   sólo se utiliza el tamaño grande.
Grupo de entrada
Ejemplo de Input.Group Nota  No necesita Col para controlar el ancho en el modo compacto.
Autosizing la altura para ajustar el contenido
prop de autosize para un tipo de entrada textarea hace que la altura se ajuste automáticamente en función del contenido. Se puede proporcionar un objeto de opciones al tamaño automático para especificar el número mínimo y máximo de líneas que la zona de texto ajustará automáticamente.
Pestaña Pre    Post
El uso de pre & amp; post tabs ejemplo ..
Área de texto
Para casos de entrada de usuario multi-línea   se puede utilizar una entrada cuyo tipo prop tiene el valor de textarea.
Buscar
Ejemplo de creación de un cuadro de búsqueda agrupando una entrada estándar con un botón de búsqueda
Editor
Formulario de validación personalizado
Fallar
Debe ser la combinación de números & amp; alfabetos
Advertencia
Validando
La información está siendo validada ...
Éxito
Advertencia
Fallar
Debe ser la combinación de números & amp; alfabetos
Barra de progreso
Barra de progreso
Una barra de progreso estándar.
Barra de progreso circular
Una barra de progreso circular.
Barra de progreso de tamaño mini
Adecuado para un área estrecha.
Una barra de progreso circular más pequeña.
Barra de progreso circular dinámica
Una barra de progreso dinámica es mejor.
Formato de texto personalizado
Puede personalizar el formato de texto configurando el formato.
Tablero
Un estilo de progreso en el tablero de instrumentos.
Botones
Tipo de botón
Icono de botón
Primario
Defecto
Dañado
Peligro
buscar
buscar
buscar
buscar
Tamaño del botón
Botón desactivado
Botón de carga
Botón múltiple
Grupo de botones
Pestañas
buscar
Pestañas inhabilitadas
Icono de las pestañas
Mini pestañas
Pestañas de acción adicionales
Posición
Posición de las pestañas  izquierda   derecha   arriba o abajo
Fichas de tipo de tarjeta
Agregar y cerrar pestañas
Fichas de tipo vertical
Pestañas básicas
Caja
Casilla de verificación básica
Uso básico de la casilla de verificación.
Grupo de casillas de verificación
Genera un grupo de casillas de verificación de una matriz. Utilizar desactivado para deshabilitar una casilla de verificación.
Grupo de casillas de verificación
Genera un grupo de casillas de verificación de una matriz. Utilizar desactivado para deshabilitar una casilla de verificación.
Radio
Radio básica
El uso más simple. Utilizar desactivado para desactivar una radio.
Grupo de radio vertical
Vertical RadioGroup   con más radios.
Grupo de radio
Un grupo de componentes de radio.
Grupo de radio
Un grupo de componentes de radio.
Transferir
Transferir con un cuadro de búsqueda.
Buscar
Autocompletar
Personalizado
Puede pasar AutoComplete.Option como hijos de Autocompletar   en lugar de utilizar dataSource
Personalizar el componente de entrada
Personalizar el componente de entrada
Distintivo
Ejemplo Básico
Uso más simple. La insignia se ocultará cuando count sea 0   pero podemos usar showZero para mostrarlo.
Cuenta de desbordamiento
OverflowCount se muestra cuando count es mayor que overflowCount. El valor predeterminado de overflowCount es 99.
Estado
Insignia autónoma con estado.
Éxito
Error
Defecto
Tratamiento
Advertencia
Insignia roja
Esto simplemente mostrará una insignia roja   sin un conteo específico.
Enlace algo
Divertido Tarjetas
Tarjeta básica
Una tarjeta básica que contiene un título   contenido y un contenido de esquina adicional.
Más
Título de la tarjeta
Contenido de la tarjeta
Lorem ipsum dolor sit amet   consectetur adipisicing elit   sed do eiusmod tempor incididunt ut labore y dolore magna aliqua. Ut enim ad minim veniam   quis nostrud ejercicio ullamco laboris nisi ut aliquip ex y commodo consequat.
Sin bordes
Una tarjeta sin fronteras sobre un fondo gris.
Tarjeta de red
Las tarjetas suelen cooperar con el diseño de la cuadrícula en la página de vista general.
Carga de la tarjeta
Muestra un indicador de carga mientras se está recuperando el contenido de la tarjeta.
Cualquier contenido
Contenido personalizado
Muestra un indicador de carga mientras se está recuperando el contenido de la tarjeta.
Europa Street beat
www.instagram.com
Parranda
Carrusel vertical
Paginación vertical. use   vertical = true
Carrusel básico
Uso básico
Fade In Transition
Las diapositivas utilizan el fundido para la transición.   effect = fade
Desplazarse automáticamente
Tiempo de desplazamiento a la siguiente tarjeta    imagen. auto reproducción
Colapso
Se puede ampliar más de un panel a la vez   el primer panel se inicializa para estar activo en este caso. use   defaultActiveKey =   [keyNum]
Un perro es un tipo de animal domesticado. Conocido por su lealtad y fidelidad   se puede encontrar como un invitado de bienvenida en muchos hogares de todo el mundo.
Este es el encabezado del panel 1
Se trata de la cabecera del panel 2
Este es el encabezado del panel 3
Éste es panel del nido del panel
Ejemplo anidado
Collapse está anidado dentro del Collapse.
Ejemplo sin márgenes
Un estilo sin fronteras de Collapse. use   bordered =   false
Acordeón
Acordeón   sólo se puede ampliar un panel cada vez. El primer panel se ampliará de forma predeterminada. utilizar acordeón
Popover
Ejemplo Básico
El ejemplo más básico. El tamaño de la capa flotante depende de la región del contenido.
Mírame
Título
Tres maneras de activar
El ratón para hacer clic   enfocar y moverse.
Enfócame
Haz click en mi
Colocación
Hay 12 opciones de colocación disponibles.
Parte superior
Arriba a la izquierda
Parte superior derecha
Parte superior izquierda
Izquierda
Abajo a la izquierda
Justo arriba
Derecha
Fondo
Abajo Izquierda
Abajo a la derecha
Control del cierre del diálogo
Utilice el apoyo visible para controlar la visualización de la tarjeta.
TR
TL
LT
LB
RT
RB
licenciado en Derecho
BR
Cerca
Tooltip
Contenido de información sobre herramientas
Ejemplo Básico
El uso más simple.
Colocación
La herramienta tiene 12 opciones de ubicación.
TL
TR
LT
LB
RT
RB
licenciado en Derecho
BR
Fondo
Derecha
Izquierda
Parte superior
La información sobre herramientas se mostrará cuando se introduzca el ratón.
Contenido de información sobre herramientas
Etiquetas
Ejemplo Básico
Uso de la etiqueta básica   y podría ser cerrable por la propiedad cerrable del sistema. La etiqueta Closable soporta eventos onClose afterClose.
Etiqueta 1
Etiqueta 2
Enlazar
Prevenga el Incumplimiento
Etiqueta colorida
Etiquetas populares
Seleccione sus temas favoritos.
Hots
Agregar y eliminar dinámicamente
Generando un conjunto de etiquetas por matriz   puede agregar y quitar dinámicamente. Se basa en el evento afterClose   que se activará mientras finaliza la animación de cierre.
+ Nueva etiqueta
Cronograma
Ejemplo Básico
Línea de tiempo básica
Ultimo nodo
Cuando la línea de tiempo está incompleta y en curso   poner un nodo fantasma por fin. set   pending =   true     o   pending =   un elemento React
Ver más
Personalizado
Establezca un nodo como un icono u otro elemento personalizado.
Ejemplo de color
Establecer el color de los círculos. verde significa estado completado o de éxito   rojo significa advertencia o error y azul significa estado en curso u otro estado predeterminado.
Crear un sitio de servicios 2015-09-01
Resolver problemas de red iniciales 2015-09-01
Problemas de red resueltos 2015-09-01
Pruebas técnicas 2015-09-01
Desplegable
Desplácese
Mírame
Despliegue de colocación de cola
Desplazamiento con desplegable
Desplegable pulsado
Botón con menú desplegable
Paginación
BASIC
Más
Cambiador
Saltador
Tamaño mini
Modo simple
Revisado
Numero total
Clasificación
Ejemplo Básico
El uso más simple.
Media estrella
Soporte de media estrella.
Mostrar copywriting
Añadir copywriting en los componentes de la tarifa.
Solo lectura
Sólo lectura   no puede utilizar el ratón para interactuar.
Otro Personaje
Reemplace la estrella predeterminada por otro carácter como alfabeto   dígito   iconfonte o incluso palabra china.
Árbol
Ejemplo básico
El uso más básico   te dirá cómo usar checkable   seleccionable   disabled   defaultExpandKeys   y etc.
Ejemplo controlado básico
ejemplo controlado básico
Ejemplo arrastrable
Arrastre treeNode para insertar después del otro treeNode o inserte en el otro TreeNode padre.
Cargar datos asincrónicamente
Para cargar datos asincrónicamente cuando haga clic para expandir un treeNode.
Ejemplo de búsqueda
Árbol de búsqueda
Árbol con línea
Netscape 2.0 se expande   introduciendo Javascript
Jesse James Garrett lanza la especificación AJAX
jQuery 1.0 publicado
Primero underscore.js commit
Backbone.js se convierte en una cosa
Angular 1.0 liberado
Reaccionar es de código abierto; los desarrolladores se regocijan
Lista
Cuadrícula
Ascendente
Descendente
Barajar
Girar
Añadir artículo
Remover el artículo
Buscar contactos
Añadir nuevo contacto
Elige un color para tu nota
Añadir nueva nota
404
Parece que te has perdido
La página que estás buscando no existe o se ha movido.
VOLVER A LA CASA
500
error de servidor interno
Algo salió mal. Por favor   inténtelo de nuevo.
VOLVER A LA CASA
Isomórfico
¿Se te olvidó tu contraseña?
Introduzca su correo electrónico y le enviaremos un enlace de restablecimiento.
Enviar petición
Isomórfico
Restablecer la contraseña
Introduzca una nueva contraseña y confirme.
Salvar
Isomórfico
Recuérdame
Registrarse
nombre de usuario  demo   contraseña  demodemo   o simplemente haga clic en cualquier botón.
Iniciar sesión usando Facebook
Acceder con Google Plus
Iniciar sesión con Auth0
Se te olvidó tu contraseña
Crear una cuenta Isomorphoic
Isomórfico
Estoy de acuerdo con los términos y condiciones
Regístrate
Registrate con Facebook
Regístrese con Google Plus
Regístrese con Auth0
¿Ya tienes una cuenta? Registrarse.
Ingresos
Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor
Márketing
Addvertisement
Consultante
Desarrollo
210
Correo electrónico no leído
1749
Subida de imagen
3024
Total de mensajes
54
Pedidos
Ingresos
15000 $
Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor
Ingresos
15000 $
Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor
Ingresos
15000 $
Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor
Ingresos
15000 $
Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed hacer eiusmod tempor
110
Nuevos mensajes
100%
Volumen
137
Logro
Descargar
50% Completo
Apoyo
80% de clientes satisfechos
Subir
65% Completo
Jhon Doe
Sr. Desarrollador iOS
Lorem ipsum dolor sentarse amet   consectetur adipisicing elit   sed eiusmod tempor ammet dolar consectetur adipisicing elit
Nombre de pila
Apellido
nombre de empresa
Dirección de correo electrónico
No móviles
País
Ciudad
Dirección
Apartamento   suite   unidad   etc. (opcional
¿Crea una cuenta?
Imagen
Nombre de pila
Apellido
Ciudad
Calle
Email
DOB
Mapa básico
Mapa básico (con marcador predeterminado
Mapa básico (con marcador de icono personalizado
Mapa básico (con marcador HTML personalizado
Mapa básico (con grupo de marcadores
Enrutamiento básico del mapa
No se ha encontrado ningún contacto
ENVIAR
CANCELAR
COMPONER
Por favor seleccione un correo para leer
Compra ahora
AJUSTES
Seleccionar
Frappe Charts
Ayuda
Cerrar sesión
Ver todo
Ver carro
Precio total
`;
export default txt;
