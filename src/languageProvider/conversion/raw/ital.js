const txt = `
E-mail
ecommerce
Negozio
Carrello
check-out
Carte
Mappe
Google Map
Mappa del foglio
Calendario
Gli appunti
Todos
Contatti
rimescolare
Grafici
Google Carts
Recharts
React Vis
React-Chart-2
React-Trend
Echart
Forme
Ingresso
editore
Forme con validazione
Progresso
Pulsante
linguetta
casella di controllo
radiobox
Trasferimento
Completamento automatico
Opzioni casella
Elementi UI
Distintivo
Carta
gozzoviglia
Crollo
Pop Over
tooltip
Etichetta
Sequenza temporale
Cadere in picchiata
paginatura
Valutazione
Albero
Elementi avanzati
Date di reazione
Specchio di codice
Uploader Uppy
Zona di rilascio
Risposta
Mettere in guardia
Modale
Messaggio
Notifica
Pop Conferma
Roteare
tabelle
Ant Table
pagine
500
404
Registrati
Registrazione
Hai dimenticato le password
Azzerare le password
Fattura
Livelli del menu
Voce 1
Articolo 2
opzione 1
Opzione 2
Opzione 3
Opzione 4
Pagina vuota
Ricerca Github
Cambia lingua
Switcher di temi
Sidebar
topbar
sfondo
Titolo di base
Testo di successo
Info Testo
Testo di avviso
Testo di errore
Tipo avvisi chiudibili
Tipo di avviso di icone
Tipo di avviso di informazioni sullicona
suggerimenti di successo
Descrizione dettagliata e consigli su copywriting di successo.
Note informative
Descrizione e informazioni aggiuntive su copywriting.
avvertimento
Questo è un avviso di avviso di copywriting.
Errore
Si tratta di un messaggio di errore relativo a copywriting.
Modale con personalizzazione di piè di pagina
Dialogo modale di base.
Successo
Informazioni
Errore
avvertimento
Modale
Finestra di dialogo Modalità di conferma
Semplice dialogo modale
Messaggio
Messaggio normale
Messaggi normali come feedback.
Visualizza il messaggio normale
Altri tipi di messaggio
Messaggi di tipo di successo   di errore e di avviso.
Personalizza durata
personalizzare la durata della visualizzazione dei messaggi da default da 1.5s a 10s.
Durata del display personalizzata
Messaggio di caricamento
Visualizzare un indicatore globale di caricamento   che viene eliminato in modo sincrono.
Visualizzare un indicatore di caricamento
Notifica
Di base
Lutilizzo più semplice che chiude la casella di notifica dopo 4.5s.
Aprire la casella di notifica
Durata dopo la chiusura della casella di notifica
La durata può essere utilizzata per specificare la durata della notifica rimanere aperta. Dopo la scadenza della durata   la notifica si chiude automaticamente. Se non è specificato   il valore predefinito è di 4  5 secondi. Se si imposta il valore su 0   la casella di notifica non si chiude automaticamente.
Notifica con icona
Una casella di notifica con unicona sul lato sinistro.
Notifica con icona personalizzata
Messaggi normali come feedback.
Notifica con il pulsante personalizzato
Messaggi normali come feedback.
Pop Conferma
Conferma fondamentale
Lesempio di base.
Elimina
Notifica con icona personalizzata
Messaggi normali come feedback.
TL
Superiore
TR
LT
Sinistra
LIBBRE
RT
Destra
RB
BL
Parte inferiore
BR
Roteare
Dimensioni Spin
Spin con lo sfondo
Spin con descrizione di sfondo
Stato di caricamento
Titolo del messaggio di avviso
Ulteriori dettagli sul contesto di questo avviso.
Ingresso
Utilizzo di base
Esempio di utilizzo di base.
Tre formati di ingresso
Sono disponibili tre dimensioni di una casella Input  grande (42px     predefinito (35px   e piccolo (30px  . Nota  Allinterno delle forme viene utilizzata solo la grande dimensione.
Gruppo di input
Esempio di input.Group Nota  Non è necessario Col per controllare la larghezza nella modalità compatta.
Autosizing laltezza per adattarsi al contenuto
autosize prop per un tipo di textarea dellinput rende laltezza regolabile automaticamente in base al contenuto. Può essere fornito un oggetto opzioni per autosizzare per specificare il numero minimo e massimo di righe che larea textarea regolerà automaticamente.
Scheda Pre    Post
Utilizzo di pre & amp; esempi di tabulazioni post ..
Textarea
Per i casi di input utente multi-line   è possibile utilizzare un input il cui tipo prop ha il valore di textarea.
Ricerca
Esempio di creazione di una casella di ricerca raggruppando un input standard con un pulsante di ricerca
editore
Modulo di convalida personalizzata
Fallire
Dovrebbe essere combinazione di numeri & amp; alfabeti
avvertimento
Convalida
Le informazioni vengono convalidate ...
Successo
avvertimento
Fallire
Dovrebbe essere combinazione di numeri & amp; alfabeti
Barra di avanzamento
Barra di avanzamento
Una barra di avanzamento standard.
Barra di progressione circolare
Una barra di avanzamento circolare.
Barra di avanzamento di taglia minima
Adatto per una zona stretta.
Una barra di avanzamento circolare più piccola.
Barra di avanzamento circolare dinamica
Una barra dinamica di avanzamento è migliore.
Formato di testo personalizzato
È possibile formattare il testo personalizzato impostando il formato.
Cruscotto
Uno stile del cruscotto del progresso.
pulsanti
Tipo di pulsante
Icona pulsante
Primario
Predefinito
tratteggiata
Pericolo
ricerca
ricerca
ricerca
ricerca
Dimensioni del pulsante
Pulsante disabilitato
Caricamento del tasto
Pulsante multiplo
Gruppo di pulsanti
Tabs
ricerca
Schede disattivate
Tabulazioni delle icone
Mini schede
Schede Azione Extra
Posizione
Posizione delle schede  sinistra   destra   superiore o inferiore
Schede del tipo di scheda
Aggiungi e chiudi le schede
Schede di tipo verticale
Schede di base
casella di controllo
Casella di controllo di base
Utilizzo di base della casella di controllo.
Gruppo di casella di controllo
Generare un gruppo di caselle di controllo da un array. Utilizza disabilitato per disattivare una casella di controllo.
Gruppo di casella di controllo
Generare un gruppo di caselle di controllo da un array. Utilizza disabilitato per disattivare una casella di controllo.
Radio
Radio di base
Luso più semplice. Usare disabilitato per disattivare una radio.
RadioGroup verticale
RadioGroup verticale   con più radio.
RadioGroup
Un gruppo di componenti radio.
RadioGroup
Un gruppo di componenti radio.
Trasferimento
Trasferisci con una casella di ricerca.
Ricerca
Completamento automatico
su misura
Potresti passare AutoComplete.Option come bambini di AutoComplete   invece di utilizzare dataSource
Personalizza componente di input
Personalizza componente di input
Distintivo
Esempio di base
Uso più semplice. Il distintivo sarà nascosto quando il conteggio è 0   ma possiamo usare showZero per mostrarlo.
Numero di overflow
OverflowCount viene visualizzato quando il conteggio è maggiore di overflowCount. Il valore predefinito di overflowCount è 99.
Stato
Distintivo autonomo con stato.
Successo
Errore
Predefinito
lavorazione
avvertimento
Distintivo rosso
Questo mostrerà semplicemente un distintivo rosso   senza un conteggio specifico.
Collegare qualcosa
Carte
Scheda di base
Una scheda di base contenente un titolo   un contenuto e un contenuto aggiuntivo dangolo.
Di Più
Titolo della carta
Contenuto della scheda
Il peso del peso è ridotto   lelit di adipisizione del consectetur   che rende meno efficace il lavoro e la dolce magna aliqua. Lut enim ad minim veniam   quis nostrud esercizio ullamco laboris nisi ut aliquip ex ea commodo consequat.
Nessun bordo
Una carta senza bordi su uno sfondo grigio.
Scheda di griglia
Carte di solito cooperano con il layout della griglia nella pagina di panoramica.
Caricamento della carta
Mostra un indicatore di caricamento durante il recupero del contenuto della scheda.
Qualunque contenuto
Contenuto personalizzato
Mostra un indicatore di caricamento durante il recupero del contenuto della scheda.
Europa Street beat
www.instagram.com
gozzoviglia
Carosello verticale
Pagination verticale. utilizzare   vertical = true
Carosello di base
Utilizzo di base
Fade in transizione
Le diapositive utilizzano dissolvenza per la transizione.   Effetto = dissolvenza
Scorri automaticamente
Timing di scorrimento alla scheda    immagine successiva. riproduzione automatica
Crollo
Più di un pannello può essere espanso alla volta   il primo pannello viene inizializzato per essere attivo in questo caso. utilizzare   defaultActiveKey =   [keyNum]
Un cane è un tipo di animale domestico. Conosciuto per la sua fedeltà e fedeltà   si può trovare come un ospite benvenuto in molte famiglie in tutto il mondo.
Questa è lintestazione del pannello 1
Questa è lintestazione del pannello 2
Questa è lintestazione del pannello 3
Questo è il pannello nido del pannello
Esempio nidificato
Il crollo è nidificato allinterno del Collapse.
Esempio senza bordi
Uno stile senza bordo di Collapse. utilizzare   bordered =   false
Fisarmonica
Modalità fisarmonica   è possibile espandere un solo pannello alla volta. Il primo pannello verrà espanso per impostazione predefinita. utilizzare la fisarmonica
popover
Esempio di base
Lesempio più semplice. La dimensione dello strato galleggiante dipende dalla regione dei contenuti.
Allontanami
Titolo
Tre modi per attivare
Mouse per fare clic   concentrarsi e muoversi.
Mi concentri
Cliccami
Posizionamento
Sono disponibili 12 opzioni di posizionamento.
Superiore
A sinistra in alto
In alto a destra
In alto a sinistra
Sinistra
Sinistra inferiore
Destra destra
Destra
Parte inferiore
In basso a sinistra
In basso a destra
Controllare la chiusura della finestra di dialogo
Utilizzare un supporto visibile per controllare la visualizzazione della scheda.
TR
TL
LT
LIBBRE
RT
RB
BL
BR
Vicino
tooltip
Contenuto del Tooltip
Esempio di base
Luso più semplice.
Posizionamento
La ToolTip ha 12 scelta dei posizionamenti.
TL
TR
LT
LIBBRE
RT
RB
BL
BR
Parte inferiore
Destra
Sinistra
Superiore
Tooltip verrà mostrato quando il mouse entra.
Contenuto del Tooltip
tag
Esempio di base
Utilizzo di Tag di base e potrebbe essere chiuso da una proprietà chiusa configurabile. Tag Closable supporta onClose afterClose eventi.
Tag 1
Tag 2
collegamento
Impedire il default
Tag colorato
Tag Hot
Seleziona i tuoi argomenti preferiti.
Hots
Aggiungi e rimuove in modo dinamico
La generazione di un insieme di tag per array consente di aggiungere e rimuovere in modo dinamico. Il suo è basato sullevento afterClose   che verrà attivato mentre la fine animazione fine.
+ Nuovo tag
Sequenza temporale
Esempio di base
Timeline di base
Ultimo nodo
Quando la timeline è incompleta e in corso   infine   metti un nodo fantasma. impostare   in attesa =   true     o   in attesa =   a React Element
Vedi altro
costume
Imposta un nodo come unicona o un altro elemento personalizzato.
Esempio di colore
Imposta il colore dei cerchi. verde significa stato completato o successo   rosso significa avvertimento o errore   e blu significa stato continuo o altro.
Creare un sito di servizi per il 2015-09-01
Risolvere i problemi di rete iniziali dal 2015-09-01
Problemi di rete risolti 2015-09-01
Test tecnici del 2015-09-01
Cadere in picchiata
Disattiva a discesa
Allontanami
Posizionamento di posizionamento a discesa
Sospendi con il disattivato link
Clicca su Drop Down
Pulsante con menu a discesa
paginatura
Di base
Di Più
Changer
Saltatore
Mini formato
Modalità semplice
Controlled
Numero totale
Valutazione
Esempio di base
Luso più semplice.
Metà stella
Sostenere selezionare la metà della stella.
Mostra copywriting
Aggiungi copywriting in componenti di velocità.
Sola lettura
Leggi solo   non può utilizzare il mouse per interagire.
Altro carattere
Sostituire la stella predefinita in altri caratteri come lalfabeto   la cifra   licona o anche la parola cinese.
Albero
Esempio di base
Lutilizzo più semplice   ti dice come utilizzare controllibili   selezionabili   disattivati   defaultExpandKeys e così via.
Esempio controllato di base
esempio controllato di base
Esempio Draggable
Trascinare alberoNode da inserire dopo laltro alberoNodo o inserire nellaltro albero TreeNode.
Caricare i dati in modo asincrono
Per caricare i dati in modo asincrono quando si fa clic per espandere un alberoNodo.
Esempio esplorabile
Albero ricercabile
Albero Con Linea
Netscape 2.0 viene fornito   introducendo Javascript
Jesse James Garrett rilascia AJAX spec
jQuery 1.0 è stato rilasciato
Prima sottolineare. Commit
Backbone.js diventa una cosa
Angular 1.0 è stato rilasciato
React è aperto; gli sviluppatori si rallegrano
Elenco
Griglia
Ascendente
Discendente
rimescolare
Ruotare
Aggiungi articolo
Rimuovi oggetto
Cerca i contatti
Aggiungi nuovo contatto
Scegli un colore per la tua nota
Aggiungi nuova nota
404
Sembra che ti sei perso
La pagina che stai cercando non esiste o è stata spostata.
RITORNO A CASA
500
Errore interno del server
Qualcosa è andato storto. Riprova la lettera.
RITORNO A CASA
isomorfo
Ha dimenticato la password?
Inserisci la tua email e ti inviamo un collegamento di ripristino.
Invia richiesta
isomorfo
Resetta la password
Inserire una nuova password e confermarla.
Salvare
isomorfo
Ricordati di me
registrati
username  demo   password  demodemo   o basta cliccare su qualsiasi pulsante.
Accedi con Facebook
Accedi con Google Plus
Accedi con Auth0
Ha dimenticato la password
Crea un account Isomorphoic
isomorfo
Sono daccordo con i termini e le condivisioni
Registrazione
Registrati con Facebook
Registrati con Google Plus
Registrati con Auth0
Hai già un account? Registrati.
Reddito
Il suo peso è ridotto   è aumentato   è aumentato
Marketing
Addvertisement
Consulenza
Sviluppo
210
Email non letti
1749
Upload di immagini
3024
Messaggio totale
54
Ordini Post
Reddito
$ 15000
Il suo peso è ridotto   è aumentato   è aumentato
Reddito
$ 15000
Il suo peso è ridotto   è aumentato   è aumentato
Reddito
$ 15000
Il suo peso è ridotto   è aumentato   è aumentato
Reddito
$ 15000
Il suo peso è ridotto   è aumentato   è aumentato
110
Nuovi messaggi
100%
Volume
137
realizzazione
Scaricare
50% completato
Supporto
80% Cliente soddisfatto
Caricare
65% completato
Jhon Doe
Sr. iOS Developer
Il peso è ridotto   il prezzo è basso   il prezzo è basso   il prezzo è basso
Nome di battesimo
Cognome
Nome della ditta
Indirizzo email
Mobile no
Nazione
Città
Indirizzo
Appartamento   suite   unità ecc. (Opzionale
Crea un account?
Immagine
Nome di battesimo
Cognome
Città
strada
E-mail
DOB
Mappa di base
Mappa di base (con il marcatore di default
Mappa di base (con il simbolo personalizzato dellicona
Mappa di base (con il personalizzatore Html Marker
Mappa di base (con cluster di marcatori
Routing della mappa di base
Nessun contatto trovato
INVIARE
ANNULLA
COMPORRE
Seleziona una mail per leggere
PAGARE ORA
IMPOSTAZIONI
Selezionare
Frappe Charts
Aiuto
Disconnettersi
Guarda tutto
Visualizza carrello
Prezzo totale
`;
export default txt;
