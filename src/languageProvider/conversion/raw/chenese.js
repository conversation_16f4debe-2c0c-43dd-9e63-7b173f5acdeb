const txt = `
电子邮件
电子商务
店
大车
查看
牌
地图
谷歌地图
传单地图
日历
笔记
待办事项
往来
拖曳
图表
Google购物车
Recharts
反应可见
反应 - 图2
反应-趋势
Echart
形式
输入
编辑
验证形式
进展
按键
标签
复选框
Radiobox
转让
自动完成
框选项
UI元素
徽章
卡
狂欢游行
坍方
流行
提示
标签
时间线
落下
分页
评分
树
高级元素
反应日期
代码镜
Uppy上传器
拖放区
反馈
警报
语气
信息
通知
流行确认
纺
表
蚂蚁表
网页
500
404
签到
注册
忘记密码
重置密码
发票
菜单级别
项目1
项目2
选项1
选项2
选项3
选项4
空白页
Github搜索
Youtube搜索
改变语言
主题切换器
侧边栏
顶栏
背景
基本标题
成功文本
信息文本
警告文字
错误文本
可关闭警报类型
图标警报类型
图标信息警报类型
成功提示
关于成功写作的详细描述和建议。
信息备注
关于文案的附加说明和信息。
警告
这是关于文案的警告通知。
错误
这是关于文案的错误信息。
具有自定义页脚的模态
基本模态对话框
成功
信息
错误
警告
语气
确认模态对话框
简单的模态对话框
信息
正常讯息
正常消息作为反馈。
显示正常消息
其他类型的消息
消息的成功，错误和警告类型。
自定义持续时间
自定义消息显示持续时间从默认值1.5s到10s。
定制显示持续时间
加载消息
显示一个全局加载指示器，它自身被异步地关闭。
显示装载指示灯
通知
基本
4.5s后关闭通知框的最简单的用法。
打开通知框
通知框关闭之后的时间
持续时间可用于指定通知保持打开的时间。持续时间过后，通知自动关闭。如果未指定，默认值为4.5秒。如果将值设置为0，则通知框将永远不会自动关闭。
通知图标
具有左侧图标的通知框。
通知与自定义图标
正常消息作为反馈。
通知使用自定义按钮
正常消息作为反馈。
流行确认
基本确认
的基本例子。
删除
通知与自定义图标
正常消息作为反馈。
TL
最佳
TR
LT
剩下
磅
RT
对
RB
BL
底部
BR
纺
尺寸旋转
旋转背景
旋转背景描述
装载状态：
提醒消息标题
有关此警报的上下文的更多详细信息。
输入
基本用法
基本使用示例。
三种尺寸的输入
输入框有三种尺寸：大（42像素），默认（35像素）和小（30像素）。注意：在表格内部，只使用大尺寸。
输入组
Input.Group示例注意：您不需要Col来控制紧凑模式下的宽度。
自动调整高度以适应内容
对于textarea类型的输入，autosize prop可以根据内容自动调整高度。可以提供一个选项对象来自动调整，以指定textarea将自动调整的最小和最大行数。
前  后选项卡
使用前＆amp;帖子标签示例..
多行文本
对于多行用户输入案例，可以使用类型prop具有textarea值的输入。
搜索
通过使用搜索按钮对标准输入进行分组来创建搜索框的示例
编辑
定制验证表
失败
应该是数字＆amp;字母
警告
证实
信息正在验证...
成功
警告
失败
应该是数字＆amp;字母
进度条
进度条
标准进度条。
循环进度栏
一个循环进度条。
迷你尺寸进度条
适合狭窄的地区。
一个较小的圆形进度条。
动态循环进度条
动态进度条更好。
自定义文本格式
您可以通过设置格式自定义文本格式。
仪表板
仪表板风格的进步。
纽扣
按钮类型
按钮图标
主
默认
虚线
危险
搜索
搜索
搜索
搜索
按钮大小
按钮禁用
按钮加载
多重按钮
按钮组
标签
搜索
禁用标签
图标标签
迷你标签
额外动作标签
位置
标签的位置：左，右，上或下
卡类型选项卡
添加和关闭选项卡
垂直类型标签
基本标签
复选框
基本复选框
复选框的基本用法
复选框组
从数组生成一组复选框。禁用此功能可禁用复选框。
复选框组
从数组生成一组复选框。禁用此功能可禁用复选框。
无线电
基本电台
最简单的用法禁用禁用广播。
垂直无线电组
垂直无线电组，更多收音机。
RadioGroup中
一组无线电组件。
RadioGroup中
一组无线电组件。
转让
使用搜索框转移。
搜索
自动完成
定制
您可以将AutoComplete.Option作为AutoComplete的子代码，而不是使用dataSource
自定义输入组件
自定义输入组件
徽章
基本例子
最简单的用法当count为0时，徽章将被隐藏，但是我们可以使用showZero来显示。
溢出计数
当count大于overflowCount时，会显示OverflowCount。 overflowCount的默认值为99。
状态
独立徽章与状态。
成功
错误
默认
处理
警告
红色徽章
这只会显示一个红色的徽章，没有特定的数字。
链接一些东西
牌
基本卡
包含标题，内容和额外角落内容的基本卡。
更多
卡标题
卡内容
Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor incididunt ut labore et dolore magna aliqua。 Ut enim ad minim veniam，quis nostrud exerciseitation ullamco laboris nisi ut aliquip ex ea commodo因此。
没有边界
在灰色背景上的无边界的卡。
网格卡
卡通常在概览页面中与网格布局配合使用。
装卡
显示加载指示符，同时正在取出卡的内容。
无论内容
定制内容
显示加载指示符，同时正在取出卡的内容。
欧洲街拍
www.instagram.com
狂欢游行
垂直旋转木马
垂直分页。使用  vertical =true
基本转盘
基本用法
淡入淡出
幻灯片使用淡入淡出。   效果= 淡入
自动滚动
滚动到下一张卡  图片的时间。自动播放
坍方
一次可以扩展多个面板，在这种情况下，第一个面板被初始化为活动状态。使用  defaultActiveKey =   [keyNum]
狗是一种驯养动物。以其忠诚和忠诚而闻名，可以作为世界各地许多家庭的欢迎客人。
这是面板标题1
这是面板标题2
这是面板标题3
这是面板嵌套面板
嵌套示例
折叠嵌套在折叠中。
无边界的例子
无边界风格的折叠。使用  bordered =   false
手风琴
手风琴模式，一次只能扩展一个面板。默认情况下，第一个面板将被扩展。使用手风琴
酥料饼
基本例子
最基本的例子。浮动层的大小取决于内容区域。
悬停我
标题
三种触发方式
鼠标点击，对焦和移动。
聚焦我
点击我
放置
有12个放置选项可用。
最佳
左上角
右上
左上
剩下
左下
右上
对
底部
左下方
右下
控制对话框的关闭
使用可见支柱来控制显卡的显示。
TR
TL
LT
磅
RT
RB
BL
BR
关
提示
工具提示内容
基本例子
最简单的用法
放置
工具提示有12个布局选择。
TL
TR
LT
磅
RT
RB
BL
BR
底部
对
剩下
最佳
鼠标进入时会显示工具提示。
工具提示内容
标签
基本例子
使用基本标签，它可以通过set closable属性来关闭。关闭标签支持onClose afterClose事件。
标签1
标签2
链接
防止默认
多彩标签
热门标签
选择您最喜欢的主题。
热门活动：
动态添加和删除
通过数组生成一组标签，可以动态添加和删除。它基于afterClose事件，这将在关闭动画结束时触发。
+新标签
时间线
基本例子
基本时间表
上一个节点
当时间轴不完整和持续时，最后放一个鬼节点。 set   pending =   true    或  pending =   a React Element
查看更多
习惯
将节点设置为图标或其他自定义元素。
颜色示例
设置圈子的颜色。绿色表示完成或成功状态，红色表示警告或错误，蓝色表示正在进行或其他默认状态。
创建服务网站2015-09-01
解决初始网络问题2015-09-01
网络问题正在解决2015-09-01
技术测试2015-09-01
落下
悬停下拉
悬停我
悬停放置下拉
悬停下拉与禁用链接
点击下拉
按钮与下拉菜单
分页
基本
更多
换
跨接器
迷你尺寸
简单模式
受控
总数
评分
基本例子
最简单的用法
半星
支持选择半星。
显示文案
以速率组件添加文案。
只读
只读，不能使用鼠标进行交互。
其他性格
将默认明星替换为其他字符，如字母，数字，iconfont甚至中文字。
树
基本例子
最基本的用法，告诉你如何使用可检查，可选择，禁用，defaultExpandKeys等。
基本控制示例
基本控制示例
可拖曳的例子
拖动treeNode插入另一个treeNode或插入到另一个父TreeNode中。
异步加载数据
单击以展开treeNode时异步加载数据。
可搜索的例子
可搜索的树
树与线
Netscape 2.0发布，引入Javascript
Jesse James Garrett发布了AJAX规范
jQuery 1.0发布
首先下划线
Backbone.js成为一件事情
角度1.0发布
反应是开源的开发商高兴
名单
格
上升
降序
拖曳
旋转
新增项目
除去项目
搜索联系人
添加新联系人
为您的笔记选择一种颜色
添加新注
404
看起来你已经迷路了
您正在寻找的页面不存在或已被移动。
回家
500
内部服务器错误
出了些问题。请再试一次信。
回家
同构
忘记密码？
输入您的电子邮件，我们向您发送重置链接。
发送请求
同构
重设密码
输入新密码并进行确认。
保存
同构
记住我
签到
用户名：demo，密码：demodemo，或者点击任意按钮。
用Facebook登录
使用Google Plus登录
用Auth0登录
忘记密码
创建一个异构帐户
同构
我同意条款和条件
注册
用Facebook注册
用Google Plus注册
注册Auth0
已经有账户？签到。
收入
Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor
营销
Addvertisement
咨询
发展
210
未读电子邮件
1749
图片上传
3024
总信息
54
订单发布
收入
$ 15000
Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor
收入
$ 15000
Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor
收入
$ 15000
Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor
收入
$ 15000
Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor
110
新消息
100％
卷
137
成就
下载
50％完成
支持
80％满意客户
上传
65％完成
Jhon Doe
高级iOS开发人员
Lorem ipsum dolor sit amet，consectetur adipisicing elit，sed do eiusmod tempor ammet dolar consectetur adipisicing elit
名字
姓
公司名
电子邮件地址
手机号码
国家
市
地址
公寓，套房，单位等（可选）
创建一个帐户？
图片
名字
姓
市
街
电子邮件
DOB
基本地图
基本地图（带默认标记）
基本地图（使用自定义图标标记）
基本地图（使用自定义Html标记）
基本地图（带标记群集）
基本地图路由
找不到联系
发送
取消
撰写
请选择一个邮件阅读
立即购买
设置
选择
Frappe Charts
帮帮我
登出
查看全部
查看购物车
总价格
`;
export default txt;
