{"name": "mover_inventory", "version": "3.0.0", "private": true, "dependencies": {"@ant-design/plots": "^1.2.5", "@jsreport/browser-client": "^3.1.0", "@jsreport/nodejs-client": "^3.0.0", "@material-ui/core": "^4.11.2", "@material-ui/icons": "^4.11.2", "@react-pdf/renderer": "^2.0.19", "antd": "^3.10.0", "antd-local-icon": "^0.1.3", "axios": "^0.19.0", "classnames": "^2.2.5", "clone": "^2.1.1", "crypto-js": "^4.0.0", "date-fns": "^1.30.1", "dayjs": "^1.11.13", "draft-js": "^0.11.0", "draftjs-to-html": "^0.8.4", "dymojs": "^1.2.0", "file-saver": "^2.0.5", "history": "^4.6.3", "jsreport-browser-client-dist": "^1.3.0", "jsreport-client": "^1.2.1", "jwt-decode": "^2.2.0", "loadash": "^1.0.0", "logrocket": "^3.0.0", "moment": "^2.29.1", "nprogress": "^0.2.0", "papaparse": "^5.4.1", "postcss-inline-rtl": "^0.9.8", "qrcode.react": "^1.0.1", "query-string": "^6.14.1", "react": "^16.8.1", "react-color": "^2.19.3", "react-csv": "^1.1.2", "react-dom": "^16.8.1", "react-draft-wysiwyg": "^1.13.2", "react-dymo-hooks": "^2.0.1", "react-helmet": "^6.1.0", "react-intl": "^2.3.0", "react-modal-image": "^2.4.0", "react-number-format": "^4.6.3", "react-pdf": "^5.3.2", "react-phone-input-2": "^2.15.0", "react-placeholder": "^2.0.0", "react-places-autocomplete": "^7.3.0", "react-redux": "^5.0.4", "react-router": "^4.1.1", "react-router-dom": "^4.1.0", "react-router-redux": "^5.0.0-alpha.5", "react-script-tag": "^1.1.2", "react-scripts": "2.0.3", "react-sidebar": "^2.3.2", "react-smooth-scrollbar": "^8.0.6", "react-throttle": "^0.3.0", "react-to-print": "^2.14.12", "react-toastify": "^8.1.0", "react-window-size-listener": "^1.0.10", "redux": "^3.6.0", "redux-devtools": "^3.3.2", "redux-devtools-dock-monitor": "^1.1.1", "redux-devtools-log-monitor": "^1.2.0", "redux-saga": "^0.16.0", "redux-thunk": "^2.2.0", "smooth-scrollbar": "^8.2.5", "styled-components": "^2.2.1", "styled-theme": "^0.3.3", "xml-to-pdf": "^1.4.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test --env=jsdom", "eject": "react-scripts eject"}, "proxy": "http://labelwriter.com/software/dls/sdk/js/dymo.connect.framework.js", "prebuild": "npm run generate-build-meta", "generate-build-meta": "./node_modules/react-clear-cache/bin/cli.js", "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"]}